import { CronType } from './cron-type.enum';

export function getCronType(origCron: string, cronFlavor: string) {
    let cronType: CronType;

   const isCronFlavorStandard = () => cronFlavor === 'standard';
    let cron: string = origCron;
    if (cron.split(' ').length === 5 && isCronFlavorStandard()) {
        cron = `0 ${cron} *`;
    }

    if (cron.match(/\d+ 0\/\d+ \* 1\/1 \* [\?\*] \*/)) {
        cronType = CronType.Minutes;
    } else if (cron.match(/\d+ \d+ 0\/\d+ 1\/1 \* [\?\*] \*/)) {
        cronType = CronType.Hourly;
    } else if (cron.match(/\d+ \d+ \d+ \d+\/\d+ \* [\?\*] \*/)) {
        cronType = CronType.Daily_EveryDays;
    } else if (cron.match(/\d+ \d+ \d+ [\?\*] \* MON-FRI \*/)) {
        cronType = CronType.Daily_EveryWeekDay;   
    }
    else if (cron.match(/\d+ \d+ \d+ (\d+|L|LW|1W) \d+\/\d+ [\?\*] \*/)) {
        cronType = CronType.Monthly_SpecificDay;
    } else if (cron.match(/\d+ \d+ \d+ [\?\*] \d+\/\d+ (MON|TUE|WED|THU|FRI|SAT|SUN)((#[1-5])|L) \*/)) {
        cronType = CronType.Monthly_SpecificWeekDay;
    } else if (cron.match(/\d+ \d+ \d+ (\d+|L|LW|1W) \d+ [\?\*] \*/)) {
        cronType = CronType.Yearly_SpecificMonthDay;
    } else if (cron.match(/\d+ \d+ \d+ [\?\*] \d+ (MON|TUE|WED|THU|FRI|SAT|SUN)((#[1-5])|L) \*/)) {
        cronType = CronType.Yearly_SpecificMonthWeek;
    } else if (cron.match(/\d+ \d+ \d+ [\?\*]|\*\/\d \* (MON|TUE|WED|THU|FRI|SAT|SUN)(,(MON|TUE|WED|THU|FRI|SAT|SUN))* \*/)) {
        cronType = CronType.Weekly;
    } else {
        cronType = CronType.Advanced;
    }

    return cronType;
}

export function parseCron(origCron: string, cronType: CronType, use24HourTime: boolean, cronFlavor: string) {
    let cron: string = origCron;
    const isCronFlavorStandard = () => cronFlavor === 'standard';
    if (cron.split(' ').length === 5 && isCronFlavorStandard()) {
        cron = `0 ${cron} *`;
    }

    const [seconds, minutes, hours, dayOfMonth, month, dayOfWeek] = cron.split(' ');
    const getAmPmHour = (hour: number) => use24HourTime ? hour : (hour + 11) % 12 + 1;
    const getHourType = (hour: number) => use24HourTime ? undefined : (hour >= 12 ? 'PM' : 'AM');
    const weekdays = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];

    if (cronType === CronType.Minutes) {
        return {
            minutes: {
                minutes: +minutes.substring(2),
                seconds:  +seconds
            }
        }
    } else if (cronType === CronType.Hourly) {
        return {
            hourly: {
                hours: +hours.substring(2),
                minutes: +minutes,
                seconds: +seconds
            }
        }
    } else if (cronType === CronType.Daily_EveryDays) {
        const parsedHours = +hours;
        return {
            daily: {
                everyDays: {
                    days: +dayOfMonth.split("/")[1],
                    hours: getAmPmHour(parsedHours),
                    hourType: getHourType(parsedHours),
                    minutes: +minutes,
                    seconds: +seconds
                }
            }
        }
    } else if (cronType === CronType.Daily_EveryWeekDay) {
        const parsedHours = +hours;
      
        const cronDays = dayOfWeek.split('-');
        const dayFrom = cronDays[0];
        const dayTo = cronDays[1];
        const days: string[] = [];

        let includeDay = false;
        weekdays.forEach(weekday => {
            if (weekday === dayFrom) {
                includeDay = true;
            }
            if (includeDay) {
                days.push(weekday);
            }
            if (weekday === dayTo) {
                includeDay = false;
            }
        })
        return {
            daily: {
                everyWeekDay: {
                    days: days,
                    hours: getAmPmHour(parsedHours),
                    hourType: getHourType(parsedHours),
                    minutes: +minutes,
                    seconds: +seconds
                }
            }
        }
    } else if (cronType === CronType.Weekly) {
        const parsedHours = +hours;
        const days = (dayOfWeek && dayOfWeek !='*') ? dayOfWeek.split(',') : weekdays;
        const interval = dayOfMonth.split('/');
        return {
            weekly: {
                days: days,
                hours: getAmPmHour(parsedHours),
                hourType:  getHourType(parsedHours),
                interval: (interval && interval.length>1) ? +interval[1] : 1,
                minutes: +minutes,
                seconds: +seconds
            }
        }
    } else if (cronType === CronType.Monthly_SpecificDay) {
        const parsedHours = +hours;
        return {
            monthly: {
                specificDay: {
                    day: dayOfMonth,
                    months: +month.split("/")[1],
                    hours: getAmPmHour(parsedHours),
                    hourType: getHourType(parsedHours),
                    minutes: +minutes,
                    seconds: +seconds
                }
            }
        }
    } else if (cronType === CronType.Monthly_SpecificWeekDay) {
        const parsedHours = +hours;
        const day = dayOfWeek.substr(0, 3);
        const monthWeek = dayOfWeek.substr(3);
        return {
            monthly: {
                specificWeekDay: {
                    monthWeek: monthWeek,
                    day: day,
                    months: +month.split("/")[1],
                    hours: getAmPmHour(parsedHours),
                    hourType: getHourType(parsedHours),
                    minutes: +minutes,
                    seconds: +seconds,
                }
            }
        }
    } else if (cronType === CronType.Yearly_SpecificMonthDay) {
        const parsedHours = +hours;
        return {
            yearly: {
                specificMonthDay: {
                    month: +month,
                    day: dayOfMonth,
                    hours: getAmPmHour(parsedHours),
                    hourType: getHourType(parsedHours),
                    minutes: +minutes,
                    seconds: +seconds
                }
            }
        }
    } else if (cronType === CronType.Yearly_SpecificMonthWeek) {
        const parsedHours = +hours;
        const day = dayOfWeek.substr(0, 3);
        const monthWeek = dayOfWeek.substr(3);
        return {
            yearly: {
                specificMonthWeek: {
                    monthWeek: monthWeek,
                    day: day,
                    month: +month,
                    hours: getAmPmHour(parsedHours),
                    hourType: getHourType(parsedHours),
                    minutes: +minutes,
                    seconds: +seconds
                }
            }
        }
    }  else {
        return {
            advanced: {
                expression: origCron
            }
        }
    }

}



