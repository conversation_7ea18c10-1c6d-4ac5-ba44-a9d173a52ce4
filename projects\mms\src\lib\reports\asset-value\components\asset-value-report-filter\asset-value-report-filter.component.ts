import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { Observable, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { Agency } from '../../../../configuration/agencies/agency.model';
import { BaseModel } from '../../../../configuration/tickets/base.model';
import { AgencyStoreActions, AgencyStoreSelectors } from '../../../../root-store';
import { AssetValueReportStoreActions, AssetValueReportStoreSelectors, AssetValueReportStoreState } from '../../../../root-store/asset-value-reports-store';
import { Validators } from '../../../../shared/forms/validators/validators';
import { ValidationMessageComponent } from '../../../../shared/forms/validation-message/validation-message.component';
import { MMSSelectMenuComponent } from '../../../../shared/forms/controls/mms-select-menu/mms-select-menu.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { BlockUIDirective } from '../../../../shared/block-ui/block-ui.directive';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
  selector: 'app-asset-value-report-filter',
  templateUrl: './asset-value-report-filter.component.html',
  styleUrls: ['./asset-value-report-filter.component.scss'],
  standalone: true,
  imports: [
    ValidationMessageComponent,
    MMSSelectMenuComponent,
    MatFormFieldModule,
    BlockUIDirective,
    ReactiveFormsModule,
    MatCheckboxModule
  ]
})
export class AssetValueReportFilterComponent implements OnInit, OnDestroy {
  formGroup: FormGroup = this.formBuilder.group({
    agencyIds: [null, [Validators.required()]],
    inService: [true],
    outOfService: [true]
  });
  isLoading$: Observable<boolean> = this.store$.select(AssetValueReportStoreSelectors.selectIsLoading).pipe(
    map(isLoading => isLoading ?? false)
  );
  selectedFilter$: Observable<any> = this.store$.select(AssetValueReportStoreSelectors.selectSelectedFilter);
  agencyList$: Observable<Agency[]> = this.store$.select(AgencyStoreSelectors.selectAllAgencyItems);
  allAgencyList: BaseModel[] = [];
  defaultAgency$ = this.store$.select(AgencyStoreSelectors.selectAgenciesWithAllOption);
  defaultAgency: any;
  defaultOption = [{ id: '0', name: 'All' }];
  subscriptions: Array<Subscription> = new Array<Subscription>();
  isCallTasks = true;
  agencyIds: any;

  constructor(
    private title: Title,
    private store$: Store<AssetValueReportStoreState.State>,
    private formBuilder: FormBuilder
  ) {
    title.setTitle('Asset Value Report');
  }

  ngOnInit() {
    this.store$.dispatch(new AgencyStoreActions.LoadRequestAction());
    this.defaultAgency$.subscribe(data => {
      this.defaultAgency = data;
      this.allAgencyList = [];
    });

    this.subscriptions.push(this.agencyList$.subscribe(data => {
      this.allAgencyList = [];

      if (data && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
          let project = {
            id: data[i]?.id ?? '',
            name: data[i]?.name ?? ''
          }

          this.allAgencyList.push(project);
        }
      } else {
        this.allAgencyList = this.defaultOption;
      }
    }));

    this.store$.dispatch(new AgencyStoreActions.LoadRequestAction());
  }

  agencyValueMapper(item: any) {
    return { id: item.id };
  }

  agencyCompareFn(c1: any, c2: any) {
    return c1 && c2 && c1.id === c2.id;
  }

  search() {
    if (this.formGroup.valid) {
      let agencyIds = this.formGroup.get('agencyIds')?.value;

      if (agencyIds != null && agencyIds.length === 1 && agencyIds[0].id === '0') {
        this.formGroup.patchValue({
          agencyIds: this.allAgencyList
        });
      }
      this.store$.dispatch(new AssetValueReportStoreActions.SearchRequestAction(this.formGroup.value));
    }
  }

  clear() {
    this.formGroup.reset();
  }

  ngOnDestroy() {
    if (this.subscriptions.length > 0) {
      this.subscriptions.forEach(element => {
        element.unsubscribe();
      });
    }
    this.clear();
  }
}