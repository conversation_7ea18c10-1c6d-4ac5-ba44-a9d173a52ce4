﻿import { Validator, AbstractControl, ValidationErrors, FormControl } from '@angular/forms'

export class MaxValidator implements Validator {

    constructor(private maxValue: number) {
    }

    validate(control: AbstractControl): ValidationErrors | null {
        if (control instanceof FormControl) {
            if (control.value == null || isNaN(parseFloat(control.value)) || parseFloat(control.value) <= this.maxValue) {
                return null;
            }

            return {
                max: {
                    current: control.value,
                    max: this.maxValue
                }
            }
        }
        return null;
    }
}
