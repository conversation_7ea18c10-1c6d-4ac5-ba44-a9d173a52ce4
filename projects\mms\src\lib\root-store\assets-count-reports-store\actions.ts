import { Action } from '@ngrx/store';
import { AssetsCountReportModel } from '../../reports/assets-counts/assets-count-report.model';
import { Region } from '../../configuration/regions/region.model';
import { MasterInventoryTypeModel } from '../../reports/assets-counts/master-inventory-type.model';
import { AssetsCountFilterModel } from '../../reports/assets-counts/assets-count-filter.model';


export enum ActionTypes {
    LOAD_TYPES_REQUEST = '[Assets Count Report] Load Types Request',
    LOAD_TYPES_FAILURE = '[Assets Count Report] Load Types Failure',
    LOAD_TYPES_SUCCESS = '[Assets Count Report] Load Types Success',
    LOAD_REGIONS_REQUEST = '[Assets Count Report] Load Regions Request',
    LOAD_REGIONS_FAILURE = '[Assets Count Report] Load Regions Failure',
    LOAD_REGIONS_SUCCESS = '[Assets Count Report] Load Regions Success',
    SHOW_FILTER = '[Assets Count Report] Show filter',
    RESET_FILTER = '[Assets Count Report] Reset Filter',
    SEARCH_REQUEST = '[Assets Count Report] Search Request',
    SEARCH_SUCCESS = '[Assets Count Report] Search Success',
    SEARCH_FAILURE = '[Assets Count Report] Search Failure',
    NO_ACTION = '[Assets Count Report] No Action',
    IS_LOADING = '[Assets Count Report] Is Loading',
}

export class LoadTypesRequestAction implements Action {
    readonly type = ActionTypes.LOAD_TYPES_REQUEST;
    constructor() {}
}

export class LoadTypesFailureAction implements Action {
    readonly type = ActionTypes.LOAD_TYPES_FAILURE;
    constructor(public payload: { error: string }) { }
}

export class LoadTypesSuccessAction implements Action {
    readonly type = ActionTypes.LOAD_TYPES_SUCCESS;
    constructor(public payload: { inventoryTypes: MasterInventoryTypeModel[] }) { }
}

export class LoadRegionsRequestAction implements Action {
    readonly type = ActionTypes.LOAD_REGIONS_REQUEST;
    constructor() {}
}

export class LoadRegionsFailureAction implements Action {
    readonly type = ActionTypes.LOAD_REGIONS_FAILURE;
    constructor(public payload: { error: string }) { }
}

export class LoadRegionsSuccessAction implements Action {
    readonly type = ActionTypes.LOAD_REGIONS_SUCCESS;
    constructor(public payload: { regions: Region[] }) { }
}

export class ShowFilterAction implements Action {
    readonly type = ActionTypes.SHOW_FILTER;
    constructor() {}
}

export class ResetFilterAction implements Action {
    readonly type = ActionTypes.RESET_FILTER;
    constructor() { }
}

export class SearchRequestAction implements Action {
    readonly type = ActionTypes.SEARCH_REQUEST;
    constructor(public payload: AssetsCountFilterModel) { }
}

export class SearchSuccessAction implements Action {
    readonly type = ActionTypes.SEARCH_SUCCESS;
    constructor(public payload: AssetsCountReportModel[]) { }
}

export class SearchFailureAction implements Action {
    readonly type = ActionTypes.SEARCH_FAILURE;
    constructor(public payload: { error: string }) { }
}

export class CheckIfIsLoading implements Action {
    readonly type = ActionTypes.IS_LOADING;
    constructor() {};
}

export type Actions =
    LoadTypesRequestAction |
    LoadTypesFailureAction |
    LoadTypesSuccessAction |
    LoadRegionsRequestAction |
    LoadRegionsFailureAction |
    LoadRegionsSuccessAction |
    ResetFilterAction |
    SearchRequestAction |
    SearchSuccessAction |
    SearchFailureAction | 
    CheckIfIsLoading;
