import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { select, Store } from '@ngrx/store';
import { of as observableOf, of } from 'rxjs';
import { catchError, map, switchMap, withLatestFrom, exhaustMap, concatMap, tap } from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state'
import * as featureSelectors from './selectors'

import { Router } from '@angular/router';
import { Page } from '../../core/page.service';
import { ShiftsService } from '../../payroll-and-billing/shifts/services/shifts.service';
import { Shift } from '../../payroll-and-billing/shifts/models/shift.model';
import { displayMessages } from '../../core/resources/display-messages';

@Injectable()
export class ShiftStoreEffects {
    constructor(
        private router: Router,
        private shiftService: ShiftsService,
        private actions$: Actions,
        private store$: Store<featureState.State>,
        private page: Page) { }

    loadShiftsRequest$ = createEffect(() => 
        this.actions$.pipe(
            ofType<featureActions.LoadRequestAction>(featureActions.ActionTypes.LOAD_REQUEST),
            switchMap((_: featureActions.LoadRequestAction) => {
                return this.shiftService.getShifts().pipe(
                    switchMap(
                        (items) => {
                            return of(new featureActions.LoadSuccessAction({ items }));
                        }
                    ),
                    catchError(error => of(new featureActions.LoadFailureAction({ error })))
                )
            })
        )
    );

    saveEffect$ = createEffect(() => 
        this.actions$.pipe(
            ofType<featureActions.SaveAction>(
                featureActions.ActionTypes.SAVE_SHIFT
            ),
            withLatestFrom(
                this.store$.pipe(select(featureSelectors.selectedShift)),
            ),
            map(([action, selectedShift]: [featureActions.SaveAction, Shift]) => {
                if (selectedShift == null) {
                    return new featureActions.SaveRequestAction({ item: action.payload.item })
                } else {
                    return new featureActions.UpdateRequestAction({ item: action.payload.item })
                }
            })
        )
    );

    saveRequestEffect$ = createEffect(() => 
        this.actions$.pipe(
            ofType<featureActions.SaveRequestAction>(
                featureActions.ActionTypes.SAVE_REQUEST
            ),
            exhaustMap((action) =>
                this.shiftService
                    .addShift(action.payload.item)
                    .pipe(
                        concatMap((shift) => {
                            return [
                                new featureActions.SaveSuccessAction({
                                    item: shift!
                                }),
                                new featureActions.LoadRequestAction()
                            ]
                        }
                        ),
                        catchError(error =>
                            observableOf(new featureActions.LoadFailureAction({ error }))
                        )
                    )
            )
        )
    );

    saveSuccess$ = createEffect(() => 
        this.actions$.pipe(
            ofType<featureActions.SaveSuccessAction>(
                featureActions.ActionTypes.SAVE_SUCCESS
            ),
            tap(() => {
                this.router.navigate(['/settings/shifts']);
            })
        ),
        { dispatch: false }
    );

    updateRequestEffect$ = createEffect(() => 
        this.actions$.pipe(
            ofType<featureActions.UpdateRequestAction>(
                featureActions.ActionTypes.UPDATE_REQUEST
            ),
            exhaustMap((action) =>
                this.shiftService
                    .updateShift(action.payload.item)
                    .pipe(
                        switchMap(
                            () => [
                                new featureActions.UpdateSuccessAction({ item: { id: action.payload.item.id!, changes: action.payload.item } }),
                                new featureActions.LoadRequestAction()]
                        ),
                        catchError(error =>
                            observableOf(new featureActions.LoadFailureAction({ error }))
                        )
                    )
            )
        )
    );

    confirmDeleteShiftEffect$ = createEffect(() => 
        this.actions$.pipe(
            ofType<featureActions.ConfirmDeleteAction>(
                featureActions.ActionTypes.CONFIRM_DELETE
            ),
            exhaustMap(() =>
                this.page
                    .confirm.show('Are you sure you want to delete this shift?', 'Are you sure?')
                    .pipe(
                        map(
                            (result) => {
                                if (result) {
                                    return new featureActions.DeleteRequestAction();
                                }
                                return new featureActions.NoAction();
                            }
                        )
                    )
            )
        )
    );

    deleteRequestEffect$ = createEffect(() => 
        this.actions$.pipe(
            ofType<featureActions.DeleteRequestAction>(
                featureActions.ActionTypes.DELETE_REQUEST
            ),
            withLatestFrom(
                this.store$.pipe(select(featureSelectors.selectedShift)),
            ),
            switchMap(([_, selectedShift]: [featureActions.DeleteRequestAction, Shift]) =>
                this.shiftService
                    .deleteShift(selectedShift.id!)
                    .pipe(
                        map(
                            () =>
                                new featureActions.DeleteSuccessAction({
                                    itemId: selectedShift.id!
                                })
                        ),
                        catchError(error =>
                            observableOf(new featureActions.LoadFailureAction({ error }))
                        )
                    )
            )
        )
    );

    deleteSuccessEffect$ = createEffect(() => 
        this.actions$.pipe(
            ofType<featureActions.DeleteSuccessAction>(
                featureActions.ActionTypes.DELETE_SUCCESS
            ),
            map(() => {
                this.page.notification.show('Shift deleted');
                return new featureActions.LoadRequestAction();
            })
        )
    );

    addNewEffect$ = createEffect(() => 
        this.actions$.pipe(
            ofType<featureActions.AddNewAction>(
                featureActions.ActionTypes.ADD_NEW
            ),
            tap(() => {
                this.router.navigate(['/settings/shifts']);
            })
        ),
        { dispatch: false }
    );

    canBeDeletedEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.CanBeDeletedAction>(
                featureActions.ActionTypes.CAN_BE_DELETED
            ),
            withLatestFrom(
                this.store$.pipe(select(featureSelectors.selectedShift)),
            ),
            switchMap(([_, selectedShift]: [featureActions.CanBeDeletedAction, Shift]) =>
                this.shiftService
                    .canShiftBeDeleted(selectedShift.id!)
                    .pipe(
                        map(
                            (result) => {
                                if (result) {
                                    return new featureActions.ConfirmDeleteAction();
                                }
                                this.page.notification.show(displayMessages.exceptions.itemIsAssociated('shift'));
                                return new featureActions.NoAction();
                            }
                        ),
                        catchError(error =>
                            observableOf(new featureActions.LoadFailureAction({ error }))
                        )
                    )
            )
        )
    );
}
