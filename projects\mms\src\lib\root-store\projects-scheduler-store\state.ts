import { CalendarView } from 'angular-calendar';
import { ScheduleFilter } from '../../project-scheduler/models/filter.model';
import { Schedule } from '../../project-scheduler/models/schedule.model';
import { ScheduleType } from '../../project-scheduler/enums/schedule-type.enum';
import { Region } from '../../configuration/regions/region.model';
import { Checklist } from '../../configuration/pm-checklist/checklist.model';
import { Project } from '../../configuration/projects/models/project.model';
import { TicketType } from '../../configuration/ticket-types/models/ticket-type.model';
import { LocationInstanceGridModel } from '../../configuration/location-instances/shared/location-instance-grid-element';
import { GridStoreState } from "../shared-store/material-grid-store";
import { ProjectType } from '../../configuration/project-types/models/project-type.model';
import { ProjectCategory } from '../../configuration/projects/enums/project-category.enum';
import { Contract } from '../../payroll-and-billing/contracts/models/contract.model';
import { PMDueScheduleModel } from 'app/dashboard/components/pm-past-due-dashboard/pm-past-due-schedule.model';

const calendarViewValue = localStorage.getItem('calendarView');
function getCalendarViewValue() : CalendarView {
    const key = (calendarViewValue!.charAt(0).toUpperCase() + calendarViewValue!.slice(1)) as keyof typeof CalendarView;
    return CalendarView[key];
}
export interface State {
    main: MainState;
    grid: GridStoreState.State<Schedule>;
    pmPastDueDashboardGrid: GridStoreState.State<Schedule>;
}

export interface MainState {
    isLoading?: boolean;
    error?: any;
    showSidebar?: boolean;
    calendarView: CalendarView;
    schedules: Schedule[];
    allSchedules: Schedule[];
    isSidebarLoading?: boolean;
    selectedFilter: ScheduleFilter | null;
    selectedScheduleType: ScheduleType;
    ticketChecklists: Checklist[],
    ticketRegions: Region[],
    ticketLocations: LocationInstanceGridModel[],
    ticketTypes: TicketType[],
    filteredTicketTypes: TicketType[],
    projects: Project[],
    projectTypes: ProjectType[],
    category: ProjectCategory,
    selectedContract: Contract | null,
    pmPastDueSchedulers: Array<PMDueScheduleModel>
}

export const initialState: MainState = {
    isLoading: false,
    error: null,
    showSidebar: false,
    calendarView: calendarViewValue ? getCalendarViewValue() : CalendarView.Month,
    isSidebarLoading: false,
    selectedFilter: null,
    schedules: [],
    allSchedules: [],
    selectedScheduleType: ScheduleType.RecurringEvent,
    ticketTypes: [],
    filteredTicketTypes: [],
    ticketRegions: [],
    ticketLocations: [],
    ticketChecklists: [],
    projects: [],
    category: ProjectCategory.Undefined,
    projectTypes: [],
    selectedContract: null,
    pmPastDueSchedulers: []
};

export const PROJECTS_SCHEDULER_GRID_ID = 'PROJECTS_SCHEDULER_GRID';
export const PM_PAST_DUE_DASHBOARD_GRID_ID = 'PM_PAST_DUE_DASHBOARD_GRID_ID';
