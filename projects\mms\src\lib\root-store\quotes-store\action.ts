import { Action } from '@ngrx/store';
import { Update } from '@ngrx/entity';

import { Agency } from '../../configuration/agencies/agency.model';
import { Quotes } from '../../configuration/quotes/models/quotes.model';

export enum ActionTypes {
    LOAD_REQUEST = '[Quotes] Load Request',
    LOAD_FAILURE = '[Quotes] Load Failure',
    LOAD_SUCCESS = '[Quotes] Load Success',
    SAVE_REQUEST = '[Quotes] Save Request',
    SAVE_SUCCESS = '[Quotes] Save Success',
    UPDATE_REQUEST = '[Quotes] Update Request',
    UPDATE_SUCCESS = '[Quotes] Update Success',
    DELETE_REQUEST = '[Quotes] Delete Request',
    CONFIRM_DELETE = '[Quotes] Confirm Delete',
    DELETE_SUCCESS = '[Quotes] Delete Success',
    ADD_NEW = '[Quotes] Add New',
    SAVE = '[Quotes] Save',
    ACTIVATE_SEARCH = '[Quotes] Activate Search',
    DEACTIVATE_SEARCH = '[Quotes] Deactivate Search',
    SEARCH_TEXT_CHANGED = '[Quotes] Search Text Changed',
    CONFIRM_DELETE_Quotes = '[Quotes] Confirm Delete Quotes',
    NO_ACTION = '[Quotes] No Action',
    CAN_BE_DELETED = '[Quotes] Can Be Deleted',
    NAVIGATE = '[Quotes] Navigate',
    SET_ORIGINAL_GRID_DATA = '[Quotes] Set Original Grid Data',
    SET_SELECTED_AGENCY = '[Quotes] Set Selected Agency'
}

export class LoadRequestAction implements Action {
    readonly type = ActionTypes.LOAD_REQUEST;
}

export class LoadSuccessAction implements Action {
    readonly type = ActionTypes.LOAD_SUCCESS;
    constructor(public payload: { items: Quotes[] }) { }
}

export class LoadFailureAction implements Action {
    readonly type = ActionTypes.LOAD_FAILURE;
    constructor(public payload: { error: string }) { }
}

export class SaveRequestAction implements Action {
    readonly type = ActionTypes.SAVE_REQUEST;
    constructor(public payload: { item: Quotes }) { }
}

export class SaveSuccessAction implements Action {
    readonly type = ActionTypes.SAVE_SUCCESS;
    constructor(public payload: { item: Quotes }) { }
}

export class UpdateRequestAction implements Action {
    readonly type = ActionTypes.UPDATE_REQUEST;
    constructor(public payload: { item: Quotes }) { }
}

export class UpdateSuccessAction implements Action {
    readonly type = ActionTypes.UPDATE_SUCCESS;
    constructor(public payload: { item: Update<Quotes> }) { }
}

export class DeleteRequestAction implements Action {
    readonly type = ActionTypes.DELETE_REQUEST;
}

export class DeleteSuccessAction implements Action {
    readonly type = ActionTypes.DELETE_SUCCESS;
    constructor(public payload: { itemId: string }) { }
}

export class ConfirmDeleteAction implements Action {
    readonly type = ActionTypes.CONFIRM_DELETE;
}

export class SaveAction implements Action {
    readonly type = ActionTypes.SAVE;
    constructor(public payload: { item: Quotes }) { }
}


export class AddNewAction implements Action {
    readonly type = ActionTypes.ADD_NEW;
}

export class ActivateSearchAction implements Action {
    readonly type = ActionTypes.ACTIVATE_SEARCH;
}

export class DeactivateSearchAction implements Action {
    readonly type = ActionTypes.DEACTIVATE_SEARCH;
}

export class SearchTextChangedAction implements Action {
    readonly type = ActionTypes.SEARCH_TEXT_CHANGED;
    constructor(public payload: string) { }
}

export class ConfirmDeleteQuotesAction implements Action {
    readonly type = ActionTypes.CONFIRM_DELETE_Quotes;
}

export class CanBeDeletedAction implements Action {
    readonly type = ActionTypes.CAN_BE_DELETED;
}

export class NoAction implements Action {
    readonly type = ActionTypes.NO_ACTION;
}

export class NavigateAction implements Action {
    readonly type = ActionTypes.NAVIGATE;
    constructor(public payload: { Quotes: Quotes }) { }
}


export class SetSelectedAgency implements Action {
    readonly type = ActionTypes.SET_SELECTED_AGENCY;
    constructor(public payload: { agency: Agency }) { }
}

export type Actions = ActivateSearchAction |
    DeactivateSearchAction |
    SearchTextChangedAction |
    ConfirmDeleteQuotesAction |
    SaveAction |
    AddNewAction |
    SaveRequestAction |
    SaveSuccessAction |
    UpdateRequestAction |
    UpdateSuccessAction |
    LoadRequestAction |
    LoadSuccessAction |
    DeleteSuccessAction |
    ConfirmDeleteAction |
    DeleteRequestAction |
    LoadFailureAction |
    NoAction |
    CanBeDeletedAction |
    NavigateAction |
    SetSelectedAgency;

