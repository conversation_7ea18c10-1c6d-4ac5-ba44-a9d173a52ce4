import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';

import * as featureActions from './actions';
import * as featureState from './state'

import { of as of } from 'rxjs';
import { switchMap, map, catchError, withLatestFrom, mergeMap } from 'rxjs/operators';

import { UserService } from '../../configuration/shared/user-and-groups/users.service';
import { UserIdentity } from '@econolite/identity-client';
import { TicketStoreActions } from '../ticket-store';
import { AgencyStoreSelectors } from '../agency-store';
import { RouterStoreSelectors } from '../router-store';
import { RouterReducerState } from '@ngrx/router-store';
import { RouterStateUrl } from '../../root-store/router-store/router-state.serializer';
import { TicketTypeCategory } from '../../configuration/ticket-types/enums/ticket-category.enum';

@Injectable()
export class UserStoreEffects {

    constructor(
        private userService: UserService,
        private actions$: Actions,
        private store$: Store<featureState.State>,
        private userIdentity: UserIdentity) { }

    loadUGRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadUGRequestAction>(
            featureActions.ActionTypes.LOAD_UG_REQUEST
        ),
        switchMap(action =>
            this.userService.getUsersAndGroups().pipe(
                map(items => {
                    return new featureActions.LoadUGSuccessAction({ usersAndGroups: items });
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        )
    ));

    loadUsersForTicketRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadUsersForTicketRequestAction>(
            featureActions.ActionTypes.LOAD_TICKET_USERS_REQUEST
        ),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
            this.store$.select(RouterStoreSelectors.getRouterStoreState),
        ),
        switchMap(([action, agency, routerState]) => {
            this.store$.dispatch(new TicketStoreActions.LoadTicketUsersDetailssAction());
            const ticketTypeCategory = action.payload.ticketType?.category;
            if (ticketTypeCategory === undefined || ticketTypeCategory === TicketTypeCategory.Undefined) {
                return of(new featureActions.LoadFailureAction({ error: 'No ticket type category provided' }));
            }

            const agencyId = agency?.id || '';
            return this.userService.getUsersForTicketDropdown(ticketTypeCategory, agencyId).pipe(
                map(items => {
                    if (routerState.state.url.includes('call-tickets/add-ticket') || routerState.state.url.includes('call-tickets/edit-ticket') || routerState.state.url.includes('call-tickets/dispatch-ticket') || routerState.state.url.includes('/dashboard')) {
                        const agencyOfDispatchCall = action.payload.ticketType?.agencyOfDispatchCall;
                        if (!agencyOfDispatchCall || !agencyOfDispatchCall.id || agencyOfDispatchCall.id === "0") {
                            items = [];
                        } else {
                            items = items.filter(x => {
                                const assignedAgency = x.assignedAgencies?.find(item => item.id === agencyOfDispatchCall.id);
                                return assignedAgency?.id === agencyOfDispatchCall.id;
                            });
                        }
                    } else if (routerState.state.url.includes('/tickets/add-ticket') || routerState.state.url.includes('/tickets/edit-ticket/') || routerState.state.url.includes('/tickets/dispatch-ticket') || routerState.state.url.includes('/maintenance/scheduler/schedules/add-schedule') || routerState.state.url.includes('/maintenance/scheduler/schedules') || routerState.state.url.includes('/maintenance/scheduler/schedules/edit-schedule/')) {
                        if (agency?.id) {
                            items = items.filter(x => x.assignedAgencies?.some(a => a.id === agency.id));
                        }
                    }
                    return new featureActions.LoadUsersSuccessAction({ usersAndGroups: items });
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            );
        })
    ));

    loadUsersSuccessActionEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadUsersSuccessAction>(
            featureActions.ActionTypes.LOAD_TICKET_USERS_SUCCESS
        ),
        mergeMap(() => {
            return of(new TicketStoreActions.LoadTicketDetailsSuccessAction())
        })
    ));

    loadUserEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadUserAction>(
            featureActions.ActionTypes.LOAD_USER
        ),
        withLatestFrom(
            this.store$.select(RouterStoreSelectors.getRouterStoreState),
        ),
        switchMap(([action, routerState]) => {
            const userId = action.payload?.userId;
            if (!userId) {
                return of(new featureActions.LoadFailureAction({ error: 'No user ID provided' }));
            }
            return this.userService.getUserByIdFromBase(userId as string).pipe(
                map(data => {                  
                    return new featureActions.LoadUserSuccessAction({ user: data});
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            );
        })
    ));

    loadUserWithRoles$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadUserByMMS>(
            featureActions.ActionTypes.LOAD_USER_BY_MMS
        ),
        withLatestFrom(
            this.store$.select(RouterStoreSelectors.getRouterStoreState)
        ),
        switchMap(([action, router]: [featureActions.LoadUserByMMS, RouterReducerState<RouterStateUrl>]) => {
            const mmsId = action.payload.mmsId;
            if (!mmsId) {
                return of(new featureActions.LoadFailureAction({ error: 'MMS ID is required' }));
            }
            return this.userService.getUserFromIdentity(mmsId).pipe(
                map(user => {
                    return new featureActions.LoadUserByMMSSuccess({ user })
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        }),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
    ));

    LoadDefaultTaskUserRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadDefaultTaskUserRequest>(
            featureActions.ActionTypes.LOAD_DEFAULT_TASK_USER_REQUEST
        ),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
            this.store$.select(RouterStoreSelectors.getRouterStoreState),
        ),
        switchMap(([action, agency, routerState]) => {
            const ticketTypeCategory = action.payload.ticketType?.category;
            if (ticketTypeCategory === undefined || ticketTypeCategory === TicketTypeCategory.Undefined || !agency?.id || !action.payload.userId) {
                return of(new featureActions.LoadFailureAction({ error: 'Missing required parameters' }));
            }
            return this.userService.getDefaultTaskUser(ticketTypeCategory, agency.id, action.payload.userId).pipe(
                map(items => {
                    return new featureActions.LoadDefaultTaskUserSuccess({ usersAndGroups: items });
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            );
        })
    ));

    loadIdentityUsers$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadUsersFromIdentity>(
            featureActions.ActionTypes.LOAD_USERS_FROM_IDENTITY
        ),
        withLatestFrom(
            this.store$.select(RouterStoreSelectors.getRouterStoreState)
        ),
        switchMap(([action]: [featureActions.LoadUsersFromIdentity, RouterReducerState<RouterStateUrl>]) => {
            return this.userService.getUsersFromIdentity().pipe(
                map(users => {
                    return new featureActions.LoadUsersFromIdentitySuccess({ users: users })
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        })
    ));
}
