import { Filter, Invoice } from './models/invoice.model';
import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { PagedResults } from './models/paged-result.model';

@Injectable({
    providedIn: 'root'
})

export class InvoicesService {

    constructor(private httpClient: HttpClient) {
    }

    getInvoices(filter: Filter): Observable<PagedResults<Invoice>> {
        let params = getParams(filter);
        
        return this.httpClient.get<Array<Invoice>>('payrollandbilling/v1/invoice', { params })
            .pipe(map((result: any) => { return result }));
    }    

    createInvoice(invoice: Invoice): Observable<Invoice> {
        return this.httpClient.post('payrollandbilling/v1/invoice', invoice).pipe(map(
            (updatedInvoice: any) => {
              return updatedInvoice.value;
            }
          ));
    } 

    updateInvoice(invoice: Invoice): Observable<Invoice> {
        return this.httpClient.put('payrollandbilling/v1/invoice', invoice).pipe(map(
            (updatedInvoice: any) => {
              return updatedInvoice;
            }
          ));
    }    

    processInvoices(ids : Array<string>): Observable<Invoice[]> {
        return this.httpClient.put<Invoice[]>('payrollandbilling/v1/invoice/process-invoices', ids).pipe(map(
            (result: Array<Invoice>) => {            
              return result;
            }
          ));
    }

    deleteInvoice(id: string): Observable<boolean> {
        return this.httpClient.delete('payrollandbilling/v1/invoice/' + id)
          .pipe(map(() => {
            return true;
          })
        )
      }
}

function getParams(filter: Filter) : HttpParams{
    let params = new HttpParams();

        if (filter) {
            if (filter.fromDate) {
                params = params.set('fromDate', filter.fromDate.toString());
            }
            if (filter.toDate) {                
                params = params.set('toDate', filter.toDate.toString());
            }            
            if (filter.agency) {
                params = params.set(`agency`, filter.agency);
                
            }
            if (filter.showProcessed) {
                params = params.set('showProcessed', filter.showProcessed.toString());
            }
            if (filter.showRevisedDeleted) {
                params = params.set('showRevisedDeleted', filter.showRevisedDeleted.toString());
            }
            if (filter.nextPageToken) {
                params = params.set('nextPageToken', filter.nextPageToken);
            }
            if (filter.paging != null) {
                params = params.set('paging', filter.paging.toString());
            }
            if (filter.showSuperseded) {
                params = params.set('showSuperseded', filter.showSuperseded.toString());
            }
        }
        return params;
}
