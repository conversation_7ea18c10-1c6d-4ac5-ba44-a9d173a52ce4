import { FailureTypesStoreModule } from './failure-types-store/failure-types-store.module';
import { PriorityStoreModule } from './priority-store/priority-store.module';
import { InvoiceStoreModule } from './invoice-store/invoice-store.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ManufacturerStoreModule } from './manufacturer-store/manufacturer-store.module';
import { RouterStateSerializer } from '@ngrx/router-store';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';

import { CustomSerializer } from './router-store/router-state.serializer';
import { LocationInstanceStoreModule } from './location-instance-store/location-instance-store.module';
import { LocationStoreModule } from './location-store/location-store.module';
import { MasterInventoryStoreModule } from './master-inventory-store/master-inventory-store.module';
import { MmsSettingsStoreModule } from './mms-settings-store/mms-settings-store.module';
import { InventoryTypeStoreModule } from './inventory-store';
import { AssetStoreModule } from './asset-store/asset-store.module';
import { PMChecklistItemGroupStoreModule } from './pm-checklist-item-store/pm-checklist-item-store.module';
import { PmChecklistStoreModule } from './pm-checklist-store/pm-checklist-store.module';
import { RegionStoreModule } from './region-store/region-store.module';
import { SchedulerStoreModule } from './scheduler-store/scheduler-store.module';
import { CallerStoreModule } from './callers-store/callers-store.module';
import { WorkorderStoreModule } from './workorders-store/workorders-store.module';
import { TicketStoreModule } from './ticket-store/ticket-store.module';
import { SharedFieldStoreModule } from './shared-field-store';
import { UserStoreModule } from './user-store';
import { PmStatusDetailsStoreModule } from './pm-status-details-store';
import { AssetsCountReportStoreModule } from './assets-count-reports-store';
import { MovedAssetsReportStoreModule } from './moved-assets-store';
import { PmComingDueReportStoreModule } from './pm-coming-due-reports-store';
import { LocationTicketStoreModule } from './location-ticket-store/location-ticket-store.module';
import { MultipleIssuesLocationsReportStoreModule } from './multiple-issues-locations';
import { DispatchHistoryReportStoreModule } from './dispatch-history-report-store';
import { ProjectsStoreModule } from './projects-store/projects-store.module';
import { AgencyStoreModule } from './agency-store';
import { ProjectTypesStoreModule } from './project-types-store/project-types-store.module';
import { TicketTypesStoreModule } from './ticket-types-store';
import { ReasonCodesStoreModule } from './reason-codes-store/reason-codes-store.module';
import { ProjectsSchedulerStoreModule } from './projects-scheduler-store/projects-scheduler-store.module';
import { AlertStoreModule } from './alert-store';
import { OnCallSchedulerStoreModule } from './on-call-scheduler-store/on-call-scheduler-store.module';
import { TagsStoreModule } from './tags-store/tags-store.module';
import { JobCodeStoreModule } from './job-codes-store/job-code-store.module';
import { EmployeeStoreModule } from './employee-store';
import { HolidayStoreModule } from './holiday-store/holiday-store.module';
import { ShiftStoreModule } from './shift-store/shift-store.module';
import { JurisdictionConfigurationStoreModule } from './jurisdiction-configuration-store.ts';
import { JurisdictionStoreModule } from './jurisdiction-store/jurisdiction-store.module';
import { MapStoreModule } from './map-store/map-store.module';
import { ContractStoreModule } from './contract-store';
import { LocationSubtypeStoreModule } from './location-subtype-store';
import { ProjectActivityReportStoreModule } from './project-activity-report-store'
import { PmEfficiencyReportStoreModule } from './pm-efficiency-report-store'
import { TimesheetStoreModule } from './timesheet-store';
import { BillingStoreModule } from './billing-store/billing-store.module';
import { WarehouseStoreModule } from './warehouse-store';
import { PmScheduledReportStoreModule } from './pm-scheduled-report-store';
import { SparePartsReportStoreModule } from './spare-parts-report-store';
import { PerformancePriorityReportStoreModule } from './performance-priority-report-store';
import { UpcomingPmReportStoreModule } from './upcoming-pm-report-store';
import { WoCallReportStoreModule } from './wo-call-report-store';
import { CustomReportStoreModule } from './custom-reports-store';
import { ProjectBudgetingReportStoreModule } from './project-budgeting-report-store/project-budgeting-report-store.module';
import { PastDueRepairsReportStoreModule } from './past-due-repairs-report';
import { AssetValueReportStoreModule } from './asset-value-reports-store';
import { QuotesStoreModule } from './quotes-store';
import { QuotesReportStoreModule } from './quotes-report-store';
import { ReorderedEquipmentStoreModule } from './reordered-equipment-store';
import { LocationsReportStoreModule } from './locations-report-store';
import { OutstandingPmReportStoreModule } from './outstanding-pm-report-store';
import { TaskSelectionStoreModule } from './task-selection-store/task-selection-store.module';

@NgModule({
  imports: [
    CommonModule,
    ManufacturerStoreModule,
    LocationInstanceStoreModule,
    LocationStoreModule,
    MasterInventoryStoreModule,
    MmsSettingsStoreModule,
    InventoryTypeStoreModule,
    AssetStoreModule,
    PMChecklistItemGroupStoreModule,
    PmChecklistStoreModule,
    SchedulerStoreModule,
    CallerStoreModule,
    WorkorderStoreModule,
    TicketStoreModule,
    SharedFieldStoreModule,
    PmStatusDetailsStoreModule,
    AssetsCountReportStoreModule,
    MovedAssetsReportStoreModule,
    PmComingDueReportStoreModule,
    LocationTicketStoreModule,
    MultipleIssuesLocationsReportStoreModule,
    DispatchHistoryReportStoreModule,
    StoreDevtoolsModule.instrument({
      maxAge: 20
    }),
    RegionStoreModule,
    UserStoreModule,
    ProjectsStoreModule,
    AgencyStoreModule,
    ProjectTypesStoreModule,
    TicketTypesStoreModule,
    ReasonCodesStoreModule,
    ProjectsSchedulerStoreModule,
    AlertStoreModule,
    OnCallSchedulerStoreModule,
    TagsStoreModule,
    JobCodeStoreModule,
    EmployeeStoreModule,
    HolidayStoreModule,
    ShiftStoreModule,
    EmployeeStoreModule,
    JurisdictionStoreModule,
    ShiftStoreModule,
    JurisdictionConfigurationStoreModule,
    MapStoreModule,
    ContractStoreModule,
    LocationSubtypeStoreModule,
    ProjectActivityReportStoreModule,
    PmEfficiencyReportStoreModule,
    TimesheetStoreModule,
    BillingStoreModule,
    InvoiceStoreModule,
    WarehouseStoreModule,
    PriorityStoreModule,
    FailureTypesStoreModule,
    PmScheduledReportStoreModule,
    SparePartsReportStoreModule,
    PerformancePriorityReportStoreModule,
    UpcomingPmReportStoreModule,
    WoCallReportStoreModule,
    CustomReportStoreModule,
    ProjectBudgetingReportStoreModule,
    PastDueRepairsReportStoreModule,
    AssetValueReportStoreModule,
    QuotesStoreModule,
    QuotesReportStoreModule,
    ReorderedEquipmentStoreModule,
    LocationsReportStoreModule,
    OutstandingPmReportStoreModule,
    TaskSelectionStoreModule
  ],
  providers: [
    { provide: RouterStateSerializer, useClass: CustomSerializer }
  ],
})
export class RootStoreModule { }
