import { createEntityAdapter, EntityAdapter } from '@ngrx/entity';
import { Region } from '../../configuration/regions/region.model';
import { Position, LayerPickerOpenDiretion } from '@econolite/limestone';
import { LocationInstanceFeatures, MapConfiguration, TicketFeatures } from '../../mms-map/models/map.models';
import { MapLayer } from '../../settings/map-layers/models/map-layer.model';
import { LocationType } from '../../configuration/type-and-field-definitions/locations/locations-manage/location-type.model';
import { getDotIcon, getPmIcon, getTicketIcon, getWorkTicketIcon, MapLocationIconEnum } from '../../settings/map-layers/enums/map-location-icon.enum';
import { Filter } from '../../configuration/tickets/filter/filter.model';


export const featureAdapter: EntityAdapter<Region> = createEntityAdapter<Region>({
    selectId: model => model.id!,
    sortComparer: (a: Region, b: Region): number =>
        a.name!.toString().localeCompare(b.name!.toString())
});

export interface State {
    initialLoad?: boolean;
    isLoading?: boolean;
    error?: any;
    mapConfig: MapConfigState;
    mapData: MapDataState;
    filters: MapFilters;
    selectedLocationId: string;
    selectedLocationOnMap: any;
    selectedLocationForTicket: any;
    mapKey: string;
    googleMapsApiKey: string
}

export interface MapConfigState {
  mapConfigs: MapConfiguration[];
  layerConfigs: MapLayer[];
  icons: any[];
}

export interface MapDataState {
  selectedFilter: Filter;
  locationTypes: LocationType[];
  locationMapInstances: LocationInstanceFeatures[];
  callMapInstances: TicketFeatures[];
  ticketMapInstances: TicketFeatures[];
  pmMapInstances: TicketFeatures[];
}

export interface MapFilters {
  location: MapLocationFilter;
}

export interface MapLocationFilter {
  locationTypes: LocationType[]
}

export const Icons = [
  {id: 'dot', icon: MapLocationIconEnum.dot},
  {id: 'dot_white', icon: getDotIcon('white')},
  {id: 'dot_purple', icon: getDotIcon('purple')},
  {id: 'dot_pink', icon: getDotIcon('pink')},
  {id: 'dot_red', icon: getDotIcon('red')},
  {id: 'dot_yellow', icon: getDotIcon('yellow')},
  {id: 'dot_darkorange', icon: getDotIcon('darkorange')},
  {id: 'dot_green', icon: getDotIcon('green')},
  {id: 'dot_chartreuse', icon: getDotIcon('chartreuse')},
  {id: 'dot_lightgreen', icon: getDotIcon('lightgreen')},
  {id: 'dot_skyblue', icon: getDotIcon('skyblue')},
  {id: 'dot_gray', icon: getDotIcon('gray')},
  {id: 'dot_lightgrey', icon: getDotIcon('lightgrey')},
  {id: 'pm', icon: MapLocationIconEnum.build_pm},
  {id: 'pm_white', icon: getPmIcon('white')},
  {id: 'pm_purple', icon: getPmIcon('purple')},
  {id: 'pm_pink', icon: getPmIcon('pink')},
  {id: 'pm_red', icon: getPmIcon('red')},
  {id: 'pm_yellow', icon: getPmIcon('yellow')},
  {id: 'pm_darkorange', icon: getPmIcon('darkorange')},
  {id: 'pm_green', icon: getPmIcon('green')},
  {id: 'pm_chartreuse', icon: getPmIcon('chartreuse')},
  {id: 'pm_lightgreen', icon: getPmIcon('lightgreen')},
  {id: 'pm_skyblue', icon: getPmIcon('skyblue')},
  {id: 'pm_gray', icon: getPmIcon('gray')},
  {id: 'pm_lightgrey', icon: getPmIcon('lightgray')},
  {id: 'ticket', icon: MapLocationIconEnum.tour},
  {id: 'ticket_white', icon: getTicketIcon('white')},
  {id: 'ticket_purple', icon: getTicketIcon('purple')},
  {id: 'ticket_pink', icon: getTicketIcon('pink')},
  {id: 'ticket_red', icon: getTicketIcon('red')},
  {id: 'ticket_yellow', icon: getTicketIcon('yellow')},
  {id: 'ticket_darkorange', icon: getTicketIcon('darkorange')},
  {id: 'ticket_green', icon: getTicketIcon('green')},
  {id: 'ticket_chartreuse', icon: getTicketIcon('chartreuse')},
  {id: 'ticket_lightgreen', icon: getTicketIcon('lightgreen')},
  {id: 'ticket_skyblue', icon: getTicketIcon('skyblue')},
  {id: 'ticket_gray', icon: getTicketIcon('gray')},
  {id: 'ticket_lightgrey', icon: getTicketIcon('lightgray')},
  {id: 'work', icon: MapLocationIconEnum.build_work},
  {id: 'work_white', icon: getWorkTicketIcon('white')},
  {id: 'work_purple', icon: getWorkTicketIcon('purple')},
  {id: 'work_pink', icon: getWorkTicketIcon('pink')},
  {id: 'work_red', icon: getWorkTicketIcon('red')},
  {id: 'work_yellow', icon: getWorkTicketIcon('yellow')},
  {id: 'work_darkorange', icon: getWorkTicketIcon('darkorange')},
  {id: 'work_green', icon: getWorkTicketIcon('green')},
  {id: 'work_chartreuse', icon: getWorkTicketIcon('chartreuse')},
  {id: 'work_lightgreen', icon: getWorkTicketIcon('lightgreen')},
  {id: 'work_skyblue', icon: getWorkTicketIcon('skyblue')},
  {id: 'work_gray', icon: getWorkTicketIcon('gray')},
  {id: 'work_lightgrey', icon: getWorkTicketIcon('lightgray')},
  {id: 'receipt', icon: MapLocationIconEnum.receipt},
  {id: 'accessibility', icon: MapLocationIconEnum.accessibility},
  {id: 'accessible', icon: MapLocationIconEnum.accessible},
  {id: 'account_box', icon: MapLocationIconEnum.account_box},
  {id: 'settings_input_antenna', icon: MapLocationIconEnum.settings_input_antenna},
  {id: 'assignment', icon: MapLocationIconEnum.assignment},
  {id: 'build', icon: MapLocationIconEnum.build},
  {id: 'alarm_on', icon: MapLocationIconEnum.alarm_on},
  {id: 'settings_input_hdmi', icon: MapLocationIconEnum.settings_input_hdmi},
  {id: 'announcement', icon: MapLocationIconEnum.announcement},
  {id: 'all_out', icon: MapLocationIconEnum.all_out},
  {id: 'bookmarks', icon: MapLocationIconEnum.bookmarks},
  {id: 'settings_input_svideo', icon: MapLocationIconEnum.settings_input_svideo},
  {id: 'cell_wifi', icon: MapLocationIconEnum.cell_wifi},
  {id: 'developer_board', icon: MapLocationIconEnum.developer_board},
  {id: 'cast', icon: MapLocationIconEnum.cast},
  {id: 'network_wifi', icon: MapLocationIconEnum.network_wifi},
  {id: 'videocam', icon: MapLocationIconEnum.videocam},
  {id: 'gps_fixed', icon: MapLocationIconEnum.gps_fixed},
  {id: 'dialer_sip', icon: MapLocationIconEnum.dialer_sip},
  {id: 'duo', icon: MapLocationIconEnum.duo},
  {id: 'comment', icon: MapLocationIconEnum.comment},
  {id: 'location_on', icon: MapLocationIconEnum.location_on},
  {id: 'linear_scale', icon: MapLocationIconEnum.linear_scale},
  {id: 'hearing', icon: MapLocationIconEnum.hearing},
  {id: 'library_books', icon: MapLocationIconEnum.library_books},
  {id: 'album', icon: MapLocationIconEnum.album},
  {id: 'speaker', icon: MapLocationIconEnum.speaker},
  {id: 'touch_app', icon: MapLocationIconEnum.touch_app},
  {id: 'settings_remote', icon: MapLocationIconEnum.settings_remote},
  {id: 'store', icon: MapLocationIconEnum.store},
  {id: 'linked_camera', icon: MapLocationIconEnum.linked_camera},
  {id: 'SVG_Manufacturer', icon: MapLocationIconEnum.SVG_Manufacturer},
  {id: 'SVG_Warehouse', icon: MapLocationIconEnum.SVG_Warehouse},
  {id: 'SVG_Vehicle', icon: MapLocationIconEnum.SVG_Vehicle},
  {id: 'SVG_RepairDepot', icon: MapLocationIconEnum.SVG_RepairDepot},
  {id: 'SVG_Region', icon: MapLocationIconEnum.SVG_Region},
  {id: 'SVG_Scrap', icon: MapLocationIconEnum.SVG_Scrap},
  {id: 'SVG_SigIntersection', icon: MapLocationIconEnum.SVG_SigIntersection},
  {id: 'SVG_FlashBeacon', icon: MapLocationIconEnum.SVG_FlashBeacon},
  {id: 'SVG_DMS', icon: MapLocationIconEnum.SVG_DMS},
  {id: 'SVG_CCTV', icon: MapLocationIconEnum.SVG_CCTV},
  {id: 'SVG_CountStation', icon: MapLocationIconEnum.SVG_CountStation},
  {id: 'SVG_HAR', icon: MapLocationIconEnum.SVG_HAR},
  {id: 'SVG_RampMeter', icon: MapLocationIconEnum.SVG_RampMeter},
  {id: 'SVG_RWIS', icon: MapLocationIconEnum.SVG_RWIS},
  {id: 'SVG_LightedCrosswalk', icon: MapLocationIconEnum.SVG_LightedCrosswalk},
  {id: 'SVG_Route', icon: MapLocationIconEnum.SVG_Route},
]

export const initialMapConfigState = {
  id: 'test',
  name: 'Main Map',
  mapOptions: {
  bearing: 0,
  pitch: 0,
  isFreeMode: false,
  ctrlZoomInteraction: true,
  boxZoomInteraction: true,
  zoom: 4,
  minZoom: 0,
  maxZoom: 20,
  center: Position.fromLatLng(39.98110, -98.25479),
  style: 'blank',
  },
  layerPickerConfig: {
    enabled: false,
    position: {
      bottom: '10px',
      left: '20px'
    },
    openDirection: LayerPickerOpenDiretion.LeftToRight
  }
}

export const initialState: State = {
        initialLoad: false,
        isLoading: false,
        error: null,
        mapConfig: {
          mapConfigs: [],
        layerConfigs: [],
        icons: Icons
        },
        mapData: {
          selectedFilter: {} as Filter,
          locationMapInstances: [],
          locationTypes: [],
          callMapInstances: [],
          ticketMapInstances: [],
          pmMapInstances: [],
        },
        filters: {
          location: {
            locationTypes: []
          }
        },
        selectedLocationId: '',
        selectedLocationOnMap: null,
        selectedLocationForTicket: null,
        mapKey: "",
        googleMapsApiKey:"",
    };
