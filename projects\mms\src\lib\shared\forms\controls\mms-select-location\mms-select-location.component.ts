import { Component, OnInit, Optional, Input, Self, HostBinding, Output, EventEmitter, DoCheck, OnChanges, ElementRef, ChangeDetectorRef, AfterViewInit, OnDestroy, ViewChild } from '@angular/core';
import { MatFormFieldControl } from '@angular/material/form-field';
import { ErrorStateMatcher } from '@angular/material/core';
import { MatFormField, MatFormFieldModule } from '@angular/material/form-field';
import { FormGroupDirective, NgControl, ControlValueAccessor, FormControl, NgForm, ReactiveFormsModule } from '@angular/forms';
import { OptionGroup } from '../mms-select-menu/option-group.model';
import { Store } from '@ngrx/store';
import { LocationInstanceStoreState, LocationInstanceStoreSelectors, AgencyStoreState, AgencyStoreSelectors } from '../../../../root-store';
import { Subject, Observable } from 'rxjs';
import { FocusMonitor } from '@angular/cdk/a11y';
import { AreaTypeEnum } from '../../../../configuration/location-instances/shared/area-type.enum';
import { map, takeUntil } from 'rxjs/operators';
import { Router } from '@angular/router';
import { LoadLocationsAndRegionsRequest } from '../../../../root-store/location-instance-store/actions';
import { BaseModel } from '../../../../configuration/tickets/base.model';
import { ScrollableItems } from '../mms-select-menu/scrollable-items.model';
import { Agency } from '../../../../configuration/agencies/agency.model';
import { MMSSelectMenuComponent } from '../mms-select-menu/mms-select-menu.component';
import { CommonModule } from '@angular/common';
import { LocationStoreState, LocationStoreSelectors, LocationStoreActions } from '../../../../root-store/location-store';
import { LocationType } from '../../../../configuration/type-and-field-definitions/locations/locations-manage/location-type.model';
import { MatIconModule } from '@angular/material/icon';

// Location model interface
export interface Location extends LocationType {
  areaType: AreaTypeEnum;
  agency?: string;
  locationTypeName?: string;
  description?: string;
  latitude?: number;
  longitude?: number;
  locationTypeId?: string;
  billing?: string;
  jurisdiction?: string;
  notes?: string;
  regions?: string[];
  regionIds?: string[];
  agencyId?: string;
}

@Component({
  selector: 'mms-select-location',
  templateUrl: './mms-select-location.component.html',
  styleUrls: ['./mms-select-location.component.scss'],
  providers: [
    {
      provide: MatFormFieldControl,
      useExisting: MmsSelectLocationComponent
    }
  ],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MMSSelectMenuComponent,
    MatFormFieldModule,
    MatIconModule
  ]
})
export class MmsSelectLocationComponent implements OnInit, OnChanges, DoCheck, ControlValueAccessor, AfterViewInit, OnDestroy {

  errorState = false;
  focused = false;
  optionGroups: OptionGroup[] = [];
  ngControl: NgControl;
  stateChanges = new Subject<void>();
  regionIds: string[] = [];
  clearSearchInput = false;
  tempAgencyname = '';
  private _value = '';
  private _disabled = false;
  private _onDestroy = new Subject<void>();
  menuValue = '';
  locationsAndRegions$: Observable<Location[]>;
  locations: Location[] = [];
  @ViewChild(MMSSelectMenuComponent) mmsSelectMenu!: MMSSelectMenuComponent;

  @Output() valueChange = new EventEmitter<any>();

  @Input() formControlName = '';
  @Input() isMultiple = false;
  @Input() selectedAgencyDispatch = '';
  @Input() enableClearSearchInput = false;
  @Input() valueAsOptions = false;
  @Input() errorStateMatcher: ErrorStateMatcher | null = null;
  @Input() showLocationsOnly = false;
  @Input() showConcreteLocationsOnly = false;
  @Input() showVehicleEquipmentLocationsOnly = false;
  @Input() showRegionsOnly = false;
  @Input() filteredLocations: Array<BaseModel> = [];
  @Input() filteredRegions: Array<BaseModel> = [];
  @Input() agencyName = '';

  @HostBinding('attr.aria-describedby') describedBy = '';

  @Input()
  get value(): any {
    return this._value;
  }
  set value(value: any) {
    this._value = value;
    this.onChangeCallback(value);
    this.stateChanges.next();
  }

  @Input()
  get required(): boolean {
      return this._required;
  }
  set required(value: boolean) {
      this._required = !!value;
      this.stateChanges.next();
  }
  private _required = false;

  @Input()
  get disabled(): boolean {
    if (this.ngControl && this.ngControl.disabled !== null) {
      return this.ngControl.disabled;
    }
    return this._disabled;
  }
  set disabled(value: boolean) {
      this._disabled = !!value;
      // Browsers may not fire the blur event if the input is disabled too quickly.
      // Reset from here to ensure that the element doesn't become stuck.
      if (this.focused) {
          this.focused = false;
          this.stateChanges.next();
      }
  }

  @Input()
  get placeholder(): string {
      return this._placeholder;
  }
  set placeholder(value: string) {
      this._placeholder = value;
      this.stateChanges.next();
  }
  private _placeholder = '';

  @HostBinding('class.floating')
  get shouldLabelFloat(): boolean {
      return this.focused || !this.empty;
  }

  get empty(): boolean {
      return this.value == null;
  }
  defaultAgency$ = this.agencyStore$.select(AgencyStoreSelectors.selectDefaultAgency);
  selectedAgencyDispatchCall$ = this.agencyStore$.select(LocationInstanceStoreSelectors.selectedAgencyDispatchCall);

  constructor(
    @Optional() public parentFormGroup: FormGroupDirective,
    @Optional() @Self() ngControl: NgControl,
    @Optional() public parentForm: NgForm,
    private elRef: ElementRef<HTMLElement>,
    private matFormField: MatFormField,
    private fm: FocusMonitor,   
    public defaultErrorStateMatcher: ErrorStateMatcher,    
    private agencyStore$: Store<AgencyStoreState.State>,
    private locationInstanceStore$: Store<LocationInstanceStoreState.State>,
    private route: Router,
    private cdr: ChangeDetectorRef,
    private store$: Store<LocationStoreState.State>
  ) {
    this.ngControl = ngControl;
    // Setting the value accessor directly (instead of using
    // the providers) to avoid running into a circular import.
    if (this.ngControl != null) { this.ngControl.valueAccessor = this; }

    // MatFormFieldControl
    fm.monitor(elRef.nativeElement, true).subscribe(origin => {
      this.focused = !!origin;
      this.stateChanges.next();
    });
    // end MatFormFieldControl
    this.selectedAgencyDispatchCall$.subscribe(data => {
      if (data && (data.name == "" || data.name == "All" && ((this.route.url.includes('call-tickets/add-ticket') || this.route.url.includes('call-tickets/edit-ticket') || this.route.url.includes('/dashboard'))))) {
        this.selectedAgencyDispatch = "";
      }
    });
    this.defaultAgency$.subscribe(data => {
      if (data && !((this.route.url.includes('call-tickets/add-ticket') || this.route.url.includes('call-tickets/edit-ticket') || (this.route.url.includes('/dashboard')) && (this.agencyName == "" || this.selectedAgencyDispatch == "")))) {
        this.locationInstanceStore$.dispatch(new LoadLocationsAndRegionsRequest(true, { id: data.id, name: data.name } as Agency));
      }
    });
    this.locationsAndRegions$ = this.store$.select(LocationStoreSelectors.selectAllLocationItems).pipe(
      map(locations => locations as unknown as Location[])
    );
    this.locationsAndRegions$.pipe(
      takeUntil(this._onDestroy)
    ).subscribe(locations => {
      this.locations = locations;
      this.setupOptionGroups();
    });
  }

  ngOnInit() {
    this.store$.dispatch(new LocationStoreActions.LoadRequestAction());
    this.locationsAndRegions$ = this.locationInstanceStore$.select(LocationInstanceStoreSelectors.selectLocationsAndRegions).pipe(map(data => {
      if (data && data.options && data.options.length > 0) {
        this.filterOptions(data);
        if (this.agencyName && this.agencyName != "All") {
          data.options = data.options.filter((x: Location) => x.areaType === AreaTypeEnum.Location && x.agency === this.agencyName);
        }
        if (this.showConcreteLocationsOnly) {
          data.options = data.options.filter((x: Location) => x.areaType === AreaTypeEnum.Location && 
            x.locationTypeName?.toLowerCase() !== "vehicle" && 
            x.locationTypeName?.toLowerCase() !== 'equipment' && 
            x.locationTypeName?.toLowerCase() !== "warehouse");
        }
        if (this.showLocationsOnly) {
          data.options = data.options.filter((x: Location) => x.areaType === AreaTypeEnum.Location);
          return data;
        } else if (this.showVehicleEquipmentLocationsOnly) {
          data.options = data.options.filter((x: Location) => x.areaType === AreaTypeEnum.Location && 
            (x.locationTypeName?.toLowerCase() === 'vehicle' || x.locationTypeName?.toLowerCase() === 'equipment'));
          const result = data.options.map((option: Location) => ({ 
            name: option.name + " - " + option.locationTypeName + " - " + (option.description ?? " "),
            agencyId: option.agencyId,
            areaType: option.areaType,
            billingcategory: option.billing,
            description: option.description,
            jurisdiction: option.jurisdiction,
            latitudelongitude: option.latitude + " " + option.longitude,
            locationTypeId: option.locationTypeId,
            locationTypeName: option.locationTypeName,
            notes: option.notes,
            regions: option.regions,
            regionsId: option.regionIds,
            id: option.id
          }));
          data.options = result;
          return data;
        } else if (this.showRegionsOnly) {
          const regions = data.options.filter((x: Location) => x.areaType === AreaTypeEnum.Region);
          if (regions && regions.length) {
            this.regionIds = regions.map((x: Location) => x.id);
          }
          return regions;
        }
      }
      return data;
    }))

    if (this.showLocationsOnly || this.showVehicleEquipmentLocationsOnly) {
      this.optionGroups = [
        {
          name: 'Locations',
          icon: 'location_on'
        }
      ]
    }
    else if (this.showRegionsOnly) {
      this.optionGroups = [
        {
          name: 'Regions',
          icon: 'landscape'
        }
      ]
    }
    else {
      this.optionGroups = [
        {
          name: 'Regions',
          icon: 'landscape'
        },
        {
          name: 'Locations',
          icon: 'location_on'
        }
      ];
    }
  }

  ngOnChanges() {
    this.stateChanges.next();
  }

  ngDoCheck() {
    if (this.ngControl) {
        this.updateErrorState();
    }
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  ngAfterViewInit() {
    this.cdr.detectChanges();
  }

  // Add ticket form
  filterOptions(data: ScrollableItems) {
    let options: any[] = [];

    if (this.filteredLocations && this.filteredLocations.length) {
      this.filteredLocations.forEach(x => (x as any).areaType = AreaTypeEnum.Location);
      options = this.filteredLocations;
    } else {
      options = data.options.filter((x: Location) => x.areaType === AreaTypeEnum.Location);
    }

    if (this.filteredRegions && this.filteredRegions.length) {
      this.filteredRegions.forEach(x => (x as any).areaType = AreaTypeEnum.Region);
      if (this.filterRegions(data)) {
        options = options.concat(this.filteredRegions);
      }
    } else {
      options = options.concat(data.options.filter((x: Location) => x.areaType === AreaTypeEnum.Region));
    }

    data.options = options.sort((a: Location, b: Location) => a.name.localeCompare(b.name));
  }
  
  filterRegions(data: ScrollableItems) {
    return data.options.find((x: Location) => x.areaType === AreaTypeEnum.Region) !== undefined;
  }

  valueMapper(option: any) {
    return option;
  }

  textMapper(option: any) {
    return option.name;
  }

  compareFn(c1: any, c2: any) {
    if (this.isMultiple) {
      return c1 && c2 ? c2.findIndex((x: Location) => x.id === c1.id) !== -1 : c1 === c2;
    } else {
      return c1 && c2
        ? c1.id && c2.id
          ? c1.id === c2.id
          : c1.id === c2
        : c1 === c2;
    }
  }

  checkOptionGroup(item: any, optionGroup: string): boolean {    
      if ( item.areaType === 1 && optionGroup === 'Locations'
        || item.areaType === 2 && optionGroup === 'Regions') {
        return true;
      }
      return false;
  }

  onSelectionChange(event: any) {
    if (event && event.length === 0) {
      event = null;
    }
    this.value = event;
  }

  clearValue() {
    this.value = null;
    this.focused = false;
    this.valueChange.emit();
  }

  valueChanged(value: any) {
    this.valueChange.emit(value);
  }

  onOpenedChange(isOpen: boolean) {
    if (this.ngControl?.control && !isOpen) {
      this.ngControl.control.markAsTouched();
      this.updateErrorState();
    }
  }

  click(value: any) {
    if (this.route.url.includes('call-tickets/add-ticket') || this.route.url.includes('call-tickets/edit-ticket')) {
      this.tempAgencyname = this.agencyName === '' ? "All" : this.agencyName;
    } else {
      const hasLocations = this.locations && this.locations.length > 0;
      if (!hasLocations) {
        this.locationInstanceStore$.dispatch(new LoadLocationsAndRegionsRequest(true));
      }
    }
  }
  
  // ControlValueAccessor
  onChangeCallback: (value: any | any[]) => void = () => { };
  onTouchedCallback: () => void = () => { };

  writeValue(val: any): void {
    if (val !== this._value) {
      this._value = val;
      this.stateChanges.next();
    }
  }

  registerOnChange(fn: any): void {
    this.onChangeCallback = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouchedCallback = fn;
  }
  // end ControlValueAccessor

  // MatFormFieldControl
  setDescribedByIds(ids: string[]) {
    this.describedBy = ids.join(' ');
  }
  // end MatFormFieldControl
  updateErrorState() {
    const oldState = this.errorState;
    const parent = this.parentFormGroup || this.parentForm;
    const matcher = this.errorStateMatcher || this.defaultErrorStateMatcher;
    const control = this.ngControl ? this.ngControl.control as FormControl : null;
    const newState = matcher.isErrorState(control, parent);

    if (newState !== oldState) {
      this.errorState = newState;
      this.stateChanges.next();
    }
  }

  setupOptionGroups() {
    this.optionGroups = [
      {
        name: 'Locations',
        icon: 'location_on'
      }
    ];
  }
}