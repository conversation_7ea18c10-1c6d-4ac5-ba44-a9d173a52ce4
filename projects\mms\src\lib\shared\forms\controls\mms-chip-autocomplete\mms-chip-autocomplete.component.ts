import { Component, OnInit, Output, EventEmitter, Input, On<PERSON><PERSON>roy, ViewChild, Optional, Self, ElementRef, HostBinding, OnChanges, DoCheck } from '@angular/core';
import { Subject, Observable } from 'rxjs';
import { FormControl, FormGroupDirective, NgControl, ControlValueAccessor, NgForm, ReactiveFormsModule } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';
import { MatAutocomplete, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatChipInputEvent, MatChipsModule } from '@angular/material/chips';
import { map,startWith } from 'rxjs/operators';
import { FocusMonitor } from '@angular/cdk/a11y';
import { ENTER } from '@angular/cdk/keycodes';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

export interface CanUpdateErrorState {
    errorState: boolean;
    errorStateMatcher: ErrorStateMatcher;
    updateErrorState(): void;
}

@Component({
    selector: 'mms-chip-autocomplete',
    templateUrl: './mms-chip-autocomplete.component.html',
    styleUrls: ['./mms-chip-autocomplete.component.scss'],
    providers: [
        {
            provide: MatFormFieldControl,
            useExisting: MMSChipAutocompleteComponent
        }
    ],
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatChipsModule,
        MatAutocompleteModule,
        MatIconModule
    ]
})
export class MMSChipAutocompleteComponent implements OnInit, OnChanges, OnDestroy, DoCheck, ControlValueAccessor, MatFormFieldControl<any[]>, CanUpdateErrorState {
    // MatFormFieldControl
    static nextId = 0;
    private _uid = `mms-chip-autocomplete-${MMSChipAutocompleteComponent.nextId++}`;
    menuValue = '';
    stateChanges = new Subject<void>();
    controlType = 'mms-chip-autocomplete';
    errorState = false;
    autofilled = false;
    focused = false;
    ngControl: NgControl;
    readonly = false;

    matChipsConfig = {
        visible: true,
        selectable: false,
        removable: true,
        addOnBlur: false,
        separatorKeysCodes: [ENTER]
    };

    filteredOptions$!: Observable<any[]>;

    formControlFilter = new FormControl();
    @ViewChild('auto', { static: false }) matAutocomplete!: MatAutocomplete;
    @ViewChild('filterInput', { static: false }) formControl!: ElementRef<HTMLInputElement>;
    @ViewChild('filterInput', { read: MatAutocompleteTrigger, static: true }) trigger!: MatAutocompleteTrigger;

    @HostBinding('attr.aria-describedby') describedBy = '';

    @Input() isMultiple = false;
    @Input() buttonTrigger = false;
    @Input() buttonTriggerText = '';
    @Input() optionGroups: string[] = [];
    @Input() groupClassifier?: Function;
    @Input() errorStateMatcher: ErrorStateMatcher = new ErrorStateMatcher();
    @Output() valueChange = new EventEmitter<any>();
    @Output() blur = new EventEmitter<void>();
    @Input() valueAsOptions = false;

    private _id = '';
    get id(): string {
        return this._id;
    }
    @Input()
    set id(value: string) {
        this._id = value || this._uid;
    }

    private _value: any[] = [];
    get value(): any[] {
        return this._value;
    }
    @Input()
    set value(value: any[]) {
        this._value = value || [];
        this.onChangeCallback(this._value);
        this.stateChanges.next();
    }

    @Input()
    get required(): boolean {
        return this._required;
    }
    set required(value: boolean) {
        this._required = !!value;
        this.stateChanges.next();
    }
    private _required = false;

    @Input()
    get disabled(): boolean {
        if (this.ngControl && this.ngControl.disabled !== null) {
            return this.ngControl.disabled;
        }
        return this._disabled;
    }
    set disabled(value: boolean) {
        this._disabled = !!value;

        // Browsers may not fire the blur event if the input is disabled too quickly.
        // Reset from here to ensure that the element doesn't become stuck.
        if (this.focused) {
            this.focused = false;
            this.stateChanges.next();
        }
    }
    private _disabled = false;

    @Input() compareFn = (c1: any, c2: any) => {
        return c1 === c2;
    }
    @Input()
    get placeholder(): string {
        return this._placeholder;
    }
    set placeholder(value: string) {
        this._placeholder = value;
        this.stateChanges.next();
    }
    private _placeholder = '';

    get shouldLabelFloat(): boolean {
        return this.focused || !this.empty;
    }

    get empty(): boolean {
        return !this.value || this.value.length === 0;
    }

    private _onDestroy = new Subject<void>();

    private _options: any[] = [];
    @Input()
    get options(): any[] {
        return this._options;
    }
    set options(options: any[]) {
        if (options && options.length > 0) {
            this._options = options;
            if (this.selectedOptions && this.selectedOptions.length === 0 && this.value != null) {
                this.selectedOptions = this.options.filter(x => this.compareFn(x, this.value)) || [];
            }
        } else {
            this._options = [];
        }

        this.formControlFilter.updateValueAndValidity();
        this.stateChanges.next();
    }

    private _selectedOptions: any[] = [];
    get selectedOptions(): any[] {
        return this._selectedOptions;
    }
    set selectedOptions(options: any[]) {
        if (options && options.length > 0) {
            this._selectedOptions = options;
        }
        this._selectedOptions = options;
    };

    @Input() textMapper = (option: any) => {
        return option['name'];
    }
    @Input() valueMapper = (option: any) => {
        return option['id'];
    }
    @Input() shouldDisplayOption = (option: any) => {
        return true;
    }

    constructor(
        @Optional() @Self() ngControl: NgControl,
        @Optional() public parentForm: NgForm,
        @Optional() public parentFormGroup: FormGroupDirective,
        public defaultErrorStateMatcher: ErrorStateMatcher,
        private fm: FocusMonitor,
        private elRef: ElementRef<HTMLElement>
    ) {
        this.ngControl = ngControl;

        // Setting the value accessor directly (instead of using
        // the providers) to avoid running into a circular import.
        if (this.ngControl != null) { this.ngControl.valueAccessor = this; }

        // Force setter to be called in case id was not specified.
        this.id = this.id;

        // MatFormFieldControl
        fm.monitor(elRef.nativeElement, true).subscribe(origin => {
            this.focused = !!origin;
            this.stateChanges.next();
        });
        // end MatFormFieldControl
    }

    ngDoCheck() {
        if (this.ngControl) {
            this.updateErrorState();
        }
    }

    setDescribedByIds(ids: string[]) {
        this.describedBy = ids.join(' ');
    }

    onContainerClick(event: MouseEvent) {
        this.formControl.nativeElement.focus();
        this.trigger.openPanel();
    }

    // ControlValueAccessor
    onChangeCallback: (value: string | string[]) => void = () => { };
    onTouchedCallback: () => void = () => { };

    writeValue(val: any[] | null): void {
        const newValue = val || [];
        this._value = newValue;
        this.onChangeCallback(this._value);
        this.stateChanges.next();
        
        if (newValue.length > 0) {
            this.selectedOptions = this.options.filter(x => newValue.some(v => this.compareFn(x, v))) || [];
        } else {
            this.selectedOptions = [];
        }
    }

    registerOnChange(fn: any): void {
        this.onChangeCallback = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouchedCallback = fn;
    }

    // end ControlValueAccessor
    ngOnInit() {
        this.filteredOptions$ = this.formControlFilter.valueChanges.pipe(
            startWith(''),
            map(value => this._filter(value))
        ) as Observable<any[]>;

        if (this.valueAsOptions && this.value && this.textMapper(this.value[0]) && this.options) {
            this.options = this.value;
            this.selectedOptions = this.value;
        }
    }

    ngOnChanges() {
        this.stateChanges.next();
    }

    ngOnDestroy() {
        this._onDestroy.next();
        this._onDestroy.complete();
    }

    onOpenedChange(isOpen: boolean): void {
        if (isOpen) {
            this.trigger?.openPanel();
        }
    }

    onClosed() {
        this.formControl.nativeElement.value = '';
        this.formControlFilter.setValue(null);
    }

    onSelectionChange(event: { value: any }): void {
        this.formControlFilter.setValue(event.value);
    }

    updateErrorState() {
        const oldState = this.errorState;
        const parent = this.parentFormGroup || this.parentForm;
        const matcher = this.errorStateMatcher || this.defaultErrorStateMatcher;
        const control = this.ngControl ? this.ngControl.control as FormControl : null;
        const newState = matcher.isErrorState(control, parent);

        if (newState !== oldState) {
            this.errorState = newState;
            this.stateChanges.next();
        }
    }

    add(event: MatChipInputEvent): void {
        // To make sure this does not conflict with OptionSelected Event
        if (!this.matAutocomplete.isOpen) {
            const input = event.input;
            if (input) {
                input.value = '';
            }
            this.formControlFilter.setValue(null);
            this.trigger.openPanel();
        }
    }

    focusFilterInput() {
        this.formControl.nativeElement.focus();
    }

    clearFilterInput() {
        if (!this.matAutocomplete.isOpen) {
            this.formControl.nativeElement.value = '';
            this.formControlFilter.setValue(null);
        }
        this.blur.emit();
    }

    remove(option: any): void {
        const index = this.selectedOptions.indexOf(option);
        if (index >= 0) {
            this.selectedOptions.splice(index, 1);
            this.value = [...this.selectedOptions.map(x => this.valueMapper(x))];
        }

        this.formControlFilter.setValue(null);

        if (this.selectedOptions.length === 0) {
            this.formControl.nativeElement.focus();
            this.trigger.openPanel();
        }
    }

    selected(event: MatAutocompleteSelectedEvent): void {
        const option = this.options.filter(x => this.valueMapper(x) === event.option.value);

        if (option.length > 0) {
            this.selectedOptions.push(option[0]);
            this.value = [...this.selectedOptions.map(x => this.valueMapper(x))];
        }

        this.formControl.nativeElement.value = '';
        this.formControlFilter.setValue(null);

        setTimeout(x => {
            this.trigger.openPanel();
        });
    }

    private _filter(value: any): any[] | null {
        if (typeof value === 'string') {
            const filterValue = value.toLowerCase();
            return this.options ? this.options.filter(option => this.textMapper(option).toLowerCase().indexOf(filterValue) === 0 && this.selectedOptions.indexOf(option) === -1) : null;
        } else {
            return this.options.filter(option => this.selectedOptions.indexOf(option) === -1);
        }
    }

}
