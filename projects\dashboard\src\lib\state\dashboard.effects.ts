import { Inject, Injectable } from '@angular/core';

import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import { catchError, exhaustMap, map, switchMap } from 'rxjs/operators';

import * as featureActions from './dashboard.actions';

import { DashboardServiceInterface } from '../dashboard.model';
import { ConfirmDialog } from '../confirm-dialog.service';

@Injectable()
export class DashboardEffects {
  constructor(
    private readonly actions$: Actions,
    private readonly store: Store<any>,
    @Inject('DashboardServiceInterface') private readonly dashboardService: DashboardServiceInterface,
    private readonly page: ConfirmDialog) {
  }

  addDashboardEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AddDashboardAction>(
      featureActions.ActionTypes.ADD_DASHBOARD
    ),
    switchMap((action) =>
      this.dashboardService
        .saveUserDashboard(action.payload.dashboard)
        .pipe(
          map(
            dashboard => {
              dashboard.added = true;
              dashboard.editing = true;
              return new featureActions.SaveDashboardSuccessAction({ dashboard: dashboard });
            }
          ),
          catchError(error =>
            of(new featureActions.LoadFailureAction({ error }))
          )
        ))
  ));

  saveDashboardEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveDashboardAction>(
      featureActions.ActionTypes.SAVE_DASHBOARD
    ),
    switchMap((action) =>
      this.dashboardService
        .saveUserDashboard(action.payload.dashboard)
        .pipe(
          map(
            dashboard => {
              return new featureActions.SaveDashboardSuccessAction({ dashboard: dashboard });
            }
          ),
          catchError(error =>
            of(new featureActions.LoadFailureAction({ error }))
          )
        ))
  ));

  deleteDashboardEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteDashboardAction>(
      featureActions.ActionTypes.DELETE_DASHBOARD
    ),
    switchMap((action) =>
      this.dashboardService
        .deleteUserDashboard(action.payload.dashboard)
        .pipe(
          map(
            dashboard => {
              return new featureActions.DeleteDashboardSuccessAction({ dashboard: action.payload.dashboard });
            }
          ),
          catchError(error =>
            of(new featureActions.LoadFailureAction({ error }))
          )
        ))
  ));

  confirmDeleteEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmDeleteAction>(
      featureActions.ActionTypes.CONFIRM_DELETE
    ),
    exhaustMap(action =>
      this.page
        .show(
          `You're about to delete the ${action.payload.dashboard.name} dashboard.`,
          'Are you sure?'
        )
        .then(result =>
          result
            ? new featureActions.DeleteDashboardAction({ dashboard: action.payload.dashboard })
            : new featureActions.NoAction()
        )
    )
  ));

  loadDashboardEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadDashboardsAction>(
      featureActions.ActionTypes.LOAD_DASHBOARDS
    ),
    switchMap((action) =>
      this.dashboardService
        .getUserDashboards()
        .pipe(
          map(
            dashboards => {
              dashboards.forEach(d => {
                d.added = false;
                d.editing = false;
              });
              return new featureActions.LoadDashboardsSuccessAction({ dashboards: dashboards });
            }
          ),
          catchError(error =>
            of(new featureActions.LoadFailureAction({ error }))
          )
        ))
  ));
}
