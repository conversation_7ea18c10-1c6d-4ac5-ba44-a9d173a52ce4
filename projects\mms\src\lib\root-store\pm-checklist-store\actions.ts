import { Action } from '@ngrx/store';
import { Update } from '@ngrx/entity';
import { Checklist } from '../../configuration/pm-checklist/checklist.model';
import { ChecklistItemGroup } from '../../configuration/pm-checklist-items/shared/checklist-item-group.model';
import { ChecklistItem } from '../../configuration/pm-checklist-items/shared/checklist-item.model';
import { ChecklistInstance } from '../../configuration/pm-checklist-items/checklistInstance.model';

export enum ActionTypes {
    LOAD_REQUEST = '[Preventive Maintenance] Load Request',
    LOAD_FAILURE = '[Preventive Maintenance] Load Failure',
    LOAD_SUCCESS = '[Preventive Maintenance] Load Success',
    ACTIVATE_SEARCH = '[Preventive Maintenance] Activate Search',
    DEACTIVATE_SEARCH = '[Preventive Maintenance] Deactivate Search',
    SEARCH_TEXT_CHANGED = '[Preventive Maintenance] Search Change',
    SAVE = '[Preventive Maintenance] Save',
    SAVE_SUCCESS = '[Preventive Maintenance] Save Success',
    SAVE_REQUEST = '[Preventive Maintenance] Save Request',
    UPDATE_REQUEST = '[Preventive Maintenance] Update Request',
    UPDATE_SUCCESS = '[Preventive Maintenance] Update Success',
    DELETE = '[Preventive Maintenance] Delete',
    CAN_BE_DELETED = '[Preventive Maintenance] Can Be Deleted',
    CONFIRM_DELETE = ' [Preventive Maintenance] Confirm delete',
    DELETE_REQUEST = '[Preventive Maintenance] Delete Request',
    DELETE_SUCCESS = '[Preventive Maintenance] Delete Success',
    NO_ACTION = '[Preventive Maintenance] No Action',
    ADD_NEW = '[Preventive Maintenance] Add New',
    ADD_CHECKLIST_ITEMS = '[Preventive Maintenance] Add Checklist items',
    REMOVE_CHECKLIST_ITEMS = '[Preventive Maintenance] Remove Checklist items',
    SET_SELECTED_CHEKLIST_ITEMS = '[Preventive Maintenance] Set Selected Checklist items',
    RESET_SELECTED_CHEKLIST_ITEMS = '[Preventive Maintenance] Reset Selected Checklist items',
    SHOW_SIDEBAR_ITEMS = '[Preventive Maintenance] Show Sidebar Items',
    SHOW_SIDEBAR_CREATE_EVENT = '[Preventive Maintenance] Show Sidebar Create Event',
    HIDE_ITEMS = '[Preventive Maintenance] Hide Items',
    LOAD_CHECKLIST_INSTANCES_REQUEST = '[Preventive Maintenance] Load ChecklistInstances Request',
    LOAD_CHECKLIST_INSTANCES_SUCCESS = '[Preventive Maintenance] Load ChecklistInstances Success',
    CLOSE_SIDEBAR = '[Preventive Maintenance] Close Sidebar',
    SAVE_NOT_SCHEDULABLE_CHECKLIST = '[Preventive Maintenance] Save Not Schedulable Checklist',
    LOAD_CHECKLIST_BY_AGENCY_REQUEST = '[Preventive Maintenance] Load Checklist By Agency Request',
    LOAD_CHECKLIST_BY_AGENCY_SUCCESS = '[Preventive Maintenance] Load Checklist By Agency Success',
}

export class LoadRequestAction implements Action {
    readonly type = ActionTypes.LOAD_REQUEST;
}

export class LoadFailureAction implements Action {
    readonly type = ActionTypes.LOAD_FAILURE;
    constructor(public payload: { error: string }) { }
}

export class LoadSuccessAction implements Action {
    readonly type = ActionTypes.LOAD_SUCCESS;
    constructor(public payload: { checkLists: Checklist[], checkListItemGroups: ChecklistItemGroup[] }) { }
}

export class ActivateSearchAction implements Action {
    readonly type = ActionTypes.ACTIVATE_SEARCH;
}

export class DeactivateSearchAction implements Action {
    readonly type = ActionTypes.DEACTIVATE_SEARCH;
}

export class SearchTextChangedAction implements Action {
    readonly type = ActionTypes.SEARCH_TEXT_CHANGED;
    constructor(public payload: string) { };
}

export class SaveAction implements Action {
    readonly type = ActionTypes.SAVE;
    constructor(public payload: Checklist) { }
}

export class SaveRequestAction implements Action {
    readonly type = ActionTypes.SAVE_REQUEST;
    constructor(public payload: Checklist) { }
}

export class SaveSuccessAction implements Action {
    readonly type = ActionTypes.SAVE_SUCCESS;
    constructor(public payload: Checklist) { }
}

export class UpdateRequestAction implements Action {
    readonly type = ActionTypes.UPDATE_REQUEST;
    constructor(public payload: Checklist) { }
}

export class UpdateSuccessAction implements Action {
    readonly type = ActionTypes.UPDATE_SUCCESS;
    constructor(public payload: Update<Checklist>) { }
}

export class DeleteAction implements Action {
    readonly type = ActionTypes.DELETE;
    constructor(public payload: Checklist) { }
}

export class DeleteRequestAction implements Action {
    readonly type = ActionTypes.DELETE_REQUEST;
}

export class DeleteSuccessAction implements Action {
    readonly type = ActionTypes.DELETE_SUCCESS;
    constructor(public payload: string) { }
}

export class CanBeDeletedAction implements Action {
    readonly type = ActionTypes.CAN_BE_DELETED;
    constructor() { }
}

export class ConfirmDeleteAction implements Action {
    readonly type = ActionTypes.CONFIRM_DELETE;
}

export class NoAction implements Action {
    readonly type = ActionTypes.NO_ACTION;
}

export class AddNewAction implements Action {
    readonly type = ActionTypes.ADD_NEW;
}

export class AddChecklistItemsAction implements Action {
    readonly type = ActionTypes.ADD_CHECKLIST_ITEMS;
    constructor(public payload: ChecklistItem[]) { }
}

export class RemoveChecklistItemsAction implements Action {
    readonly type = ActionTypes.REMOVE_CHECKLIST_ITEMS;
    constructor(public payload: ChecklistItem[]) { }
}

export class SetSelectedChecklistItemsAction implements Action {
    readonly type = ActionTypes.SET_SELECTED_CHEKLIST_ITEMS;
    constructor(public payload: ChecklistItem[]) { }
}

export class ResetSelectedChecklistItemsAction implements Action {
    readonly type = ActionTypes.RESET_SELECTED_CHEKLIST_ITEMS;
    constructor() { }
}

export class ShowSidebarItemsAction implements Action {
    readonly type = ActionTypes.SHOW_SIDEBAR_ITEMS;
    constructor() { }
}

export class ShowSidebarCreateEventAction implements Action {
    readonly type = ActionTypes.SHOW_SIDEBAR_CREATE_EVENT;
    constructor() { }
}

export class HideItemsAction implements Action {
    readonly type = ActionTypes.HIDE_ITEMS;
    constructor() { }
}

export class LoadAllChecklistInstancesAction implements Action {
    readonly type = ActionTypes.LOAD_CHECKLIST_INSTANCES_REQUEST;
    constructor() {}
}

export class LoadAllChecklistInstancesSuccess implements Action {
    readonly type = ActionTypes.LOAD_CHECKLIST_INSTANCES_SUCCESS;
    constructor(public payload: {checklistInstances : ChecklistInstance[]}) {}
}

export class CloseSidebarAction implements Action {
    readonly type = ActionTypes.CLOSE_SIDEBAR;
    constructor() {}
}

export class SaveNotSchedulableChecklist implements Action {
    readonly type = ActionTypes.SAVE_NOT_SCHEDULABLE_CHECKLIST;
    constructor(public payload: Array<Checklist>) { }
}

export class LoadChecklistByAgencyRequestAction implements Action {
    readonly type = ActionTypes.LOAD_CHECKLIST_BY_AGENCY_REQUEST;
    constructor(public payload: { agencyId: string }) { }
}
export class LoadChecklistByAgencySuccessAction implements Action {
    readonly type = ActionTypes.LOAD_CHECKLIST_BY_AGENCY_SUCCESS;
    constructor(public payload: { checklists: Checklist[] }) { }
}

export type Actions =
    LoadRequestAction |
    LoadFailureAction |
    LoadSuccessAction |
    ActivateSearchAction |
    DeactivateSearchAction |
    SearchTextChangedAction |
    SaveRequestAction |
    SaveAction |
    SaveSuccessAction |
    UpdateRequestAction |
    UpdateSuccessAction |
    DeleteAction |
    DeleteRequestAction |
    DeleteSuccessAction |
    CanBeDeletedAction |
    ConfirmDeleteAction |
    NoAction |
    AddNewAction |
    AddChecklistItemsAction |
    RemoveChecklistItemsAction |
    SetSelectedChecklistItemsAction |
    ResetSelectedChecklistItemsAction |
    ShowSidebarItemsAction |
    ShowSidebarCreateEventAction |
    LoadAllChecklistInstancesAction |
    LoadAllChecklistInstancesSuccess |
    HideItemsAction |
    CloseSidebarAction |
    SaveNotSchedulableChecklist |
    LoadChecklistByAgencyRequestAction |
    LoadChecklistByAgencySuccessAction;
