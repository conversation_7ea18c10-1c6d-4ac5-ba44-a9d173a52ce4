import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { ScheduleFilter } from '../models/filter.model';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { Schedule } from '../models/schedule.model';
import { PMDueScheduleModel } from 'app/dashboard/components/pm-past-due-dashboard/pm-past-due-schedule.model';

@Injectable({
  providedIn: 'root'
})
export class ProjectSchedulerService {

  URL: string = "project/v1/project-scheduler";
  constructor(private httpClient: HttpClient) { }

  getSchedules(filter: ScheduleFilter): Observable<Schedule[]> {
    let params = new HttpParams();
    if (filter) {
        if (filter.name != null) {
          params = params.set('name', filter.name);
        }
        if (filter.recurrences != null) {
          params = params.set('recurrences', JSON.stringify(filter.recurrences));
        }
        if (filter.active != null) {
          params = params.set('active', filter.active.toString());
        }
        if (filter.agencyId != null) {
          params = params.set('agencyId', filter.agencyId.toString());
        }
        if (filter.types != null) {
          params = params.set('types', filter.types.toString());
        }
    }
    return this.httpClient.get<Schedule[]>(this.URL + '/search', { params: params })
    .pipe(map((result: any) => result.items));
  }

  addSchedule(schedule: Schedule): Observable<Schedule | null> {
    return this.httpClient.post<Schedule>('project/v1/project-scheduler/', schedule, { observe: 'response' })
      .pipe(map((response: HttpResponse<Schedule>) => {
        return response.body;
      }))
  }

    generateScheduledInstances(scheduleId: string, tenantId: string): Observable<any> {
        const request = {
            scheduleId,
            startDate: new Date().toISOString(),
            tenantIdsList: [tenantId]
        };
        return this.httpClient.post('aggregator/v1/schedule/create-project-instances/', request, { observe: 'response' })
            .pipe(map((response: any) => {
                return response.body;
            }));
    }

  updateSchedule(schedule: Schedule): Observable<boolean> {
    return this.httpClient.put('project/v1/project-scheduler/', schedule).pipe(map(
      () => {
        return true;
      }
    ));
  }

  deleteSchedule(id: string): Observable<boolean> {
    return this.httpClient.delete('project/v1/project-scheduler/' + id)
      .pipe(map(() => {
        return true;
      }))
  }

  getPMPastDueSchedulers(): Observable<Array<PMDueScheduleModel>> {

    return this.httpClient.get<Array<PMDueScheduleModel>>('aggregator/v1/schedule/pm-past-due-schedulers')
    .pipe(map((result: any) => result));
  }
}
