import { Actions, ActionTypes } from './actions';
import { initialState, State, MainState, DISPATCH_HISTORY_REPORT_GRID_ID } from './state';
import { ActionReducerMap, combineReducers } from '@ngrx/store';
import { createGridReducer } from '../shared-store/material-grid-store/reducer';

export function assetsCountReportReducer(state = initialState, action: Actions): MainState {
    switch (action.type) {
       
        case ActionTypes.LOAD_LOCATIONS_REQUEST: {
            return {
                ...state,
                isLoading: true,
                error: null
            };
        }
        case ActionTypes.LOAD_LOCATIONS_SUCCESS: {
            return {
                ...state,
                isLoading: false,
                error: null,
                locations: action.payload.locations
            };
        }
        case ActionTypes.LOAD_LOCATIONS_FAILURE: {
            return {
                ...state,
                isLoading: false,
                error: action.payload.error
            }
        }
        case ActionTypes.SEARCH_REQUEST: {
            return {
                ...state,
                isLoading: true,
                selectedFilter: action.payload
            };
        }
        case ActionTypes.SEARCH_SUCCESS: {
            return {
                ...state,
                isLoading: false
            };
        }
        case ActionTypes.SEARCH_FAILURE: {
            return {
                ...state,
                isLoading: false,
                error: action.payload.error
            };
        }
        case ActionTypes.RESET_FILTER: {
            return {
                ...state,
                selectedFilter: null
            };
        }
        default: {
            return state;
        }
    }
}

export const reducers: ActionReducerMap<State, Actions> = {
    main: assetsCountReportReducer,
    grid: createGridReducer(DISPATCH_HISTORY_REPORT_GRID_ID)
};

const metaReducer = combineReducers(reducers);

export function featureReducer(state: State | undefined, action: Actions) {
    return metaReducer(state, action);
}