import { Component, OnInit, ViewChild, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatD<PERSON>er, MatSidenavModule } from '@angular/material/sidenav';
import { Observable, Subscription } from 'rxjs';
import { RouterReducerState } from '@ngrx/router-store';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { Column } from '../../root-store/shared-store/material-grid-store/state';
import { RouterStateUrl } from '../../root-store/router-store/router-state.serializer';
import { RouterStoreSelectors, ProjectStoreActions, TicketTypeStoreActions, PMChecklistStoreActions, ReasonCodesStoreActions, AgencyStoreActions } from '../../root-store';
import { TicketStoreSelectors, TicketStoreState, TicketStoreActions } from '../../root-store/ticket-store';
import { Formats } from '../../core/formats';
import { Ticket } from './tickets.model';
import { PermissionsEnum } from '../../core/auth/permissions.enum';
import { TagsStoreActions } from '../../root-store/tags-store';
import { TicketTypeCategory } from '../ticket-types/enums/ticket-category.enum';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TicketQuickFilter } from './filter/quick-filter.model';
import { UserIdentity } from '@econolite/identity-client';
import { UserStoreActions, UserStoreSelectors } from '../../root-store/user-store';
import { CallerStoreActions } from '../../root-store/callers-store';
import { TicketType } from '../ticket-types/models/ticket-type.model';
import { User } from '../shared/user-and-groups/user.model';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { GridModule } from '../../shared/grid/grid.module';
// import { IdentityClientModule } from '@econolite/identity-client';
import { BlockUIDirective } from '../../shared/block-ui/block-ui.directive';

@Component({
  templateUrl: './tickets.component.html',
  styleUrls: ['./tickets.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatSidenavModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatInputModule,
    RouterModule,
    GridModule,
    // BlockUIDirective
    // IdentityClientModule
  ]
})
export class TicketsComponent implements OnInit, OnDestroy {

  @ViewChild('sidenav', { static: true }) sideNav!: MatDrawer;
  Permissions = PermissionsEnum;
  columns: Column[] = [
    { name: 'ticketNo', hidden: false, displayText: 'Task #' },
    { name: 'ticketType', hidden: false },
    { name: 'description', hidden: false },
    { name: 'priority', hidden: false },
    { name: 'status', hidden: false },
    { name: 'locationData', hidden: false, displayText: 'Location' },
    { name: 'assignedUsers', hidden: false, displayText: 'Assigned Users' },
    { name: 'dispatchDateTime', hidden: false, displayText: 'Dispatched' },
    { name: 'activeUsers', hidden: false, displayText: 'Active Users' },
    { name: 'project', hidden: false, displayText: 'Project' },
    { name: 'callDateTime', hidden: false, displayText: 'Call Date' },
    { name: 'caller', hidden: true },
    { name: 'actions', canHide: false }    
  ];
  sortByColumns: string[] = ['callDateTime'];
  routerState$: Observable<RouterReducerState<RouterStateUrl>> = this.store$.select(RouterStoreSelectors.getRouterStoreState);
  key = 'id';
  gridStoreSelector: any = TicketStoreSelectors.selectGrid;
  isGridPopulated$: Observable<boolean> = this.store$.select(TicketStoreSelectors.selectIsGridPopulated);
  id = TicketStoreState.TICKET_GRID_ID;
  isGridLoading$: Observable<boolean> = this.store$.select(TicketStoreSelectors.selectIsGridLoading);
  isLoading$: Observable<boolean> = this.store$.select(TicketStoreSelectors.selectIsTicketsLoading);
  initialLoad$: Observable<boolean> = this.store$.select(TicketStoreSelectors.selectInitialLoad);
  DateFormatEnum = Formats;
  subscriptions: Subscription[] = [];

  isTasksScreen = true;
  isCallTasksScreen = false;
  displayHeader = true;
  hideCallElements = true;

  isSearchActive$ = this.store$.select(TicketStoreSelectors.selectSearchActive);
  quickFilter$ = this.store$.select(TicketStoreSelectors.getQuickFilter);
  selectedFilter$ = this.store$.select(TicketStoreSelectors.selectSelectedFilter);
  getIsFilterApplied$ = this.store$.select(TicketStoreSelectors.getIsFilterApplied);
  showTicketDetailsClicked: boolean = false;
  formGroup!: FormGroup;
  selectedUser$ = this.store$.select(UserStoreSelectors.selectUserById);
  selectedUser!: User;

  constructor(private title: Title, private store$: Store<TicketStoreState.State>, @Inject(ChangeDetectorRef) private cdr: ChangeDetectorRef, private formBuilder: FormBuilder, @Inject(UserIdentity) public userIdentity: UserIdentity) {
   
  }

  ngOnInit() {
    this.selectedUser$.subscribe(data => {
      this.selectedUser = data;
    });
    this.store$.dispatch(new UserStoreActions.LoadDefaultTaskUserSuccess({ usersAndGroups: null }));
    this.store$.dispatch(new TicketStoreActions.LoadSidebarRequestAction());
    this.subscriptions.push(this.routerState$.subscribe((data: RouterReducerState<RouterStateUrl>) => {
      this.hideCallElements = data.state.url.includes('call-tickets');
      if (data.state.url.includes('call-tickets') && !data.state.url.includes('filter')) {
        this.title.setTitle("Dispatch Call Ticket");
        this.sortByColumns = ['dispatchDateTime'];
        this.displayHeader = !(data.state.url.includes('add-ticket') || data.state.url.includes('edit-ticket') 
          || data.state.url.includes('add-caller') || data.state.url.includes('filter') || data.state.url.includes('dispatch-ticket') );
        this.store$.dispatch(new TicketStoreActions.LoadRequestAction());
      } else {
        this.title.setTitle("Task Details");
        if (!data.state.url.includes('ticket-details') && !data.state.url.includes('edit-ticket') && !data.state.url.includes('add-caller') && !data.state.url.includes('caller-details')){
            this.store$.dispatch(new TicketStoreActions.LoadRequestAction());          
        }
      }

      if (data.state.url.includes('call-tickets/filter')) {
        this.isCallTasksScreen = false;
      } 
      else if (data.state.url.includes('call-tickets'))
      {
        this.isCallTasksScreen = true;
        this.isTasksScreen = false;
      }
      else
      {   
        this.isCallTasksScreen = false;
        this.isTasksScreen = true;
      }
      
      if (data.state.url.indexOf('add-ticket') > -1 || data.state.url.indexOf('edit-ticket') > -1 || data.state.url.indexOf('ticket-details') > -1 ||
        data.state.url.indexOf('dispatch-ticket') > -1 || data.state.url.indexOf('add-caller') > -1 || data.state.url.indexOf('filter') > -1 ||
        data.state.url.indexOf('caller-details') > -1
      ) {
        this.sideNav.open();
      } else {
        this.sideNav.close();
      }

      this.store$.dispatch(
        new ProjectStoreActions.LoadProjectsRequestAction({})
      );

      this.store$.dispatch(
        new TicketTypeStoreActions.LoadRequestAction()
      );

      this.store$.dispatch(
        new PMChecklistStoreActions.LoadRequestAction()
      );

      this.store$.dispatch(
        new ReasonCodesStoreActions.LoadReasonCodesRequestAction()
      );
      
      this.store$.dispatch(
        new TagsStoreActions.LoadTagsRequestAction()
      );

      if(data.state.url.includes("call-tickets/ticket-details/") || data.state.url.includes("call-tickets/dispatch-ticket/")) {
        this.showTicketDetailsClicked = true;
        const quickFilter: TicketQuickFilter = {
          callType: true,
          woType: true,
          pmType: false,
          finalized: true,
       }
       this.store$.dispatch(new TicketStoreActions.QuickFilterChagnedAction({quickFilter}));
      }
      
    }));

    this.subscriptions.push(this.sideNav.closedStart.subscribe(() => {
      this.store$.dispatch(new TicketStoreActions.CloseSidebarAction());
    }));

    
    setTimeout(() => {
      this.store$.dispatch(new ProjectStoreActions.LoadAllProjects());
    }, 1300);

    this.store$.dispatch(new ProjectStoreActions.LoadProjectsAllAgency())
    this.store$.dispatch(new CallerStoreActions.LoadCallerListRequestAction())
  }

  ngOnDestroy() {
    this.store$.dispatch(new TicketStoreActions.ResetFilterAction());
      this.subscriptions.forEach((subscription: Subscription) => {
          subscription.unsubscribe();
      });

      const quickFilter: TicketQuickFilter = {
        callType: true,
        woType: true,
        pmType: false,
        finalized: false,
     }
     
     this.store$.dispatch(new TicketStoreActions.QuickFilterChagnedAction({quickFilter}));
  }

  addTicket() {
    this.store$.dispatch(new TicketStoreActions.ShowCreateTicketAction({}));
  }

  addCallTicket() {
    var ticketType = new TicketType;
    ticketType.category = TicketTypeCategory.Call;
    this.store$.dispatch(new UserStoreActions.LoadDefaultTaskUserRequest({ ticketType: ticketType, userId: this.selectedUser.id! }));
    this.store$.dispatch(new CallerStoreActions.LoadDefaultCallerRequest({ callerId: this.selectedUser.id! }));
    //showTicketDetailsClicked - it is important for sidenav's width
    this.showTicketDetailsClicked = false;
    this.store$.dispatch(new TicketStoreActions.ShowCreateCallTicketAction({}));
    this.store$.dispatch(new UserStoreActions.LoadUserAction({ userId: this.userIdentity.id}));
    this.store$.dispatch(new AgencyStoreActions.LoadRequestAction())
  }

  editTicket(ticketId: string) {
   this.store$.dispatch(new UserStoreActions.LoadDefaultTaskUserSuccess({ usersAndGroups: null }));
   this.showTicketDetailsClicked = false;
   this.store$.dispatch(new TicketStoreActions.ShowEditTicketAction({ ticketId: ticketId }));
   this.store$.dispatch(new UserStoreActions.LoadUserAction({ userId: this.userIdentity.id}));
   this.store$.dispatch(new AgencyStoreActions.LoadRequestAction())
  }

  showTicketDetails(ticketId: string) {
    this.showTicketDetailsClicked = true;
    this.store$.dispatch(new TicketStoreActions.ShowTicketDetailsAction({ ticketId: ticketId }));
  }

  deleteTicket(ticket: Ticket) {
    this.store$.dispatch(new TicketStoreActions.ConfirmDeleteAction({ ticket }));
  }

  assign(ticketId: string) {
    this.store$.dispatch(new TicketStoreActions.ShowDispatchTicketAction({ ticketId: ticketId }));
  }

  showFilter() {
    this.store$.dispatch(new TicketStoreActions.ShowFilterAction());
  }

  mapStatusData(ticket: Ticket) {
    return Ticket.getStatus(ticket);
  }

  closeSideNav() {
    this.store$.dispatch(new TicketStoreActions.CloseSidebarAction());
    this.showTicketDetailsClicked = false;
  }

  ticketsGridSoringAccessor(data: Ticket, propertyName: string) {
    switch (propertyName) {
      case 'ticketNo':
          return +(data.ticketNo ?? 0)
      case 'locationData':
        return data.locationData != null && data.locationData.name != null ? data.locationData.name.toLocaleLowerCase() : '';
      case 'project':
        return data.project != null && data.project.name != null ? data.project.name.toLocaleLowerCase() : '';
      case 'dispatchDateTime':
        return data.dispatchDateTime;
      case 'caller':
        return data.caller != null && data.caller.name != null ? data.caller.name.toLocaleLowerCase() : '';
      default:
        if (typeof (data as any)[propertyName] === 'string') {
          return (data as any)[propertyName].toLocaleLowerCase();
        }
        return (data as any)[propertyName];
    }
  }

  activateSearch() {
    this.store$.dispatch(new TicketStoreActions.ActivateSearchAction());
  }

  cancelSearch() {
    this.store$.dispatch(new TicketStoreActions.DeactivateSearchAction());
    this.store$.dispatch(new TicketStoreActions.SearchTextChagnedAction(''));
  }

  searchChange(value: string) {
    this.store$.dispatch(new TicketStoreActions.SearchTextChagnedAction(value));
  }

  quickFilterChanged() {
    const formValue = this.formGroup.getRawValue()
    const quickFilter: TicketQuickFilter = {
       callType: !!formValue.callType,
       woType: !!formValue.woType,
       pmType: !!formValue.pmType,
       finalized: !!formValue.finalized,
    }
    this.store$.dispatch(new TicketStoreActions.QuickFilterChagnedAction({quickFilter}));
  }
}
