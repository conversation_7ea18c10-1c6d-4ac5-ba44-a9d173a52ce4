import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { PermissionsEnum } from '../../core/auth/permissions.enum';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { JobCodeStoreState, JobCodeStoreSelectors, JobCodeStoreActions } from '../../root-store/job-codes-store';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { RouterOutlet } from '@angular/router';
import { IdentityClientModule } from '@econolite/identity-client';
import { BlockUIDirective } from '../../shared/block-ui/block-ui.directive';

@Component({
  selector: 'mms-job-codes',
  templateUrl: './job-codes.component.html',
  styleUrls: ['./job-codes.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    RouterOutlet,
    BlockUIDirective,
    // IdentityClientModule
  ]
})
export class JobCodesComponent implements OnInit {

  jobCodes$ = this.store$.select(JobCodeStoreSelectors.selectedFilteredJobCodes);
  error$ = this.store$.select(JobCodeStoreSelectors.selectJobCodeError);
  isLoading$ = this.store$.select(JobCodeStoreSelectors.selectJobCodeIsLoading);
  isSearchActive$ = this.store$.select(JobCodeStoreSelectors.selectSearchActive);
  selectedJobCode$ = this.store$.select(JobCodeStoreSelectors.selectedJobCode);
  Permissions = PermissionsEnum;
  @ViewChild('searchInput', { static: false }) searchInput!: ElementRef;

  constructor(private title: Title, private store$: Store<JobCodeStoreState.State>) {
  }

  ngOnInit() {
      this.store$.dispatch(new JobCodeStoreActions.LoadRequestAction());
      this.title.setTitle('Job Codes');
  }

  add(): void {
      this.store$.dispatch(new JobCodeStoreActions.AddNewAction());
  }

  delete() {
    this.store$.dispatch(new JobCodeStoreActions.CanBeDeletedAction());
  }

  activateSearch() {
      this.store$.dispatch(new JobCodeStoreActions.ActivateSearchAction());
      setTimeout(() => {
          this.searchInput.nativeElement.focus();
        }, 20);
  }

  cancelSearch() {
      this.store$.dispatch(new JobCodeStoreActions.DeactivateSearchAction())
  }

  searchChange(value: string) {
      this.store$.dispatch(new JobCodeStoreActions.SearchTextChangedAction(value))
  }
}