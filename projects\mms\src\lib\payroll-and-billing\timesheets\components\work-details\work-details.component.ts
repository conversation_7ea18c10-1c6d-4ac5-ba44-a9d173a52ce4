import { Store } from '@ngrx/store';
import { Component, HostListener, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { ActivatedRoute, Router } from '@angular/router';
import { TimesheetStoreActions, TimesheetStoreSelectors } from '../../../../root-store';
import { map, take, withLatestFrom } from 'rxjs/operators';
import { BlockUIDirective } from '../../../../shared/block-ui/block-ui.directive';
import { WorkPeriodManageComponent } from '../work-period-manage/work-period-manage.component';
import { TaskManageComponent } from '../task-manage/task-manage.component';
import { TaskInventoryComponent } from '../task-inventory/task-inventory.component';
import { TaskHistoryComponent } from '../task-history/task-history.component';
import { VarDirective } from '../../../../shared/forms/variable.directive';

@Component({
  selector: 'app-work-details',
  templateUrl: './work-details.component.html',
  styleUrls: ['./work-details.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    BlockUIDirective,
    WorkPeriodManageComponent,
    TaskManageComponent,
    TaskInventoryComponent,
    TaskHistoryComponent,
    VarDirective
  ]
})
export class WorkDetailsComponent implements OnInit {

  isLoading$ = this.store$.select(TimesheetStoreSelectors.selectIsLoading);
  selectedTimesheet$ = this.store$.select(TimesheetStoreSelectors.selectSingleTimesheet);
  selectedWorkPeriod$ = this.store$.select(TimesheetStoreSelectors.selectWorkPeriodForEditor);

  isUIBlocked = false;
  constructor(private router: Router, private store$: Store<any>, private route: ActivatedRoute) { }


  ngOnInit() {
    this.isLoading$.subscribe(isLoading => {
      setTimeout(() => {
        this.isUIBlocked = isLoading || false;
      });
    });

    window.scrollTo(0, 0);
    this.route.paramMap.subscribe((params) => {
      const timesheetId = params.get('id');
      if (timesheetId) {
        this.store$.dispatch(new TimesheetStoreActions.LoadSingleTimesheet({ timesheetId }));
      }
      this.selectedTimesheet$.subscribe(data => {
        setTimeout(() => {
          this.store$.dispatch(new TimesheetStoreActions.LoadEmployees());
        });
      })
    });
  }

  goBack(timesheetId: string) {
    this.router.navigate([`/maintenance/timesheet/${timesheetId}`]);
  }

  canDeactivate() {
    const navigation = this.router.getCurrentNavigation();
    const shouldDeactivate = navigation?.extras?.state?.['shouldDeactivate'];

    return this.store$.select(
      TimesheetStoreSelectors.selectIsWorkPeriodOrTaskModified
    ).pipe(withLatestFrom(
      this.store$.select(
        TimesheetStoreSelectors.getWorkPeriod),
      this.store$.select(
        TimesheetStoreSelectors.getTaskDetails)),
      map(([formModified, workPeriod, taskDetails]) => {
        return !formModified.workDetailsChanged || shouldDeactivate;
      }),
      take(1)
    );
  };

  /**
   * Triggered on any unload browser event (refresh, back, exit, tenant change, sign out) if there are any unsaved changes on form
   */
  @HostListener('window:beforeunload')
  canUnloadPage(): boolean {
    var returnValue = false;
    this.canDeactivate().subscribe(x => returnValue = x);
    return returnValue;
  }

  onTabChange(event: any) {
    if (event.index === 1) { // Index 1 corresponds to the "Task" tab
      this.selectedTimesheet$.subscribe(timesheet => {
      });
    }
    this.selectedWorkPeriod$.subscribe(workPeriod => {
    })
  }
}
