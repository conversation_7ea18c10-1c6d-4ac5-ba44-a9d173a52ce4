import { HttpClient, HttpBackend } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { UserIdentity } from '@econolite/identity-client';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class AppSettingsService {
    private httpClient: HttpClient;
    private appSettings!: AppSettings;

    constructor(handler: HttpBackend,
        private userIdentity: UserIdentity) {
        this.httpClient = new HttpClient(handler);
    }

    public async initAppConfig(): Promise<AppSettings> {
        const settings = await firstValueFrom(this.httpClient.get<AppSettings>('./config/config.json'));
        if (!settings) {
            throw new Error('Failed to load app settings');
        }
        
        this.appSettings = settings;
        await this.userIdentity.init({
            identityUrl: settings.api.identityUrl,
            usersUrl: settings.api.baseUrl,
            clientId: 'web',
            clientScope: settings.api.clientScope,
            clientRedirectUri: `${window.location.origin}/login-redirect.html`,
            clientPostLogoutRedirectUri: `${window.location.origin}/logout-redirect.html`,
            clientSilentRedirectUri: `${window.location.origin}/silent-redirect.html`,
            useLegacyOidcClient: settings.api.useLegacyOidcClient,
            identityRealm: 'econolite',
            flow: 'implicit'
        });
        
        return settings;
    }

    public getAppConfig(): AppSettings {
        return this.appSettings;
    }
}

export interface AppSettings {
    api: Api;
}

export interface Api {
    baseUrl: string;
    clientScope: string;
    mapConfigurationUrl: string;
    identityUrl: string;
    mapId: string;
    reportService: string,
    jasperReportServer: string,
    useLegacyOidcClient: boolean
}
