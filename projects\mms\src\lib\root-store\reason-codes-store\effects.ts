import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Page } from '../../core/page.service';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import { map, catchError, concatMap, switchMap, tap, withLatestFrom, exhaustMap } from 'rxjs/operators';
import * as featureActions from './actions';
import * as featureState from './state';
import { RouterStoreSelectors } from '../router-store';
import { RouterReducerState } from '@ngrx/router-store';
import { RouterStateUrl } from '../router-store/router-state.serializer';
import { FormResetAction } from '../../shared/forms/connect-form-ngrx';
import { ReasonCodesService } from '../../configuration/reason-codes/services/reason-codes.service';
import { displayMessages } from '../../core/resources/display-messages';

@Injectable()
export class ReasonCodesStoreEffects {

    loadReasonCodesRequestEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.LoadReasonCodesRequestAction>(
                featureActions.ActionTypes.LOAD_REASON_CODES_REQUEST
            ),
            switchMap(_ =>
                this.reasonCodesService.getReasonCodes().pipe(
                    map(reasonCodes => new featureActions.LoadReasonCodesSuccessAction({
                        reasonCodes
                    })),
                    catchError(error => of(new featureActions.LoadFailureAction({ error })))
                )
            )
        )
    );

    addNewEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.AddNewAction>(
                featureActions.ActionTypes.ADD_NEW
            ),
            tap(() => {
                this.router.navigate(['/configuration/reason-codes']);
            })
        ),
        { dispatch: false }
    );

    confirmDeleteReasonCodeEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.ConfirmDeleteReasonCodeAction>(
                featureActions.ActionTypes.CONFIRM_DELETE_REASON_CODE
            ),
            exhaustMap((_) =>
                this.page.confirm.show('Are you sure you want to delete this reason code?', 'Are you sure?').pipe(
                    map(
                        (result) => {
                            if (result) {
                                return new featureActions.DeleteReasonCodeRequestAction();
                            }
                            return new featureActions.NoAction();
                        }
                    )
                )
            )
        )
    );

    deleteReasonCodeEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.DeleteReasonCodeRequestAction>(
                featureActions.ActionTypes.DELETE_REASON_CODE_REQUEST
            ),
            withLatestFrom(
                this.store$.select(RouterStoreSelectors.getRouterStoreState),
            ),
            switchMap(([_, routerState]: [featureActions.DeleteReasonCodeRequestAction, RouterReducerState<RouterStateUrl>]) =>
                this.reasonCodesService.deleteReasonCode(routerState.state.params['id']).pipe(
                    map(
                        () => {
                            return new featureActions.DeleteReasonCodeSuccessAction({ id: routerState.state.params['id'] });
                        }
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
            )
        )
    );

    deleteReasonCodeSuccessEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.DeleteReasonCodeSuccessAction>(
                featureActions.ActionTypes.DELETE_REASON_CODE_SUCCESS
            ),
            concatMap((_) => {
                this.page.notification.show('Reason Code deleted');
                return [
                    new FormResetAction(),
                    new featureActions.AddNewAction()
                ];
            })
        )
    );

    saveRequestEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.SaveReasonCodeAction>(
                featureActions.ActionTypes.SAVE_REASON_CODE_REQUEST
            ),
            exhaustMap((action) =>
                this.reasonCodesService.createReasonCode(action.payload.reasonCode).pipe(
                    map((reasonCode) => {
                        return new featureActions.SaveReasonCodeSuccessAction({
                            reasonCode: reasonCode!
                        });
                    }),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
            )
        )
    );

    saveRequestSuccessEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.SaveReasonCodeSuccessAction>(
                featureActions.ActionTypes.SAVE_REASON_CODE_SUCCESS
            ),
            map(() => {
                this.page.notification.show('Reason Code added');
                return new FormResetAction();
            })
        )
    );

    updateRequestEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.UpdateReasonCodeAction>(
                featureActions.ActionTypes.UPDATE_REASON_CODE_REQUEST
            ),
            exhaustMap((action) =>
                this.reasonCodesService.updateReasonCode(action.payload.reasonCode).pipe(
                    map(() =>
                        new featureActions.UpdateReasonCodeSuccessAction({ reasonCode: { id: action.payload.reasonCode.id!, changes: action.payload.reasonCode } })
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
            )
        )
    );

    updateRequestSuccessEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.UpdateReasonCodeSuccessAction>(
                featureActions.ActionTypes.UPDATE_REASON_CODE_SUCCESS
            ),
            tap(() =>
                this.page.notification.show('Reason Code updated')
            )
        ),
        { dispatch: false }
    );

    canBeDeletedEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.CanBeDeletedAction>(
                featureActions.ActionTypes.CAN_BE_DELETED
            ),
            withLatestFrom(
                this.store$.select(RouterStoreSelectors.getRouterStoreState),
            ),
            exhaustMap(([_, routerState]: [featureActions.CanBeDeletedAction, RouterReducerState<RouterStateUrl>]) =>
                this.reasonCodesService.canBeDeleted(routerState.state.params['id']).pipe(
                    map((response) => {
                        if (!response) {
                            return new featureActions.ConfirmDeleteReasonCodeAction();
                        } else {
                            this.page.alert.warning(displayMessages.exceptions.itemIsAssociated('reason code'));
                            return new featureActions.NoAction();
                        }
                    }),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
            )
        )
    );

    constructor(
        private actions$: Actions,
        private page: Page,
        private router: Router,
        private reasonCodesService: ReasonCodesService,
        private store$: Store<featureState.State>) { }
}