import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Action, Store, select } from '@ngrx/store';
import { Observable, of as observableOf, of } from 'rxjs';
import { catchError, map, switchMap, tap, withLatestFrom, exhaustMap } from 'rxjs/operators';

import * as featureActions from './action';
import * as featureState from './state'
import * as featureSelectors from './selectors'

import { Router } from '@angular/router';
import { RegionsService } from '../../configuration/regions/regions.service';
import { Page } from '../../core/page.service';
import { FormResetAction } from '../../shared/forms/connect-form-ngrx';
import { IGridStoreEffects } from '../shared-store/material-grid-store/effects';
import { GridStoreActions } from '../shared-store/material-grid-store';

import { AgencyStoreSelectors } from '../agency-store';
import { UserIdentity } from '@econolite/identity-client';
import { ReorderedEquipmentService } from '../../configuration/reordered-equipment/services/reordered-equipment.service';
import { ReorderedEquipment } from '../../configuration/reordered-equipment/models/reordered-equipment.model';

@Injectable()
export class ReorderedEquipmentStoreEffects implements IGridStoreEffects {

  constructor(
    private reorderedEquipmentService: ReorderedEquipmentService,
    private router: Router,
    private regionService: RegionsService,
    private actions$: Actions,
    private store$: Store<featureState.State>,
    private page: Page,
    private userIdentity: UserIdentity) { }
  gridLoadItems(actionType: string): Observable<GridStoreActions.Actions> {
    throw new Error('Method not implemented.');
  }
  gridLoadItemsSuccess(actionType: string): Observable<Action> {
    throw new Error('Method not implemented.');
  }
  gridLoadItemsFailed(actionType: string): Observable<Action> {
    throw new Error('Method not implemented.');
  }

  // tslint:disable-next-line:member-ordering
  loadRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(
      featureActions.ActionTypes.LOAD_REQUEST
    ),
    withLatestFrom(
      this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
    ),
    switchMap(([_, agency]) => {
      if (!agency) {
        return of(new GridStoreActions.GridNoAction());
      }

      return this.reorderedEquipmentService
        .getAllReorderedEq(agency)
        .pipe(
          map(
            item =>
              new featureActions.LoadSuccessAction({ items: item })
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    })
  ));

  // tslint:disable-next-line:member-ordering
  addNewEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AddNewAction>(
      featureActions.ActionTypes.ADD_NEW
    ),
    tap(() => {
      this.router.navigate(['/maintenance/reordered-equipment-status']);
    })
  ), { dispatch: false });

  saveEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveAction>(
      featureActions.ActionTypes.SAVE
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedReorderedEquipment)),
    ),
    map(([action, selectedReorderedEquipment]: [featureActions.SaveAction, ReorderedEquipment]) => {
      if (selectedReorderedEquipment == null) {
        return new featureActions.SaveRequestAction({ item: action.payload.item })
      } else {
        const reorderedEquipment = action.payload.item;
        reorderedEquipment.id = selectedReorderedEquipment.id;
        reorderedEquipment.agency = selectedReorderedEquipment.agency;
        return new featureActions.UpdateRequestAction({ item: reorderedEquipment })
      }
    })
  ));

  saveRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveRequestAction>(
      featureActions.ActionTypes.SAVE_REQUEST
    ),
    withLatestFrom(this.store$.select(AgencyStoreSelectors.selectDefaultAgency)),
    exhaustMap(([action, agency]) => {
      if (!agency) {
        return of(new featureActions.LoadFailureAction({ error: 'No agency selected' }));
      }
      const reorderedEquipment: ReorderedEquipment = {
        ...action.payload.item,
        agency
      };
      return this.reorderedEquipmentService
        .addReorderedEq(reorderedEquipment)
        .pipe(
          map(
            (reorderedEq) => {
              return new featureActions.SaveSuccessAction({
                item: reorderedEq!
              });
            }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    })
  ));

  updateRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateRequestAction>(
      featureActions.ActionTypes.UPDATE_REQUEST
    ),
    exhaustMap((action) =>
      this.reorderedEquipmentService
        .updateReorderedEq(action.payload.item)
        .pipe(
          map(
            () =>
              new featureActions.UpdateSuccessAction({ item: { id: action.payload.item.id!, changes: action.payload.item } })
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  updateRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateSuccessAction>(
      featureActions.ActionTypes.UPDATE_SUCCESS
    ),
    tap(() => {
      this.page.notification.show('Reordered Equipment updated');
    })
  ), { dispatch: false });

  reorderedEquipmentSaveSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveSuccessAction>(
      featureActions.ActionTypes.SAVE_SUCCESS
    ),
    map(() => {
      this.page.notification.show('Reordered Equipment added');
      return new FormResetAction();
    })
  ));

  confirmDeleteEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmDeleteAction>(
      featureActions.ActionTypes.CONFIRM_DELETE
    ),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectedReorderedEquipment))),
    exhaustMap(([action, selectedReorderedEquipment]: [featureActions.ConfirmDeleteAction, ReorderedEquipment]) =>
      this.page
        .confirm.show('Are you sure you want to delete this reordered equipment?', 'Are you sure?')
        .pipe(
          map(
            (result) => {
              if (result) {
                return new featureActions.DeleteRequestAction();
              }
              return new featureActions.NoAction();
            }
          )
        )
    )
  ));

  deleteRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteRequestAction>(
      featureActions.ActionTypes.DELETE_REQUEST
    ),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectedReorderedEquipment))),
    exhaustMap(([action, selectedReorderedEquipment]: [featureActions.DeleteRequestAction, ReorderedEquipment]) => {
      if (!selectedReorderedEquipment?.id) {
        return of(new featureActions.LoadFailureAction({ error: 'No reordered equipment selected' }));
      }
      return this.reorderedEquipmentService
        .deleteReorderedEq(selectedReorderedEquipment.id)
        .pipe(
          map(
            () => {
              this.page.notification.show('Reordered Equipment deleted');
              return new featureActions.DeleteSuccessAction({ itemId: selectedReorderedEquipment.id! });
            }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    })
  ));

  deleteSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteSuccessAction>(
      featureActions.ActionTypes.DELETE_SUCCESS
    ),
    map(() => {
      this.router.navigate(['/maintenance/reordered-equipment']);
      return new featureActions.LoadRequestAction();
    })
  ));
}