import { Actions, ActionTypes } from './actions';
import { featureAdapter, initialState, MainState, RATES_HISTORY_GRID_ID, TRIP_CHARGES_GRID_ID, VEHICLE_EQUIPMENT_RATES_GRID_ID, JOB_CODE_RATES_GRID_ID, MARKUP_GRID_ID, BILL_CODE_RATES_GRID_ID, BILL_CODE_RATES_HISTORY_GRID_ID, VEHICLE_EQUIPMENT_CATEGORY_MAPPERS_GRID_ID, State } from './state';
import { ActionReducerMap, combineReducers } from '@ngrx/store';
import { createGridReducer } from '../shared-store/material-grid-store/reducer';

export function mainReducer(state = initialState, action: Actions): MainState {
    switch (action.type) {
        case ActionTypes.LOAD_REQUEST: 
        case ActionTypes.LOAD_ALL_JURISDICTION_CONFIGURATIONS_REQUEST: {
            return {
                ...state,
                isLoading: true,
                error: null
            };
        }
        case ActionTypes.LOAD_SUCCESS: {
            return {
                ...state,
                isLoading: false,
                error: null,
                jurisdictionConfigurations: action.payload.items
            };
        }
        case ActionTypes.LOAD_FAILURE: {
            return {
                ...state,
                isLoading: false,
                error: action.payload.error
            };
        }
        case ActionTypes.SAVE_REQUEST: {
            return {
                ...state,
                isLoading: true
            };
        }
        case ActionTypes.SAVE_SUCCESS: {
            return featureAdapter.addOne(action.payload.item, {
                ...state,
                isLoading: false,
                currentJobCodeRates: [],
                selectedJurisdictionConfiguration: null
            });
        }
        case ActionTypes.UPDATE_REQUEST: {
            return {
                ...state,
                isLoading: true
            };
        }
        case ActionTypes.UPDATE_SUCCESS: {
            return {
                ...state,
                isLoading: false,
                selectedJurisdictionConfiguration: action.payload.item
            };
        }
        case ActionTypes.DELETE_REQUEST: {
            return {
                ...state,
                isLoading: true,
                error: null
            };
        }
        case ActionTypes.DELETE_SUCCESS: {
            return featureAdapter.removeOne(action.payload.itemId, {
                ...state,
                isLoading: false,
                error: null,
                selectedJurisdictionConfiguration: null
            });
        }
        case ActionTypes.CHANGE_SELECTED_JURISDICTION_CONF_SUCCESS: {
            return {
                ...state,
               selectedJurisdictionConfiguration: action.payload.item
            };
        }
        case ActionTypes.FORM_CHANGED_ACTION: {
            return {
                ...state,
                formChanged: action.payload.changed
            };
        }
        case ActionTypes.JURISDICTION_CONF_REVERT_CHANGES_ACTION:
        case ActionTypes.JURISDICTION_CONF_RESET_ACTION: {
            return {
                ...state,
                formChanged: false,
                currentJobCodeRates: []
            };
        }
        case ActionTypes.LOAD_ALL_JURISDICTION_CONFIGURATIONS_SUCCESS: {
            return {
                ...state,
                isLoading: false,
                error: null,
                allJurisdictionConfigurations: action.payload.items
            };
        }
        case ActionTypes.SAVE_COPIED_JOB_CODES: {
            return {
                ...state,
                copiedJobCodes: action.payload.items
            };
        }
        case ActionTypes.SAVE_CURRENT_JOB_CODES_RATES: {
            return {
                ...state,
                currentJobCodeRates: action.payload.rates
            };
        }
        case ActionTypes.SET_VEH_EQ_TYPE: {
            return {
                ...state,
                selectedVehicleEquipmentLocationType: action.payload.type
            };
        }
        case ActionTypes.UPDATE_HAPPEND: {
            return {
                ...state,
                updateHappend: action.payload.updateHappend
            };
        }
        case ActionTypes.CREATION_HAPPEND: {
            return {
                ...state,
                creationHappend: action.payload.creationHappend
            };
        }
        case ActionTypes.CREATE_NEW_JURISDICTION_CONF: {
            return {
                ...state,
                createNewJurisConfCheck: action.payload.createNewJurisConfCheck
            };
        }
        default: {
            return state;
        }
    }
}

export const reducers: ActionReducerMap<State, Actions> = {
    main: mainReducer,
    rateHistoryGrid: createGridReducer(RATES_HISTORY_GRID_ID),
    jobCodeRatesRateGrid: createGridReducer(JOB_CODE_RATES_GRID_ID),
    tripChargesGrid: createGridReducer(TRIP_CHARGES_GRID_ID),
    vehicleEquipmentRatesGrid: createGridReducer(VEHICLE_EQUIPMENT_RATES_GRID_ID),
    markupsGrid: createGridReducer(MARKUP_GRID_ID),
    billCodeRatesGrid: createGridReducer(BILL_CODE_RATES_GRID_ID),
    billCodeRatesHistoryGrid: createGridReducer(BILL_CODE_RATES_HISTORY_GRID_ID),
    vehicleEquipmentCategoryMappersGrid: createGridReducer(VEHICLE_EQUIPMENT_CATEGORY_MAPPERS_GRID_ID),
    isListJobCodesEmpty: (state = false) => state,
};

const metaReducer = combineReducers(reducers);

export function featureReducer(state: State, action: Actions) {
    return metaReducer(state, action);
}