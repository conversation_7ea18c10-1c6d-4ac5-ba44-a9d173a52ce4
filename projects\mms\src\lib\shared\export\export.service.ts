import { Injectable } from '@angular/core';

import * as pdfMake from 'pdfmake/build/pdfmake';
import { DatePipe } from '@angular/common';
import { ReportsEnum } from '../../reports/reports.enum';

@Injectable({
    providedIn: 'root'
})
export class ExportService {

    constructor(public datepipe: DatePipe) {
        // pdfMake.vfs = pdfFonts.pdfMake.vfs;
    }

    public initExportToPdf(json: any, fileName: string, pdfHeader: string, reportName: string, additionalFields: any): void {
        this.exportToPdf(json, fileName, pdfHeader, reportName, additionalFields);
    }

    private exportToPdf(json: any, fileName: string, pdfHeader: string, reportName: string, additionalFields: any): void {
        const documentForDownload = this.populateContentForExportToPDF(json, pdfHeader, reportName, additionalFields);
        pdfMake.createPdf(documentForDownload).download(fileName);
    }

    public initExportGroupedDataToPdf(json: any, fileName: string, pdfHeader: string, reportName: string, filter?: any, name?: string[]) {
        return this.exportGroupedDataToPdf(json, fileName, pdfHeader, reportName, filter, name);
    }

    private exportGroupedDataToPdf(json: any, fileName: string, pdfHeader: string, reportName: string, filter?: any, name?: string[]): void {
        //json.sort(this.orderByProperty(['Region', 'Location', 'Type', 'SerialNo']));
        var documentForDownload;
        if (reportName === ReportsEnum.DispatchHistoryReport) {
            documentForDownload = this.populatePdfWithGroupedDataLandscape(json, pdfHeader, reportName);
        } else if (reportName === ReportsEnum.AssetsAndInventoriesReport) {
            documentForDownload = this.populatePdfWithAssetInventoriesReportData(json, pdfHeader, reportName, filter, name);
        }
        else if (reportName === ReportsEnum.LocationsReport) {
            documentForDownload = this.populatePdfLocationsDataLandscape(json, pdfHeader, reportName);
        }
        else {
            documentForDownload = this.populatePdfWithGroupedData(json, pdfHeader, reportName);
        }

         pdfMake.createPdf(documentForDownload).download(fileName);
     }
    
    public initPrintPdf(json: any, pdfHeader: string, reportName: string, additionalFields: any) {
        return this.printPdf(json, pdfHeader, reportName, additionalFields);
    }

    public initPrintGroupedDataPdf(json: any, pdfHeader: string, reportName: string, filter? : any, name?: string[]) {
        return this.printGroupedDataPdf(json, pdfHeader, reportName, filter, name);
    }

    private printPdf(json: any, pdfHeader: string, reportName: string, additionalFields: any): void {
        var documentForDownload = this.populateContentForExportToPDF(json, pdfHeader, reportName,additionalFields);
        pdfMake.createPdf(documentForDownload).print();
    }

    private printGroupedDataPdf(json: any, pdfHeader: string, reportName: string, filter?: any, name?: string[]): void {
        //json.sort(this.orderByProperty(['Region', 'Location', 'Type', 'SerialNo']));
        var documentForDownload;
        if(reportName === ReportsEnum.DispatchHistoryReport) {
            documentForDownload = this.populatePdfWithGroupedDataLandscape(json, pdfHeader, reportName);
        } else if (reportName === ReportsEnum.AssetsAndInventoriesReport) {
            documentForDownload = this.populatePdfWithAssetInventoriesReportData(json, pdfHeader, reportName, filter, name);
        }
        else {
            documentForDownload = this.populatePdfWithGroupedData(json, pdfHeader, reportName);
        }
        pdfMake.createPdf(documentForDownload).print();
    }

    private populateContentForExportToPDF(json: any, pdfHeader: string, reportName: string,  additionalFields: any, name?: string[]): any {
        var dataForExport = this.prepareDataForExportToPdf(json);
        dataForExport.shift();

        var docDefinition;

        if (reportName === ReportsEnum.UpcomingPM)
        {
            docDefinition = {
                pageMargins: [ 40, 80, 30, 60 ],
                header: {
                    image: 'data:image/png;base64,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',
                    width: 150,
                    margin: [40, 30],
                },
                footer: function(currentPage: number, pageCount: number) { return {
                    table: {
                        widths: '*',
                        body: [
                            [
                                { text: "Page " + currentPage.toString() + ' of ' + pageCount, alignment: 'center', style: 'tableExample', margin: 20, fontSize: 9, bold: true, color: 'grey'}
                            ]
                        ]
                    },
                    layout: 'noBorders',
                };
            },
                content: [
                    { text: pdfHeader, style: 'header' },
    
                    { text: reportName, style: 'subheader', alignment: 'left', color: '#3C4858', bold: false, margin: [0, 20, 0, 20] },
                    {
                        style: 'tableExample',
                        layout: {
                            paddingLeft: function (i: number, node: any) { return 10; },
                            paddingTop: function (i: number, node: any) { return 12; },
                            paddingRight: function (i: number, node: any) { return 10; },
                            paddingBottom: function (i: number, node: any) { return 0; },
                            defaultBorder:false
                        },
                        table: {
                            heights: 25,
                            lineHeight: 2,
                            body: dataForExport,
                            margin:[0, 0, 0, 10],
                            headerRows: 1,
                            widths: [230, 100, 100]
                        }
                    }
                    ],
                styles: {
                    header: {
                        fontSize: 0,
                        bold: true,
                        margin: [0, 5, 5, 10]
                    },
                    subheader: {
                        fontSize: 18,
                        bold: false,
                        margin: [0, 0, 0, 30],
                        color: '#3C4858'
                    },
                    tableExample: {
                        fontSize: 8,
                        color: 'black'
                    },
                    tableHeader2: {
                        bold: true,
                        fontSize: 9,
                        color: 'white',
                        fillColor: '#3C4858'
                    },
                    tableHeader3: {
                        bold: true,
                        fontSize: 8,
                        color: 'white',
                        fillColor: '#3C4858'
                    },
                    tableHeader: {
                        bold: true,
                        fontSize: 10,
                        fillColor: '#eeeeee'
                    },
                    dateRange: {
                        fontSize: 10,
                        margin: [0, 0, 0, 20]
                    }
                },
                pageOrientation: null
            }
        }
        else 
        {
            docDefinition = {
                pageMargins: [ 40, 80, 30, 60 ],
                header: {
                    image: 'data:image/png;base64,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',
                    width: 150,
                    margin: [40, 30],
                },
                footer: function(currentPage: number, pageCount: number) { return {
                    table: {
                        widths: '*',
                        body: [
                            [
                                { text: "Page " + currentPage.toString() + ' of ' + pageCount, alignment: 'center', style: 'tableExample', margin: 20, fontSize: 9, bold: true, color: 'grey'}
                            ]
                        ]
                    },
                    layout: 'noBorders',
                };
            },
                content: [
                    { text: pdfHeader, style: 'header' },
    
                    { text: reportName, style: 'subheader', alignment: 'left', color: '#3C4858', bold: false, margin: [0, 20, 0, 20] },
                    {
                        style: 'tableExample',
                        layout: {
                            paddingLeft: function (i: number, node: any) { return 10; },
                            paddingTop: function (i: number, node: any) { return 12; },
                            paddingRight: function (i: number, node: any) { return 10; },
                            paddingBottom: function (i: number, node: any) { return 0; },
                            defaultBorder:false
                        },
                        table: {
                            heights: 25,
                            lineHeight: 2,
                            body: dataForExport,
                            margin:[0, 0, 0, 10],
                            headerRows: 1,
                        }
                    }
                    ],
                styles: {
                    header: {
                        fontSize: 0,
                        bold: true,
                        margin: [0, 5, 5, 10]
                    },
                    subheader: {
                        fontSize: 18,
                        bold: false,
                        margin: [0, 0, 0, 30],
                        color: '#3C4858'
                    },
                    tableExample: {
                        fontSize: 8,
                        color: 'black'
                    },
                    tableHeader2: {
                        bold: true,
                        fontSize: 9,
                        color: 'white',
                        fillColor: '#3C4858'
                    },
                    tableHeader3: {
                        bold: true,
                        fontSize: 8,
                        color: 'white',
                        fillColor: '#3C4858'
                    },
                    tableHeader: {
                        bold: true,
                        fontSize: 10,
                        fillColor: '#eeeeee'
                    },
                    dateRange: {
                        fontSize: 10,
                        margin: [0, 0, 0, 20]
                    },
                    dateRangeBigger: {
                        fontSize: 12,
                        margin: [0, 0, 0, 20]
                    },
                    tableSubheader: {
                        fontSize: 12,
                        margin: [0, 0, 0, 5] 
                    }
                },
                pageOrientation: null
            }
        }

        if(reportName === ReportsEnum.MovedAssetsReport) {
            if (docDefinition.content[2]?.table?.body) {
                docDefinition.content[2].table.body.unshift([{text: 'NAME', style: 'tableHeader2'}, {text: 'TYPE', style: 'tableHeader2'}, {text: 'QUANTITY', style: 'tableHeader2'}, {text: 'FROM', style: 'tableHeader2'}, {text: 'TO', style: 'tableHeader2'}, {text: 'USER', style: 'tableHeader2'}, {text: 'TIMESTAMP', style: 'tableHeader2'}, {text: 'COMMENT', style: 'tableHeader2'}]);
                (docDefinition.content[2].table.body as any).widths = [70, 50, 50, 70, 70, 80, 100, 'auto'];    
                (docDefinition.content[2].table.body as any).heights = 40;
            }
        }
        else if (reportName === ReportsEnum.OutstandingPM) {
            if (docDefinition.content[2]?.table?.body) {
                docDefinition.content[2].table.body.unshift([{ text: 'NAME', style: 'tableHeader2' }, { text: 'TASKS COUNT', style: 'tableHeader2' }]);
                (docDefinition.content[2].table.body as any).widths = [120, 120];
                (docDefinition.content[2].table.body as any).heights = 40;
            }
        }
        else if (reportName === ReportsEnum.MultipleIssuesLocationsReport) {
            if (docDefinition.content[2]?.table?.body) {
                docDefinition.content[2].table.body.unshift([{text: '', style: 'tableHeader2'}, {text: 'LOCATION', style: 'tableHeader2'}, {text: 'INCIDENT COUNT', style: 'tableHeader2'}]);
                (docDefinition.content[2].table.body as any).heights = 40;
            }

            if (additionalFields[0] || additionalFields[1]) {
                docDefinition.content.splice(2, 0, {text: 'Date Range: ' + additionalFields[0] + ' - ' + additionalFields[1], style:'dateRange'});
            }
            else {
                docDefinition.content.splice(2, 0, {text: 'Date Range: All tasks are shown.', style:'dateRange'});
            }

            if (docDefinition.content[3]?.table?.body) {
                (docDefinition.content[3].table.body as any).width = 530;
                (docDefinition.content[3].table.body as any).widths = [130, 200, 200];
            }   

        } else if (reportName === ReportsEnum.AssetsAndInventoriesReport) {
            docDefinition.pageMargins = [10, 60, 10, 10 ];
            (docDefinition as any).pageOrientation = 'landscape'
        } else if (reportName === ReportsEnum.PmScheduledReport) {
            docDefinition.content.splice(2, 0, {text: additionalFields, style:'dateRange'});

            if(docDefinition.content[3].table?.body.length > 0 && docDefinition.content[3].table?.body[0].length === 8){
                docDefinition.content[3].table?.body.unshift([{text: 'SCHEDULED', style: 'tableHeader3'}, {text: 'LOCATION', style: 'tableHeader3'},  {text: 'TASK#', style: 'tableHeader3'},{text: 'CHECKLIST', style: 'tableHeader3'},{text: 'CHECKLIST ITEM AND RESPONSE', style: 'tableHeader3'}, {text: 'TECH', style: 'tableHeader3'},{text: 'START', style: 'tableHeader3'},{text: 'END', style: 'tableHeader3'}]);

                for(let i = 1; i < docDefinition.content[3].table.body.length; i++) {
                    let item = docDefinition.content[3].table.body[i];
                    let item4Data = item[4].split('|');
                    let labelsArray = item4Data[0].split('%');
                    let responsesArray = item4Data[1].split('%');
                    labelsArray.pop();
                    responsesArray.pop();
                    item[4] = {
                        table: {
                            widths: [40, 'auto'],
                            body: []
                        },
                        layout: 'noBorders'
                    };

                    for(let i = 0; i < labelsArray.length; i++) {
                        let bodyItem = [];
                        bodyItem.push(labelsArray[i]);
                        bodyItem.push(responsesArray[i]);
                        item[4].table.body.push(bodyItem);
                    }
                }
            } else if (docDefinition.content[3].table?.body.length > 0 && docDefinition.content[3].table?.body[0].length === 7) {
                docDefinition.content[3].table?.body.unshift([{text: 'SCHEDULED', style: 'tableHeader2'}, {text: 'LOCATION', style: 'tableHeader2'},  {text: 'TASK#', style: 'tableHeader2'},{text: 'CHECKLIST', style: 'tableHeader2'},{text: 'COMPLETED', style: 'tableHeader2'}, {text: 'START', style: 'tableHeader2'},{text: 'END', style: 'tableHeader2'}]);
            }
        }
        else if (reportName === ReportsEnum.SparePartsReport) {
            docDefinition.content.splice(2, 0, {text: additionalFields, style:'dateRange'});

            if (docDefinition.content[3].table?.body.length > 0) {
                docDefinition.content[3].table?.body.unshift([{text: 'LOCATION', style: 'tableHeader2'}, {text: 'PART NO', style: 'tableHeader2'},  {text: 'NAME', style: 'tableHeader2'},{text: 'UNITS', style: 'tableHeader2'},{text: 'REORDER POINT', style: 'tableHeader2'}, {text: 'ON HAND', style: 'tableHeader2'},{text: 'UNIT COST', style: 'tableHeader2'},{text: 'TOTAL COST', style: 'tableHeader2'}]);
            }
        }
        else if (reportName === ReportsEnum.UpcomingPM) {
            docDefinition.content.splice(2, 0, {text: additionalFields, style:'dateRange'});

            if (docDefinition.content[3].table?.body.length > 0) {
                docDefinition.content[3].table?.body.unshift([{text: 'PM Schedule', style: 'tableHeader2'}, {text: 'Last Completed Date', style: 'tableHeader2'},  {text: 'Next Scheduled Completion Date', style: 'tableHeader2'}]);
            }
        }
        else if(reportName === ReportsEnum.WoCallReport){
            var dataForMetrics = this.prepareDataForExportToPdf(additionalFields[2]);
            dataForMetrics.shift();

            docDefinition.content.splice(2, 0, {text: 'Created for period from: ' + additionalFields[0] + ' to ' + additionalFields[1], style:'dateRangeBigger'});
            docDefinition.content.splice(3, 0, {text: "Call Ticket Metrics:", style:'tableSubheader'});

            docDefinition.content.splice(4, 0,
            {
                style: 'tableExample',
                layout: {
                    paddingLeft: function (i, node) { return 10; },
                    paddingTop: function (i, node) { return 12; },
                    paddingRight: function (i, node) { return 10; },
                    paddingBottom: function (i, node) { return 0; },
                    defaultBorder:false
                },
                table: {
                    heights: 25,
                    lineHeight: 2,
                    body: dataForMetrics,
                    margin:[0, 0, 0, 10],
                    headerRows: 1
                }
            });

            if (docDefinition.content[4].table?.body.length > 0) {
                docDefinition.content[4].table?.body.unshift([{text: 'METRIC', style: 'tableHeader3'}, {text: 'RESULT', style: 'tableHeader3'}, {text: 'TARGET', style: 'tableHeader3'}, {text: 'VARIANCE', style: 'tableHeader3'}]);
            }
          
            if (docDefinition.content[5].table?.body.length > 0) {
                docDefinition.content[5].table?.body.unshift([{text: 'PROJECT', style: 'tableHeader3'}, {text: 'TASK #', style: 'tableHeader3'},  {text: 'LOCATION', style: 'tableHeader3'},{text: 'DESCRIPTION', style: 'tableHeader3'},{text: 'TYPE', style: 'tableHeader3'}, {text: 'LABOR REPORT', style: 'tableHeader3'},{text: 'ENROUTE', style: 'tableHeader3'},{text: 'ONSITE', style: 'tableHeader3'},{text: 'COMPLETE', style: 'tableHeader3'},{text: 'ECONOLITE LABOR', style: 'tableHeader3'},{text: 'OTHER COSTS', style: 'tableHeader3'},{text: 'TOTAL COST', style: 'tableHeader3'}]);
            }

            docDefinition.pageMargins = [20, 80, 10, 10 ];
            (docDefinition as any).pageOrientation = 'landscape'
        }
        else if (reportName === ReportsEnum.PastDueRepairsReport) {
            docDefinition.content.splice(2, 0, {text: additionalFields, style:'dateRange'});

            if (docDefinition.content[3].table?.body.length > 0) {
                docDefinition.content[3].table?.body.unshift([{ text: 'Project', style: 'tableHeader2' },
                { text: 'Task#', style: 'tableHeader2' },
                { text: 'Location', style: 'tableHeader2' },
                { text: 'Description', style: 'tableHeader2' },
                { text: 'Status', style: 'tableHeader2' },
                { text: 'Priority', style: 'tableHeader2' },
                { text: 'Days Past Due', style: 'tableHeader2' },
                { text: 'Planned End Date', style: 'tableHeader2' },
                { text: 'Work Performed', style: 'tableHeader2' }
                ]);
            }
        }
        
        return docDefinition;
    }

    populatePdfWithGroupedData(jsonData: any, pdfHeader: string, reportName: string): any {
                    var doc = this.populateContentForExportToPDF(jsonData,pdfHeader,reportName,null);
                    var lastColX: any = null;
                    var lastColY: any = null;
                    var bod = []; 

                    var inServiceTotal = jsonData.reduce((sum: number, x: any) => sum + Number(x['In Service']), 0);
                    var outOfServiceTotal = jsonData.reduce((sum: number, x: any) => sum + Number(x['Out of Service']), 0);
                    var totalQuantity = inServiceTotal + outOfServiceTotal; 
                    var outOfServicePercentTotal = (((outOfServiceTotal*100)/totalQuantity).toFixed(2)).toString() + ' %';

                    if (reportName === ReportsEnum.PmComingDueReport) {
                        bod.push([ {text: 'REGION', style: 'tableHeader2'}, {text: 'LOCATION', style: 'tableHeader2'}, {text: 'NAME', style: 'tableHeader2'}, {text: 'USER NAME ', style: 'tableHeader2'}, {text: 'LAST MODIFIED', style: 'tableHeader2'}, {text: 'DUE DATE', style: 'tableHeader2'}, {text: 'DAYS UNTIL DUE', style: 'tableHeader2'}]);
                        doc.content[2].table.widths = [70, 100, 'auto', 'auto', 'auto', 'auto', 70];
                        doc.content[2].table.heights = 40;

                    }
                    else {
                        bod.push([ {text: 'REGION', style: 'tableHeader2'}, {text: 'LOCATION', style: 'tableHeader2'}, {text: 'TYPE', style: 'tableHeader2'}, {text: 'IN SERVICE', style: 'tableHeader2'}, {text: 'OUT OF SERVICE', style: 'tableHeader2'}, {text: 'TOTAL', style: 'tableHeader2'}, {text: 'OUT OF SERVICE (%)', style: 'tableHeader2'}]);
                        doc.content[2].table.widths = [70, 100, 70, 'auto', 'auto', 'auto', 70];
                        doc.content[2].table.heights = 40;
                    }

                    //Loop over all lines in the table
                    doc.content[2].table.body.forEach(function(line: any, i: number){
                        //Group based on first column (ignore empty cells)
                        const isDifferentRegion = lastColX != line[0] && line[0] != '';
                        if(isDifferentRegion){
                            //Add line with group header
                                bod.push([{text:line[0], style:'tableHeader'},{text:'', style:'tableHeader'},{text:'', style:'tableHeader'},{text:'', style:'tableHeader'},{text:'', style:'tableHeader'},{text:'', style:'tableHeader'},{text:'', style:'tableHeader'}]);
                                doc.content[2].table.heights = 25;
                            //Update last
                            lastColX=line[0];
                        }
                        //Group based on second column (ignore empty cells) with different styling
                        if(isDifferentRegion || lastColY != line[1] && line[1] != ''){
                            //Add line with group header
                                bod.push([{text:'', style:'tableSubheader'},{text:line[1], style:'tableSubheader'},{text:'', style:'tableSubheader'},{text:'', style:'tableSubheader'},{text:'', style:'tableSubheader'},{text:'', style:'tableSubheader'},{text:'', style:'tableSubheader'}]);
                                doc.content[2].table.heights = 40;
                            //Update last
                            lastColY=line[1];
                        }
                        //Add line with data except grouped data
                        if( i < doc.content[2].table.body.length){
                            if (reportName === ReportsEnum.PmComingDueReport) {
                                bod.push(
                                    ['','',{text:line[2], style:'defaultStyle'},
                                                {text:line[3], style:'defaultStyle'},
                                                {text:line[4], style:'defaultStyle'},
                                                {text:line[5], style:'defaultStyle'},
                                                {text:line[6], style:'defaultStyle'}]);
                                                doc.content[2].table.heights = 25;   
                            }
                            else {
                                bod.push(
                                    ['','',{text:line[2], style:'defaultStyle'},
                                                {text:line[3] === '' ? 0 : line[3], style:'defaultStyle'},
                                                {text:line[4] === '' ? 0 : line[4], style:'defaultStyle'},
                                                {text:line[5] === '' ? 0 : line[5], style:'defaultStyle'},
                                                {text:line[6] === '' ? 0 : line[6], style:'defaultStyle'}]);
                                doc.content[2].table.heights = 25;      
                            }
                        }                       
                    });
                    //Overwrite the old table body with the new one.
                    doc.content[2].table.headerRows = 1;
                    doc.content[2].table.dontBreakRows = true;
                    doc.content[2].table.body = bod;
                    if(reportName === ReportsEnum.AssetsCountReport) {
                        doc.content[2].table.body.push([{text:'TOTAL', style:'tableHeader2'}, {text: '', style:'tableHeader2' }, {text: '', style:'tableHeader2' }, {text: inServiceTotal, style:'tableHeader2'}, {text: outOfServiceTotal, style:'tableHeader2'}, {text: totalQuantity, style:'tableHeader2'}, {text: outOfServicePercentTotal, style:'tableHeader2'}]);
                    }
                    doc.content[2].layout = 'noBorders';
                     
                    doc.styles = {
                        header: {
                            fontSize: 0,
                            bold: true,
                            margin: [0, 5, 5, 10]
                        },
                        subheader: {
                            fontSize: 16,
                            bold: true,
                            margin: [0, 0, 0, 30]
                        },
                        tableSubheader: {
                            fontSize: 11,
                            bold: false,
                            color: '#3C4858',
                            margin: [10, 8, 0, 7],
                            fillColor: '#f8f9fa'
                        },
                        tableHeader: {
                            bold: true,
                            fontSize: 11,
                            color: '#3C4858',
                            margin: [10, 10, 0, 10],
                            fillColor: '#f1f1f9'
                        },
                        tableHeader2: {
                            bold: true,
                            fontSize: 9,
                            color: 'white',
                            fillColor: '#3C4858',
                            margin: 10
                        },
                        defaultStyle: {
                        fontSize: 9,
                        color: 'black',
                        margin: [10, 6, 10, 6]
                        }
                    }

                    return doc;
                }
            
    
    private populateContentForExportToPDFLandscape(json: any, pdfHeader: string, reportName: string,  additionalFields: any): any {
        var dataForExport = this.prepareDataForExportToPdf(json);
        dataForExport.shift();
        var docDefinition = {
            pageMargins: [ 40, 80, 30, 60 ],
            pageOrientation: 'landscape',
            header: {
                image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAABkCAYAAAA8AQ3AAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKE1hY2ludG9zaCkiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NTQ2MzA1NkY1OTQyMTFFOThFNDBEREVCM0Y0REI2OTkiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NTQ2MzA1NzA1OTQyMTFFOThFNDBEREVCM0Y0REI2OTkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo1NDYzMDU2RDU5NDIxMUU5OEU0MERERUIzRjREQjY5OSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo1NDYzMDU2RTU5NDIxMUU5OEU0MERERUIzRjREQjY5OSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PoOzn2IAABeSSURBVHja7F0JlFxVmf47+0LI0llJ0mlISEjAuGFiCISIoCaBAVFARhQYFGWGiMJMlJkoqMPMIMgighwcBZQR0DgYtoSdTIBEQsImiUDMQvals3U2svXcb+p7U886VfX+t1W9Sv7vnP90d/WrV6/uu/e7//7qWlpaxGAwGGoBdUZYBoPBCMtgMBiMsAwGgxGWwWAwGGEZDAaDEZbBYDDCMhgMBiMsg8FgMMIyGAxGWAaDwWCEZTAYDEZYBoPBCMtgMBiMsAwGg8EIy2AwGGEZDAaDEZbBYDBUgLBOmnB+Gtc1zMkxTj7gZKCTLk4O8H+tnWx3ssbJ207+7OQNJ3vtdhoMtYXZM+4PdXybjF3/CCcnODnVyVBK5xLH7nGyzMlfnLzk5EUns3zEZjAYDjJkgbA6OrnYyXlOxoV4XzsfqU3ga0ucTHNyj5NFdnsNBiOsJHGlk2/S7EsCRzmZQrnLybU0HQ3xcLKT4U52lPh/JyeLnTxjQ2U4GAlrlJM7nHw0xc+41Mk5Ti538hu71bHwPSenBBzzipOP2VAZ0kSrKnzm15z8MWWy8tDdyX85ucFudSw0K47ZYsNkONgIa6qTO6vwPf+xSp97sGBfQscYDDVDWPAr/bCK3xWa3bV2yw2GVDHSyXQn70kuin+Pkz5JnbxSPqwznFwf4X0Lncx18q7kcq8ApDkMdnKckzEhz3eNkycllwZhMBiSxZed3OvkHSe/k1zO5BecXCg5v/W8WiCsvk5+H/I9OP42yeVVlQMSS/9OcpFGLeCAP9KJpfgbDMnhWJIV1i5yIuGj3i25wNdNTl5w0kNKR5ozYxL+3Elb5bFIBv1bJ59XkBXwppNvSS469Z7yMwaR5Ax67FccYwm7hzau4/rF2r3KyUonO53MdDJZcnmTX826hoVE0NOVx+LLnejk1Qif8wpVzteo0QUBTvhfRPicw7hz9HfSz0lv34LeILmcr+VOFjh5v8ITpsHJ8byuAby3uIYVnDzY9aJG8jSEVUmn+xAnH3RSL7kcvqeVG5wH5JSh9Ks7x6ub5Eq72lADWM2xWsVNsTml79Ga32MIrwNzt46KxAbeN/yc72RzCp/fkZ/fwLncj9fUitrRWiebOIde97llSq31p3x/9+ecWC+5JG6MIRK8b8kyYV0X4tizIpKVh3U8x1zFscdQK5unHKNzuHOcoCTElVSBf+vkoRDf4V95U0upzfDf3VFAtmdLLudsHCdgKWx08oSTW8t8b0zU+7iAdlNr2stJHQRsNs/wHK0LSP5efi4w3sl/cDIX08raO1lKf8ge3+vQ0i+jBv7RgrnboiCsT3OsPsHvVxdiXmGxPubkQf4dFxM4p3AtjYrjN3IzhjP7Af4dB+dzPn/cyRHK96ylYvAw/VNbiigc3X1jdgF/70Kiw9zdFnfg0ix+hk37J+Vp/jMJdZHAgJ6hOO5myWXalwMii9+WnM8rKrBD/xsnWhBeJCmWwzROdky0e5ycFuGaQB7fLLHjb5XS9ZtR8WsSEDDJyaOK9wwk8QOf4TUPLaMx/7jE/6DhX60YVw0wNj9x8n2l1lnsWv6FRBEV0Hh+6uTfuamEwZc4FsNjjsMGjsN1kvcF/5jrCffol05udHIF5+tcaolncn3+P7JU/PzFEKbgVQl+7mXc6XeX8at0JpGUM6+gxZyawPUgMIC78ln6zso5Hd9SLKzF3MnmK7W9YsBEQvi5WPb6qjLEEGeReVih9Id59+4rkvODlkOpXRfO3m8l+D26OvkuN8SzaP5r0Jbf4cIErgGOa1QeIPqGGlxtxBtKwSUJjUMvyaUofU5yNcCICv6Amv4CbtDQBmdTu76dpPVwln1Yk5THTUtCVSxYcLfGeP9HaIv3SHg8zpVc25zxZXxJmnEY7WRGDLLy8AlqPl+qsK9NM+da0Y/0cQVZlcJdCWrthfgQTf6RCt9SF26gSZctDaVGjnX2eMCx94VQIMKOw8tOPkwzHj7UJ0nql9Okb+R1JrH5pxYlbOTN1OD3kh0M567QI6Xzwxf0P1I6atpGSTSjE7qeC2huZQ3w0XyKvpIo+IcUycrDAGoP5dCR8ynNGkv41saW+f8lKZGVX+t8gu4E9KcbRNPwWSfPS85veGIE87WihKW9QTAH52RkkSDsihBsp5Dv2xPBRPx1hsjh+iIaQdLoGPL4A/TTDIjwWd2ktD+rGPZxocGhjEaQYSKCMA3HBJDJBytwDx8mcRSig+QCHBoso3b039TYXhZ95PdoyfkRPdxJzR0m60NJftG0TMIhyuPQfG9DRhYuFkiD4jiovg9w94DZsouTBa1tTlf6KWD3382dKQkgnWIhyRMR0DBBAmjCozhBhSZOH07WFpJHe8XmdsBH3q0KNoKdIb9Pb8mnjIQlxEt5vUHYRNPlCS5Wz4nejyT0XZo8QbiwxKZ7ObVhLUCYr1G77EYS+KTyvbAIbiyiVUJ77qm4b9+g6b2nyDoG6fyz4hqu4DXsT3ORpkVY2sn2XkbIapjShLiD6m6xHCs4G+GPu4U/Bwec61aSSxwgPeEamh3bfQQxhpPneOV5PkvC2s9F0l7yaQctnMxB+XTPSz6UXUhYm1O8d7hOv0/wHOV74B97twT5Q8uYzjH5SMC5xvL7+gM8PUNoeXBGT+E9LATmBxzsmj7kCE7A8e0PaoxRzunbS/wPAR5ENZHS8JOA8/QjQT5Wi4SlLXZcmxHCmqo45nf0jQQBu+Q4LoZOAST56RhaFpIlTythos6iWY5dW9PGZ2TAPdmqOEezpN8scRvH6yluduv5uSt987kttd5iWl0dTd4bS5CVH/tJFkEpGI3UiPyRUC+zO+o99IDnFSDvrIkaWxC+ymv2oIn2arTR2+jKaCgzrpjrqT9XIS3C0ubxbM8AWXWliVYO0Ki+HuKcMBVv5u5UDhdEJKwtoss1Q+7TW4rjYM62LqPOd0ho4sfBDdRayiVuQhucQNLaU+R/3nVqNfvZfF+5JFMs1PoCwrpIaZKeqbyOydSC+yvM0+8V+c5BJIf7/kspn0h9aRY0i7QIS+usq8/AGEyU4FrHaQUTUgMQ1tUBvh/P/ApbxgMVXhN1gV9rvkLL6kKNYJdkD/tpjs5UHqvV8tqRiDuXWNi76UcKIqxWBRv0GNH5Qm+RcL69v5HyVRCdimjHS5Xn/jrFCz5g3uD5CItI7puzMhnSIqwm5XF9MzAGmgzoRvoZOijPCcI+jH6NVgF2/3CakWEQxk/wooKw9kl2u1ecoSSrchjIMYDfahAFfqbDeZ9aSpjWmmCDFGimWkf5gyG/wwJKGISNwA+jFK5laOmvcy7NkSr6ntMiLG2t0xEZWBBHKY4ZK+VzXeJgaEjC2kjfhhabpHaB7OwZMbVnmDynkpgqgcGKY+BzW1yBa3lEclH4XjHOAStoHGUytcLnJJea82ClJ0RaeVhrQpBFtUmrX5U/vyHk8WtDquita5Ss9hf4Y8LgaBIdNNGzKkhWnjYXhEVSmXY8cDUk3UoJpiey65Hag7y1LxwMhPWm8jiYWKOruCjgu+pR5YUZ9vPDNkCrk9rE0xIt6jiaplO1Mvg1ftltFbweRDm/ltK5vTrZ+2qdsNAmRuvAnVTFRVEn1XlykB/tQh6/Uw4NPBeRLJ6psEYVZYOo9CaCukqUx6RVVYLSn5mV+CJp+bCQt4OEOE2mL1TKKyRm69QCrW0U1eEDJSYL1Fpk2a+gjT+wihM8bGlPrZp4YRHFsYvoaZjWOM2cq5s4/9r4zFFETuH76R3yGjTmevcqjKfXugh9sC6SXK1m2wTPj5xCdMa4uRYJC3hESViYYAj/T03oc1Eb9w3FcchRguNQE9FEkevdEr7OMGiXRUnJy2JIgrCQrHye8tibOD8XkqhKbZYjJNfTLYxGtFpxzAiSxd4qjOs0yiCSzGhKg8SvI8UavkNS7LabJmHBrr1BqRGgVukeiR85QWLd5cpjn+VPZDwHNcFDouYs45CySDotImxb4k8pzWks0hdCXENY822JklyPk3gdduNiOU3Fu/g3UoyGkbhAZijaRsTzwyHOCX/s8dTmao6wYGohe1ZTo4dJMYODEyf7HQ5AjU/qD5LrmwW8otTGpkQw3w4lJF30GtZcGabcRF8Icc7xEa77FeVxXwxJWDBPEfHcXWJzaMu5/wfR50H6sVaKl2Udy81gqugCRI21SljAD0Tfl2iI5BuSrYzwWcjZOUl5rP/R9ZrSGPgcrhVd1boH+MU+R3Oj1ASDw/1XkqFM4hLQaMnVrlrQLKawGnyUnlrP0SQKKlVCZ9wfSa4eUgO4JSYojpvOn+fS9Cy1yWLuvUaCK4e3KEgRmaMY555p3uS0CWslb8oU5fEjOYjfpomo2bVRTQ+/lbajIaJILxX4HJ6gqVAO8LO9SS0uCJisTyl2fUzsX0j20ay8d50lueBJWGi033EFm1U5/H2IDdAPpCygP1VQ1wj4Q9F3aqzC53OlkqygQXpJ29DGNF0e+omuCcE7XJunKL5/aqhESP87/LJa1FNbgkMUjfbPpFp6FG3qYRy0y3jD50u49qvFdk1tZAMPYb1VykcVcS0LlCbKlZKNAnCN7ycInbkLo5buMJL2YNE9FSYJrFIcg5rEoI4bKNdBT/LbY1yLdj6hXOh1Kd2650heh7ZVjf8RWncp34N8t6MVx6EiQ9MfbHmaN7kST372KugXSbico6EkOw/I66qjeRI1HItds1hBKDSs2codFRHIS6mpIdMXvjpE++CoHKW8qZ72+TOpDWjLe06m9rqGGg+qGBCJvaQC16j1HaFR40SaQot95lsj7x0ijX1iXgtMp0dF90xObGxexPJVajvduEGfGGKuz+Tm7eF5yaXuBJUKeU+3+hnXwevcoLxgQwM34asV5mBziPuQWcIClpC0nolxjo4xr+GnAQRxAW+wZkyQ6zVJ4iW9ni/ZLTguxLyQx/vLnSqVNzaLi11TUD+RkiYu5qakbbszghIFSI/4SpHXETHX1GJCkbiCstNnotaRPLW4/2AwCT0gjeCTkt5TdIPIanLAMcj7ObtC1/NPEi5aVW08LdFzayr1BGwkCX+/SuNTrJ0SfElnVOjzzyphEkPrui3kueBb604JQ1bwXU5N+4tWuiwFpHV82mpjEXKYrDz2EdE5KuMAkdMbpbbQLOEe7FAt3CnFWw2nbQ6XylxH4OXclDVpmLCPB7gwKhHYOVMq8HyGatTRwQGP9r3XSroOZ5ifoyKQwwP0xSxOYeKjDOkaqU2ge+pLEd5X6VIiuB7mJ3QudPnUPKW6nCaC1trow/WnFNYRfK6/VRwLc3GKpJNZjyj7KRLP3ZNpwvIA9R0Ov5tEV86gBfJgkP8ER+G8iOfAswMRpr9O9L29SmGn5HtiB/UP0uSwhG2Ho6lba1DOhXHUYsK0Riks99D6dKK2XIZpAmf13THu2UK6L+CY17Q/Cqpf9B42+p0E5jrytn7I+RnGrYB0DmTX3yv6jsDlgOqPW3jO5ypFGqGd7idNOB+LCs7B3bNn3B+3Dg5+o6toJsHeR0YtCjQHhxy4uZTHEjQ3d9Emx01BTg3alSBnRts+ZA5NAtRtaUO9mIADpHQuU2cJX3s4j+fdVoYY4P/Q5LzhmMs4JmjZixy4RmoYmBfwVzVx7LAw4XS+v4hvZyYJcl+JOdkS07xANjj6QOFpP0hjQbQuqIldM8dpGsnOM+OQiNmuhDXg1YNqEn/xXa8n4WNDnUQNSdNcr4mm7gyJ1q7br5VdxI0YJtxptHa0xdhruM6e5LiskQqjrqWlJSxhwSb+ESf6zzl5FyZMoiAsPOJoEG9oa8m3Gz7ACYLBeptksLFC4+U9Lw6CKn6Eeb0i1k0UmJLvSnaet1ipje9wLsptGby+rtQE0I66r+T7uG/hfXqH921dFa7rSM71vkXm03rOpcWSXjVEPdfbEG46PSSffrSZY4S1tpRrLVE3jlN6Uies/rzx8GmM58t4uisSKq1A2GAwZIewCshrAn1R3qPp59Bf86BUpgWswWAwwgpFWkL/Dh7t7T2BZhXNxV+J/lFDBkNW0Iouj3KbLhaOde+oUcLygAJipPCf7HvN6/n8uN0eQ8pADWNXCn6Hrwo+mW4U77FecJR34GvwF/Xk6/DBIbDQy/d30XVDMltP0mrLY73OC/DjIfCAIMRWCvysmylw8MM3BD/V9kP5hlWbsDwgLwPOef+TbeE4RI4T8lLesLVlCAGkRiC9oA9JBw5qpHcgolrvIyX87M5jso4dJDFIE8lrBS2SlXwNTu41chC7V7JCWB6QuIkndlwsf925EeF+5LhMF12VveHgRRtqOCCfgfyJvxslFyX2SKiPJNuDvBawlVqbP1oIMvszf2+q9S+YNcLygMl3oeRyQBp9r0NtRk4HsolnSrTGfYbso54kBM3oGJLSET5S6lUlrWgvCXMbNR6E83f7zLQw7ZEPSD69w0N3niMNooVGhjxGZNCjuQC6LCyklrajViZGVgnLA27cOSSvwh7cyAhHev/jJLElts5rCoeRgEBGyOk5mr/349+9UvxsEIznH9pCAmr2mVqbqa14viXMtT00x9pK3q/Unv+PuuD93Q1a+J3hwO9EIsP54VfrKvkC43ofYXclsddL9CfreCQG8kLSsNcx1AgrAmH5gSzp8yiDCv4HB+Zcmo4ok/mj6J9zaEgPnaglDaeGhKTHETTXBlCSBEhkAzXvdSSjdXQjrCUZbZW/dmbXOtrTDPZICwnK/blG6n3j3lf0z18Eeb3NtYSuuS9nZaxqibA8QA2fSM1rohQvJF3LwX6RP9EM8H3jj1TQkTs+tKWhXCioNTyWPqZuEv7hr8XwPgkHBLSUpgx+X83fV/D/28TSBwrRxkdm3pNuBvJ+DaFW27XM+0H887meUI70qlSuWqTmCcuPniQtVMmfJqWLSpdJrnPAAg72G9Ua8BompUGc2F7lQgN/9yZ8Es0dN9JE8UjpL9x8oCEt5++77XYkjl40yaGJoezmQ7y/I6R4I8x9tGZg1aBaZU6lNolaJyw/oPJOIIGNl/KdDJpopy+kuruUqm/TITxpu3PCNnACD+Yk7kcZJMk8Mh0m22KSEELwXgTL05Y2GH9kBgM4D7BBoZ88AiCjimjMMBdRaP0YLZplRljhFx86JeBJ0mgbM1LxniYumDe5gFbQlFzBnX9njU46OHEP55j0pSnQn4TeQJLqI/kOCnGxj+O1in6QpT5iWi75om9DbaIPzf+PUQMbR/PfDxSHo0vEI9TCjLBC4lgO7BjuFGF6YcN3sp6mynJqAJ6ZspK7ixd18qJKafsjvKxskEwn/t6XmpFXQV9Pn0U//uwmyfbkX85xWUlNdTXH6B2OzQ5b24cMepPAsLZOl3ytsLd+kPyNJ0g9KzF9yYcKYRUCA4tHa59AdRcRyA4xzndA8hEoj7R2UNvYIPnSjLW09VuX0U66cALso3bkhbo78xq9kLdXVpIWDpCM3qOGBH/Su9Q48T2WGCkZSgBzFQ0IT6abZixfRxkT8idRejc9yuZ+qBJWIWAaDaF9PohklnYuULWxk9rhKpptq6kxLSE5NZGYttv6M8REO1o3Z5PERnBzh7mITi0PaTc/I6zS8BIbvYgYiAwh4QE0uXpLdVtGl0KLT9vbQtLxunlu9Jm3K2nG7rT1ZKgwelDzgvl4HM1E+I7h83pUykQcjbCioQuJy+siCukjeR/S4ZKv7gepwZ/kdYb0ugAUazHcmloPiMTzNzXxfbv5v13UerbR3Nws+fyjLfx7Hd/XYmvDUANA6+fPUDnAXEewBlUss+ISVhsb2/8DiGMRRYNOHLu9JCsQ3r4ShLWD5AOiq5ODIxvbYChr6Un+cWte5BGleKMl5zdF7uSyKCc2wooGv9m1SyrfC9xgqBUslPwzH46SXC4g8r+6S77sygjLYDBkDksojbRSQvf5iu3DMhgMhkrBCMtgMBhhGQwGgxGWwWAwwjIYDAYjLIPBYDDCMhgMRlgGg8FghGUwGAxGWAaDwQjLYDAYjLAMBoPBCMtgMBhhGQwGgxGWwWAwGGEZDAYjLIPBYDDCMhgMBgX+V4ABACEE72PpQSLMAAAAAElFTkSuQmCC',
                width: 150,
                margin: [40, 30],
            },
            footer: function(currentPage: number, pageCount: number) { return {
                table: {
                    widths: '*',
                    body: [
                        [
                            { text: "Page " + currentPage.toString() + ' of ' + pageCount, alignment: 'center', style: 'tableExample', margin: 20, fontSize: 9, bold: true, color: 'grey'}
                        ]
                    ]
                },
                layout: 'noBorders',
            };
        },
            content: [
                { text: pdfHeader, style: 'header' },

                { text: reportName, style: 'subheader', alignment: 'left', color: '#3C4858', bold: false, margin: [0, 20, 0, 20] },
                {
                    style: 'tableExample',
                                            layout: {
                            paddingLeft: function (i: number, node: any) { return 5; },
                            paddingTop: function (i: number, node: any) { return 12; },
                            paddingRight: function (i: number, node: any) { return 5; },
                            paddingBottom: function (i: number, node: any) { return 0; },
                            defaultBorder:false
                        },
                    table: {
                        heights: 25,
                        lineHeight: 2,
                        body: dataForExport,
                        margin:[0, 0, 0, 5],
                        headerRows: 1
                    }
                }
                ],
            styles: {
                header: {
                    fontSize: 0,
                    bold: true,
                    margin: [0, 5, 5, 0]
                },
                subheader: {
                    fontSize: 18,
                    bold: false,
                    margin: [0, 0, 0, 0],
                    color: '#3C4858'
                },
                tableExample: {
                    fontSize: 8,
                    color: 'black'
                },
                tableHeader2: {
                    bold: true,
                    fontSize: 9,
                    color: 'white',
                    fillColor: '#3C4858'
                },
                tableHeader: {
                    bold: true,
                    fontSize: 10,
                    fillColor: '#eeeeee'
                },
                dateRange: {
                    fontSize: 10,
                    margin: [0, 0, 0, 0]
                }
            }
        }
        return docDefinition;
    }

    populatePdfWithGroupedDataLandscape(jsonData: any, pdfHeader: string, reportName: string): any {
        var doc = this.populateContentForExportToPDFLandscape(jsonData,pdfHeader,reportName,null);
        var lastColX=null;
        var lastColY=null;
        var bod = []; 

        doc.content[2].table.widths = [80, 50, 50, 50, 50, 90, 60, 50, 70, 60, 60];
        doc.content[2].table.heights = 40;

        doc.content[2].table.body.shift();                     

        //Loop over all lines in the table  
        doc.content[2].table.body.forEach(function(line: string, i: any){
            //Removed by MMS3-5819
            //Group based on first column (ignore empty cells)
           /* if(lastColX != line[0] && line[0] != ''){
                //Add line with group header
               bod.push([{text:line[0], style:'tableHeader'},{text:'', style:'tableHeader'},{text:'', style:'tableHeader'},{text:'', style:'tableHeader'},{text:'', style:'tableHeader'},{text:'', style:'tableHeader'},
                            {text:'', style:'tableHeader'},{text:'', style:'tableHeader'},{text:'', style:'tableHeader'},{text:'', style:'tableHeader'}, {text:'', style:'tableHeader'}, {text:'', style:'tableHeader'}]);
                    doc.content[2].table.heights = 25;
                //Update last
                lastColX=line[0];
            }
            //Group based on second column (ignore empty cells) with different styling
            if(lastColY != line[1] && line[1] != ''){
                //Add line with group header
                bod.push([{text:'', style:'tableSubheader'},{text:line[1], style:'tableSubheader'},{text:'', style:'tableSubheader'},{text:'', style:'tableSubheader'},{text:'', style:'tableSubheader'},
                {text:'', style:'tableSubheader'},{text:'', style:'tableSubheader'}, {text:'', style:'tableSubheader'}, {text:'', style:'tableSubheader'}, {text:'', style:'tableSubheader'}, {text:'', style:'tableSubheader'}, {text:'', style:'tableSubheader'}]);
                    doc.content[2].table.heights = 40;
                //Update last
                lastColY=line[1];
            }*/
            //Add line with data except grouped data
            if( i < doc.content[2].table.body.length){
                bod.push(
                    [
                                {text:line[0], style:'defaultStyle'},
                                {text:line[1], style:'defaultStyle'},
                                {text:line[2], style:'defaultStyle'},
                                {text:line[3], style:'defaultStyle'},
                                {text:line[4], style:'defaultStyle'},
                                {text:line[5], style:'defaultStyle'},
                                {text:line[6], style:'defaultStyle'},
                                {text:line[7], style:'defaultStyle'},
                                {text:line[8], style:'defaultStyle'},
                                {text:line[9], style:'defaultStyle'},
                                {text:line[10], style:'defaultStyle'},
                                ]);
                                doc.content[2].table.heights = 25;   
            }                       
        });
        //Overwrite the old table body with the new one.
        bod.unshift([ {text: 'LOCATION NAME', style: 'tableHeader2'}, {text: 'TASK NO', style: 'tableHeader2'}, {text: 'PRIORITY', style: 'tableHeader2'}, {text: 'STATUS', style: 'tableHeader2'}, {text: 'CALLER', style: 'tableHeader2'}, {text: 'DESCRIPTION', style: 'tableHeader2'}, 
                                                    {text: 'USER', style: 'tableHeader2'}, {text: 'CALL TIME', style: 'tableHeader2'}, {text: 'DISPATCHED TIME', style: 'tableHeader2'}, 
                                                    {text: 'ARRIVED TIME', style: 'tableHeader2'}, {text: 'CLOSED TIME', style: 'tableHeader2'}]);
        doc.content[2].table.headerRows = 1;
        doc.content[2].table.dontBreakRows = true;
        doc.content[2].table.body = bod;
        doc.content[2].layout = 'noBorders';
            
        doc.styles = {
            header: {
                fontSize: 0,
                bold: true,
                margin: [0, 5, 5, 10]
            },
            subheader: {
                fontSize: 16,
                bold: true,
                margin: [0, 0, 0, 30]
            },
            tableSubheader: {
                fontSize: 9,
                bold: false,
                color: '#3C4858',
                margin: [10, 8, 0, 7],
                fillColor: '#f8f9fa'
            },
            tableHeader: {
                bold: true,
                fontSize: 9,
                color: '#3C4858',
                margin: [10, 10, 0, 10],
                fillColor: '#f1f1f9'
            },
            tableHeader2: {
                bold: true,
                fontSize: 8,
                color: 'white',
                fillColor: '#3C4858',
                margin: 10
            },
            defaultStyle: {
            fontSize: 8,
            color: 'black',
            margin: [10, 6, 10, 6]
            }
        }

        return doc;
    }
    populatePdfLocationsDataLandscape(jsonData: any, pdfHeader: string, reportName: string, filter?: any): any {
        var doc = this.populateContentForExportToPDF(jsonData, pdfHeader, reportName, null);
        var lastColX = null;
        var bod = [];

        bod.push([
            { text: 'NAME', style: 'tableHeader2' },
            { text: 'TYPE', style: 'tableHeader2' },
            { text: 'DESCRIPTION', style: 'tableHeader2' },
            { text: 'LATITUDE', style: 'tableHeader2' },
            { text: 'LONGITUDE', style: 'tableHeader2' },
            { text: 'OWNER', style: 'tableHeader2' },
            { text: 'JURISDICTION', style: 'tableHeader2' },
            { text: 'ALTERNATE ID', style: 'tableHeader2' },
        ]);

        doc.content[2].table.widths = [70, 45, 55, 50, 50, 50, 60, 70];
        doc.content[2].table.heights = 20;

        doc.content[2].table.body.forEach(function (line: any, i: any) {
            bod.push([
                { text: line[0], style: 'defaultStyle' },
                { text: line[1], style: 'defaultStyle' },
                { text: line[2] ? line[2] : '', style: 'defaultStyle' },
                { text: line[3] ? line[3] : '', style: 'defaultStyle' },
                { text: line[4] ? line[4] : '', style: 'defaultStyle' },
                { text: line[5], style: 'defaultStyle' },
                { text: line[6] ? line[6] : '', style: 'defaultStyle' },
                { text: line[7] ? line[7] : '', style: 'defaultStyle' },
            ]);
            doc.content[2].table.heights = 25;
        });

        doc.content[2].table.headerRows = 1;
        doc.content[2].table.dontBreakRows = true;
        doc.content[2].table.body = bod;
        doc.content[2].layout = 'lightHorizontalLines';

        doc.styles = {
            header: {
                fontSize: 0,
                bold: true,
                margin: [0, 0, 0, 0]
            },
            subheader: {
                fontSize: 16,
                bold: true,
                margin: [0, 0, 0, 0]
            },
            tableSubheader: {
                fontSize: 11,
                bold: false,
                color: '#3C4858',
                margin: [0, 0, 0, 0],
                fillColor: '#f8f9fa'
            },
            tableHeader: {
                bold: true,
                fontSize: 11,
                color: '#3C4858',
                margin: [0, 0, 0, 0],
                fillColor: '#f1f1f9'
            },
            tableHeader2: {
                bold: true,
                fontSize: 7,
                color: 'white',
                fillColor: '#3C4858',
                margin: 3
            },
            defaultStyle: {
                fontSize: 7,
                color: 'black',
                margin: [0, 5, 0, 0]
            }
        }
        return doc;
    }

    prepareDataForExportToPdf(json: any) {
        var csv = this.convertJsonToCsvEx(json, true);
        var parsedCsv = csv.split("\n");
        var data = [];
        for (var row in parsedCsv) {
            let lastCharacter: string = parsedCsv[row].charAt(parsedCsv[row].length - 2);
            if (lastCharacter === ",") {
                parsedCsv[row] = parsedCsv[row].slice(0, -2)
            }
            if (parsedCsv[row] != "") {
                data.push("[" + parsedCsv[row] + "]");
            }
        }
        var formattedData = data.toString();
        var filteredFromattedData = formattedData.replace(/\s+/g, " ");
        return JSON.parse("[" + filteredFromattedData + "]");
    }

    populatePdfWithAssetInventoriesReportData(jsonData: any, pdfHeader: string, reportName: string, filter?: any, name?: string[]): any {
       
       let jsonDataModified =  jsonData.filter((element: any) => {
            return element !== null || element != undefined;
          });
        var doc = this.populateContentForExportToPDF(jsonDataModified, pdfHeader, reportName, null, name);
        var docAllData = this.populateContentForExportToPDF(jsonData, pdfHeader, reportName, null, name);
    
        
        var lastColX = null;
        var bod = [];
        // console.log("nameeeee",name);
        let headerArray: any[] = [];
        if(name){
            headerArray = [
                name.includes("location") ? { text: 'LOCATION', style: 'tableHeader2' } : null,
                name.includes("typeName") ? { text: 'TYPE', style: 'tableHeader2' } : null,
                name.includes("ownerNameAgency") ? { text: 'OWNER', style: 'tableHeader2'} : null,
                name.includes("name") ?  { text: 'NAME or P/N', style: 'tableHeader2' } : null,
                name.includes("manufacturer") ? { text: 'MANUF- ACTURER', style: 'tableHeader2' } : null,
                name.includes("modelNo") ? { text: 'MODEL NO', style: 'tableHeader2' } : null,
                name.includes("serialNo") ? { text: 'SERIAL NO', style: 'tableHeader2' } : null,
                name.includes("quantity") ?  { text: 'QTY', style: 'tableHeader2' } : null,
                name.includes("units") ? { text: 'UNITS', style: 'tableHeader2' } : null,
                name.includes("outOfService") ? { text: 'OUT OF SERVICE', style: 'tableHeader2' } : null,
                name.includes("category") ? { text: 'CATEGORY', style: 'tableHeader2' } : null,
                name.includes("serviceDate") ? { text: 'SERVICE DATE', style: 'tableHeader2' } : null,
                name.includes("originalValue") ? { text: 'ORIGINAL VALUE', style: 'tableHeader2' } : null,
                name.includes("depreciatedValue") ? { text: 'DEPRE- CIATED VALUE', style: 'tableHeader2' } : null,
                name.includes("warrantyDate") ?  { text: 'WARRANTY DATE', style: 'tableHeader2' } : null,
                name.includes("warrantyExpiration") ? { text: 'WARRANTY EXP', style: 'tableHeader2' } : null,
                name.includes("latitudeLongitude") ?  { text: 'LATITUDE LONGITUDE', style: 'tableHeader2' } : null,
            ];
        }
        const header = headerArray.filter((element: any) => {
            return element !== null && element !== undefined;
          });
        bod.push(header);

        doc.content[2].table.widths = [];
        
        if(name && name.length<16) {
            for (let i = 0; i < name.length; i++) {      
                doc.content[2].table.widths.push('auto');
            }
        }
        else if(name && name.length == 16) {
            for (let i = 0; i < name.length; i++) {      
                doc.content[2].table.widths.push(37);
            }
        }
        else if(name && name.length == 17) {
            for (let i = 0; i < name.length; i++) {
                doc.content[2].table.widths.push(34);
            }
        }
       
        doc.content[2].table.heights = 20;
        
        
        docAllData.content[2].table.body.forEach(function (line: any, t: any) {
            let rowArray: any[] = [];
            if(name) {
                rowArray = [
                name.includes("location") ? { text: line[0], style: 'defaultStyle' } : null,
                name.includes("typeName") ? { text: line[1] ? line[1] : '', style: 'defaultStyle' } : null,
                name.includes("ownerNameAgency") ? { text: line[2], style: 'defaultStyle' } : null,
                name.includes("name") ? { text: line[3], style: 'defaultStyle' } : null,
                name.includes("manufacturer") ? { text: line[4], style: 'defaultStyle' } : null,
                name.includes("modelNo") ? { text: line[5], style: 'defaultStyle' } : null,
                name.includes("serialNo") ? { text: line[6], style: 'defaultStyle' } : null,
                name.includes("quantity") ? { text: line[7], style: 'defaultStyle' } : null,
                name.includes("units") ? { text: line[8], style: 'defaultStyle' } : null,
                name.includes("outOfService") ? { text: line[9], style: 'defaultStyle' } : null,
                name.includes("category") ? { text: line[10], style: 'defaultStyle' } : null,
                name.includes("serviceDate") ? { text: line[11], style: 'defaultStyle' } : null,
                name.includes("originalValue") ? { text: line[12], style: 'defaultStyle' } : null,
                name.includes("depreciatedValue") ? { text: line[13], style: 'defaultStyle' } : null,
                name.includes("warrantyDate") ? { text: line[14], style: 'defaultStyle' } : null,
                name.includes("warrantyExpiration") ? { text: line[15], style: 'defaultStyle' } : null,
                name.includes("latitudeLongitude") ? { text: line[16], style: 'defaultStyle' } : null,
        
                ];
            }

            const data = rowArray.filter((element: any) => {
                return element !== null && element !== undefined;
              });
            bod.push(data);
            
        });

        doc.content[2].table.headerRows = 1;
        doc.content[2].table.dontBreakRows = true;
        doc.content[2].table.body = bod;
        doc.content[2].layout = 'lightHorizontalLines';

        doc.styles = {
            header: {
                fontSize: 0,
                bold: true,
                margin: [0, 0, 0, 0]
            },
            subheader: {
                fontSize: 16,
                bold: true,
                margin: [10, 0, 0, 0]
            },
            tableSubheader: {
                fontSize: 11,
                bold: false,
                color: '#3C4858',
                margin: [0, 0, 0, 0],
                fillColor: '#f8f9fa'
            },
            tableHeader: {
                bold: true,
                fontSize: 11,
                color: '#3C4858',
                margin: [0, 0, 0, 0],
                fillColor: '#f1f1f9'
            },
            tableHeader2: {
                bold: true,
                fontSize: 7,
                color: 'white',
                fillColor: '#3C4858',
                margin: 3
            },
            defaultStyle: {
                fontSize: 7,
                color: 'black',
                margin: [0, 5, 0, 0]
            }
        }
        
        return doc;
    }

    private convertJsonToCsvEx(jsonData: any, showLabel: boolean): any {
        let arrayData: any = typeof jsonData != 'object' ? JSON.parse(jsonData) : jsonData;
        var CSV: string = '';
        if (showLabel) {
            var row = "";
            for (var index in arrayData[0]) {
                row += '"' + index + '",';
            }

            row = row.slice(0, -1);
            CSV += row + '\r\n';
        }

        for (let i = 0; i < arrayData.length; i++) {
            var row = "";
            for (var index in arrayData[i]) {
                row += '"' + this.checkQuotesInString(arrayData[i][index]) + '",';
            }

            row.slice(0, row.length - 1);
            CSV += row + '\r\n';
        }

        if (CSV == '') {
            alert("Invalid data");
            return;
        }
        return CSV;
    }

    private orderByProperty(props: any[]): (a: any, b: any) => number {
        let args = [...props].slice(1);
        return (a: any, b: any) => {
            let equality = 0;
            if (props && props.length > 0 && a[props[0]] && b[props[0]]) {
                equality = a[props[0]].toLowerCase().localeCompare(b[props[0]].toLowerCase());
            }
            if (equality === 0 && props.length > 1) {
                return this.orderByProperty(args)(a, b);
            }
            return equality;
        };
    }
   
    checkQuotesInString(value: number) {
        if (value) {
            return value.toString().replace(/['"]+/g, '\\"');
        }
        return '';
    }
}
