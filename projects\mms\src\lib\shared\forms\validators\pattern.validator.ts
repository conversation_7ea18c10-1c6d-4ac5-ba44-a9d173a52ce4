import { Validator, ValidationErrors, AbstractControl, FormControl } from '@angular/forms'

export class PatternValidator implements Validator {

    constructor(private pattern: string) {
    }

    validate(control: AbstractControl): ValidationErrors | null {
        if (control instanceof FormControl) {
            const value: string = control.value;
            const regex = new RegExp(this.pattern);
            if (value != null && value.trim() && regex.test(value) === false) {
                return {
                    pattern: {
                        pattern: value
                    }
                };
            }
        }
        return null;
    }
}
