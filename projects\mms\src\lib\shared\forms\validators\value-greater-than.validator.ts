import { Validator, AbstractControl, ValidationErrors } from '@angular/forms'

export class GreaterThanValidator implements Validator {

    constructor(
        private controlToCompare: AbstractControl,
        private controlToValidateName: string,
        private controlToCompareName: string
    ) { }

    validate(controlToValidate: AbstractControl): ValidationErrors | null {
        const start = controlToValidate.value;
        const end = this.controlToCompare.value;

        if (start === null || end === null || start === '' || end === '' || +start >= +end) {
            if (this.controlToCompare.invalid) {
                this.controlToCompare.updateValueAndValidity({ onlySelf: true, emitEvent: false });
            }
            return null;
        }

        return {
            greaterThan: {
                greaterThan: true,
                controlToValidateName: this.controlToValidateName,
                controlToCompareName: this.controlToCompareName
            }
        };
    }
}
