import { Action } from '@ngrx/store';
import { JurisdictionConfiguration } from '../../payroll-and-billing/contracts/models/jurisdiction-configuration.model';
import { Item } from '../../payroll-and-billing/contracts/models/item.model';
import { Rate } from '../../payroll-and-billing/contracts/models/rate.model';

export enum ActionTypes {
    LOAD_ACTION = '[Jurisdiction Configuration] Load Action',
    LOAD_REQUEST = '[Jurisdiction Configuration] Load Request',
    LOAD_FAILURE = '[Jurisdiction Configuration] Load Failure',
    LOAD_SUCCESS = '[Jurisdiction Configuration] Load Success',
    SAVE_ACTION = '[Jurisdiction Configuration] Save Action',
    SAVE_REQUEST = '[Jurisdiction Configuration] Save Request',
    SAVE_SUCCESS = '[Jurisdiction Configuration] Save Success',
    UPDATE_REQUEST = '[Jurisdiction Configuration] Update Request',
    UPDATE_SUCCESS = '[Jurisdiction Configuration] Update Success',
    DELETE_REQUEST = '[Jurisdiction Configuration] Delete Request',
    DELETE_SUCCESS = '[Jurisdiction Configuration] Delete Success',
    CHANGE_SELECTED_JURISDICTION_CONF = '[Jurisdiction Configuration] Change Selected Jurisdiction Configuration',
    CHANGE_SELECTED_JURISDICTION_CONF_SUCCESS = '[Jurisdiction Configuration] Change Selected Jurisdiction Configuration sUCCESS',
    CONFIRM_DELETE_JURISDICTION_CONF = '[Jurisdiction Configuration] Confirm Delete Jurisdiction Conf',
    JURISDICTION_CONF_RESET_ACTION = '[Jurisdiction Configuration] Jurisdiction Configuration Reset',
    JURISDICTION_CONF_REVERT_CHANGES_ACTION = '[Jurisdiction Configuration] Jurisdiction Configuration Revert Changes',
    FORM_CHANGED_ACTION = '[Jurisdiction Configuration] Form Changed',
    CONFIRM_CHANGE_SELECTED_JURISDICTION = '[Jurisdiction Configuration] Confirm change selected jurisdiction',
    LOAD_ALL_JURISDICTION_CONFIGURATIONS_REQUEST = '[Jurisdiction Configuration] Load All Jurisdiction Configurations Request',
    LOAD_ALL_JURISDICTION_CONFIGURATIONS_SUCCESS = '[Jurisdiction Configuration] Load All Jurisdiction Configurations Success',
    SAVE_COPIED_JOB_CODES = '[Jurisdiction Configuration] Save Copied Job Codes',
    CONTRACT_HOURS_CHANGED = '[Jurisdiction Configuration] Contract Hours Changed',
    SAVE_CURRENT_JOB_CODES_RATES = '[Jurisdiction Configuration] Save Current Job Codes Rates',
    SET_VEH_EQ_TYPE = '[Jurisdiction Configuration] Set Vehicle Equipment Location Type',
    NO_ACTION = '[Jurisdiction Configuration] No Action',
    UPDATE_HAPPEND = '[Jurisdiction Configuration] Update Happend',
    CHECK_LIST_OF_JOB_CODES_IS_EMPTY = '[Jurisdiction Configuration] Check List Of Job Codes Is Empty',
    CREATION_HAPPEND = '[Jurisdiction Configuration] Creation Happend',
    CREATE_NEW_JURISDICTION_CONF = '[Jurisdiction Configuration] Create New Jurisdiction Configuration',
}

export class LoadAction implements Action {
    readonly type = ActionTypes.LOAD_ACTION;
}
export class LoadRequestAction implements Action {
    readonly type = ActionTypes.LOAD_REQUEST;
    constructor(public payload: { contractId: string }) { }
}

export class LoadFailureAction implements Action {
    readonly type = ActionTypes.LOAD_FAILURE;
    constructor(public payload: { error: string }) { }
}

export class LoadSuccessAction implements Action {
    readonly type =  ActionTypes.LOAD_SUCCESS;
    constructor(public payload: { items: Array<JurisdictionConfiguration> }) { }
}

export class SaveAction implements Action {
    readonly type = ActionTypes.SAVE_ACTION;
    constructor(public payload: { item: JurisdictionConfiguration }) { }
}
export class SaveRequestAction implements Action {
    readonly type = ActionTypes.SAVE_REQUEST;
    constructor(public payload: { item: JurisdictionConfiguration }) { }
}

export class SaveSuccessAction implements Action {
    readonly type = ActionTypes.SAVE_SUCCESS;
    constructor(public payload: { item: JurisdictionConfiguration }) { }
}

export class UpdateRequestAction implements Action {
    readonly type = ActionTypes.UPDATE_REQUEST;
    constructor(public payload: { item: JurisdictionConfiguration }) { }
}

export class UpdateSuccessAction implements Action {
    readonly type = ActionTypes.UPDATE_SUCCESS;
    constructor(public payload: { item: JurisdictionConfiguration }) { }
}

export class DeleteRequestAction implements Action {
    readonly type = ActionTypes.DELETE_REQUEST;
}

export class DeleteSuccessAction implements Action {
    readonly type = ActionTypes.DELETE_SUCCESS;
    constructor(public payload: { itemId: string }) { }
}

export class ChangeSelectedJurisdictionConf implements Action {
    readonly type = ActionTypes.CHANGE_SELECTED_JURISDICTION_CONF;
    constructor(public payload: { item: JurisdictionConfiguration }) { }
}

export class ConfirmChangeSelectedJurisdictionConf implements Action {
    readonly type = ActionTypes.CONFIRM_CHANGE_SELECTED_JURISDICTION;
    constructor(public payload: { item: JurisdictionConfiguration }) { }
}

export class ChangeSelectedJurisdictionConfSuccess implements Action {
    readonly type = ActionTypes.CHANGE_SELECTED_JURISDICTION_CONF_SUCCESS;
    constructor(public payload: { item: JurisdictionConfiguration }) { }
}

export class ConfirmDeleteJurisdictionConf implements Action {
    readonly type = ActionTypes.CONFIRM_DELETE_JURISDICTION_CONF;
}

export class JurisdictionConfigurationResetAction implements Action {
    readonly type = ActionTypes.JURISDICTION_CONF_RESET_ACTION;
}

export class JurisdictionConfigurationRevertChangesAction implements Action {
    readonly type = ActionTypes.JURISDICTION_CONF_REVERT_CHANGES_ACTION;
}

export class FormChangedActions implements Action {
    readonly type = ActionTypes.FORM_CHANGED_ACTION;
    constructor(public payload: { changed: boolean }) { }
}

export class NoAction implements Action {
    readonly type = ActionTypes.NO_ACTION;
}

export class LoadAllJurisdictionConfigurationsRequestAction implements Action {
    readonly type = ActionTypes.LOAD_ALL_JURISDICTION_CONFIGURATIONS_REQUEST;
}
export class LoadAllJurisdictionConfigurationsSuccessAction implements Action {
    readonly type =  ActionTypes.LOAD_ALL_JURISDICTION_CONFIGURATIONS_SUCCESS;
    constructor(public payload: { items: Array<JurisdictionConfiguration> }) { }
}

export class SaveCopiedJobCodesAction implements Action {
    readonly type =  ActionTypes.SAVE_COPIED_JOB_CODES;
    constructor(public payload: { items: Array<Item> }) { }
}

export class ContractHoursChangedAction implements Action {
    readonly type =  ActionTypes.CONTRACT_HOURS_CHANGED;
    constructor(public payload: { contractHours: Item }) { }
}

export class SaveCurrentJobCodeRatesAction implements Action {
    readonly type =  ActionTypes.SAVE_CURRENT_JOB_CODES_RATES;
    constructor(public payload: { rates: Array<Rate>}) { }
}

export class SetVehicleEquipmentLocationType implements Action {
    readonly type =  ActionTypes.SET_VEH_EQ_TYPE;
    constructor(public payload: { type: string }) { }
}

export class UpdateHappend implements Action {
    readonly type = ActionTypes.UPDATE_HAPPEND;
    constructor(public payload: { updateHappend: boolean }) { }
}

export class CreationHappend implements Action {
    readonly type = ActionTypes.CREATION_HAPPEND;
    constructor(public payload: { creationHappend: boolean }) { }
}

export class CreateNewJurisConfCheck implements Action {
    readonly type = ActionTypes.CREATE_NEW_JURISDICTION_CONF;
    constructor(public payload: { createNewJurisConfCheck: boolean }) { }
}

export class CheckListOfJobCodesIsEmpty implements Action {
    readonly type =  ActionTypes.CHECK_LIST_OF_JOB_CODES_IS_EMPTY;
    constructor(public payload: { isListJobCodesEmpty?: boolean}) { }
}

export type Actions =
    | SaveAction
    | LoadAction
    | SaveRequestAction
    | SaveSuccessAction
    | UpdateSuccessAction
    | UpdateRequestAction
    | LoadRequestAction
    | DeleteSuccessAction
    | DeleteRequestAction
    | LoadFailureAction
    | LoadSuccessAction
    | ChangeSelectedJurisdictionConf
    | ConfirmDeleteJurisdictionConf
    | JurisdictionConfigurationResetAction
    | JurisdictionConfigurationRevertChangesAction
    | ChangeSelectedJurisdictionConfSuccess
    | FormChangedActions
    | LoadAllJurisdictionConfigurationsRequestAction
    | LoadAllJurisdictionConfigurationsSuccessAction
    | SaveCopiedJobCodesAction
    | ContractHoursChangedAction
    | SaveCurrentJobCodeRatesAction
    | SetVehicleEquipmentLocationType
    | NoAction
    | UpdateHappend
    | CheckListOfJobCodesIsEmpty
    | CreationHappend
    | CreateNewJurisConfCheck;