import { Component, OnInit, OnD<PERSON>roy, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatD<PERSON>er, MatSidenavModule } from '@angular/material/sidenav';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable, Subscription } from 'rxjs';

import { LocationInstanceStoreState, RouterStoreSelectors, LocationInstanceStoreSelectors, LocationInstanceStoreActions } from '../../../root-store';
import { RouterStateUrl } from '../../../root-store/router-store/router-state.serializer';
import { RouterReducerState } from '@ngrx/router-store';
import { ActivatedRoute, NavigationEnd, Router, Event as NavigationEvent } from '@angular/router';
import { MapSidebarComponent } from '../../shared/map-sidebar/map-sidebar.component';
import { MatTabsModule } from '@angular/material/tabs';

@Component({
    selector: 'mms-location-instance-map',
    templateUrl: './location-instances-map.component.html',
    styleUrls: ['./location-instances.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        RouterModule,
        MatSidenavModule,
        MatButtonModule,
        MatIconModule,
        MatTabsModule,
        MapSidebarComponent
    ]
})
export class LocationInstancesMapComponent implements OnInit, OnDestroy {
    @ViewChild('sidenav1', { static: false}) sideNav!: MatDrawer;
    mapPoints$ =this.store$.select(LocationInstanceStoreSelectors.selectSelectedLocationMapPointData); 
    mapConfig$ = this.store$.select(LocationInstanceStoreSelectors.selectMapConfig);
    subscriptions: Array<Subscription> = new Array<Subscription>();
    routerState$: Observable<RouterReducerState<RouterStateUrl>> = this.store$.select(RouterStoreSelectors.getRouterStoreState);
    selectedWarehouse$ = this.store$.select(LocationInstanceStoreSelectors.selectedWarehouse);  
    locationId = "" ;

    isVehicleType$!: Observable<boolean>;
   
    constructor(private store$: Store<LocationInstanceStoreState.State>, private route: Router, private currentRoute: ActivatedRoute ) {

    }

    ngOnInit() {
        this.subscriptions.push(this.routerState$.subscribe((data: RouterReducerState<RouterStateUrl>) => {
            if (data.state.url.indexOf('ticket-details') > -1) {
                this.sideNav.open();
            } else {
                this.customHandleCloseSideBar();
            }
            this.locationId = data.state.params['locationId'] || '';
        }));
        if (this.locationId)
            this.store$.dispatch(new LocationInstanceStoreActions.RefreshMapDataAction({ locationInstanceId: this.locationId }));
        this.mapPoints$ = this.store$.select(LocationInstanceStoreSelectors.selectSelectedLocationMapPointData);
        this.isVehicleType$ = this.store$.select(LocationInstanceStoreSelectors.isSelectedLocationVehicleType);
    }
    
    
    ngOnDestroy(): void {
        if (this.subscriptions.length > 0) {
            this.subscriptions.forEach(element => {
                element.unsubscribe();
            });
        }
    }
    
    close(){
        this.store$.dispatch(new LocationInstanceStoreActions.CloseSidebarAction());
    }

    closeDrawer(){
        let id = this.currentRoute.snapshot.params['locationId'];
        if (this.route.url.includes("ticket-details")){
            this.route.navigate(["locations/map/" + id]);
        }
    }

    customHandleCloseSideBar(){
       this.route.events
        .subscribe(
          (event: NavigationEvent) => {
            if(event instanceof NavigationEnd) {
              if(event.url.toString().includes("locations/map/") && !event.url.toString().includes("ticket-details")){
                  this.sideNav.close();
              }
            }
          });   
    }

}
