import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { QuotesStoreEffects } from './effects';
import { featureReducer } from './reducer';

@NgModule({
  imports: [
    CommonModule,
    StoreModule.forFeature('quotes', featureReducer),
    EffectsModule.forFeature([QuotesStoreEffects])
  ],
  providers: [QuotesStoreEffects]
})
export class QuotesStoreModule { }
