import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, Action, select } from '@ngrx/store';
import { Observable, of } from 'rxjs';
import {
  withLatestFrom,
  map,
  switchMap,
  catchError,
  exhaustMap,
  tap
} from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state';
import * as featureSelectors from './selectors';

import { ReportsService } from '../../reports/reports.service';
import { Router } from '@angular/router';
import { IGridStoreEffects } from '../shared-store/material-grid-store/effects';
import { GridStoreActions } from '../shared-store/material-grid-store';
import { PmEfficiencyReportModel, ToReport } from '../../reports/pm-efficiency/pm-efficiency-report.model';
import { UserService } from '../../configuration/shared/user-and-groups/users.service';

@Injectable()
export class PmEfficiencyReportStoreEffects implements IGridStoreEffects {
  constructor(
    private actions$: Actions,
    private reportsService: ReportsService,
    private userService: UserService,
    private store$: Store<featureState.State>,
    private router: Router
  ) { }

  public gridLoadItems(actionType: string): Observable<Action> {
    return this.actions$.pipe(
      ofType<GridStoreActions.GridLoadItemsAction<PmEfficiencyReportModel>>(actionType),
      withLatestFrom(this.store$.select(featureSelectors.selectGridItems)),
      map(([action, items]) => {
        return new GridStoreActions.GridLoadItemsSuccessAction(featureState.PM_EFFICIENCY_REPORT_GRID_ID, { items: items });
      })
    );
  }

  public gridLoadItemsSuccess(actionType: string): Observable<Action> {
    return this.actions$.pipe(
      ofType(actionType),
      map(action => new GridStoreActions.GridNoAction())
    );
  }

  public gridLoadItemsFailed(actionType: string): Observable<Action> {
    return this.actions$.pipe(
      ofType(actionType),
      map(action => new GridStoreActions.GridNoAction())
    );
  }

  gridLoadItems$ = createEffect(() => this.gridLoadItems(
    GridStoreActions.ActionTypes.GRID_LOAD_ITEMS(
      featureState.PM_EFFICIENCY_REPORT_GRID_ID
    )
  ));

  gridLoadItemsSuccess$ = createEffect(() => this.gridLoadItemsSuccess(
    GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_SUCCESS(
      featureState.PM_EFFICIENCY_REPORT_GRID_ID
    )
  ));

  gridLoadItemsFailed$ = createEffect(() => this.gridLoadItemsFailed(
    GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_FAILED(
      featureState.PM_EFFICIENCY_REPORT_GRID_ID
    )
  ));

  searchRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SearchRequestAction>(
      featureActions.ActionTypes.SEARCH_REQUEST
    ),
    withLatestFrom(
      this.store$.select(featureSelectors.selectAgencies),
      this.store$.select(featureSelectors.selectSelectedFilter)
    ),
    exhaustMap(([action, agencies, filter]) => {
      if (!filter.agencies || filter.agencies.length === 0) {
        filter.agencies = agencies?.map(a => a.id!) ?? [];
      }
      return this.reportsService.searchPmEfficiencyReport(action.payload)
        .pipe(
          map(
            reportData => {
              const report = ToReport(reportData.items!, agencies!, filter.fromDate!);
              return new featureActions.SearchSuccessAction(report);
            }
          ),
          catchError(error =>
            of(new featureActions.SearchFailureAction({ error }))
          )
        )
    })
  ));

  searchSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SearchSuccessAction>(
      featureActions.ActionTypes.SEARCH_SUCCESS
    ),
    withLatestFrom(
      this.store$.select(featureSelectors.selectSelectedFilter)
    ),
    map(([action, filter]) => {
      this.router.navigate(['/reports/pm-efficiency']);
      let items: PmEfficiencyReportModel[] = [];
      if (action.payload) {
        items = action.payload.map(project => ({ projects: [project] }));
      }
      return new GridStoreActions.GridLoadItemsSuccessAction(featureState.PM_EFFICIENCY_REPORT_GRID_ID, { items: items });
    })
  ));

  showFilter$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ShowFilterAction>(
      featureActions.ActionTypes.SHOW_FILTER
    ),
    tap((action: featureActions.ShowFilterAction) => {
      this.router.navigate(['/reports/pm-efficiency-filter']);
    })
  ), { dispatch: false });

  getIsLoading$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.CheckIfIsLoading>(
      featureActions.ActionTypes.IS_LOADING
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectIsLoading)),
    ),
    tap(([action, isLoading]: [featureActions.CheckIfIsLoading, boolean]) => {
      if (isLoading === null) {
        this.router.navigate(['/reports/pm-efficiency-filter']);
      }
    })
  ), { dispatch: false });

  loadUserRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadUserRequestAction>(
      featureActions.ActionTypes.LOAD_USER
    ),
    exhaustMap((action) => {
      return this.userService.getUserByIdMMS(action.payload)
        .pipe(
          map(
            user => {
              return new featureActions.LoadUserSuccessAction(user);
            }
          ),
          catchError(error =>
            of(new featureActions.LoadUserFailureAction({ error }))
          )
        )
    })
  ));
}
