import {
    createFeatureSelector, createSelector, MemoizedSelector
} from '@ngrx/store';
import { featureAdapter, State, MainState } from './state';
import { GridStoreState } from '../shared-store/material-grid-store';
import { RouterStoreSelectors } from '../router-store';
import { Invoice } from '../../payroll-and-billing/invoices/models/invoice.model';
import { selectedAgenciesByUser } from '../agency-store/selectors';
import { getAllProjects } from "../projects-store/selectors";

export const getError = (state: MainState): any => state.error;
export const getIsLoading = (state: MainState): boolean => !!state.isLoading;

export const selectInvoiceState: MemoizedSelector<object, State> = createFeatureSelector<State>('invoice');
export const selectMainState: MemoizedSelector<object, MainState> = createSelector(
    selectInvoiceState,
    InvoiceState => InvoiceState.main
);

export const selectAllInvoiceItems: (state: object) => Invoice[] = featureAdapter.getSelectors(selectMainState).selectAll;
export const getNextPageToken = createSelector(
    selectInvoiceState,
    state => state.main.nextPageToken
);

export const selectIsLoading = createSelector(
    selectInvoiceState,
    store => store.main.isLoading
);

export const selectIncludedBillsGrid = createSelector(
    selectInvoiceState,
    (store) => store.includedBillsGrid
);

export const selectAvailableBillsGrid = createSelector(
    selectInvoiceState,
    (store) => store.availableAuthorizedBillsGrid
);

export const selectInvoiceTotalBillsGrid = createSelector(
    selectInvoiceState,
    (store) => store.invocieTolalGrid
);

export const selectIncludedBillsGridItems = createSelector(
    selectInvoiceState,
    (store) => store.includedBillsGrid.items
);

export const selectGrid: MemoizedSelector<object, GridStoreState.State<any>> = createSelector(
    selectInvoiceState,
    (store) => store.grid
);

export const selectIsGridPopulated = createSelector(
    selectGrid,
    grid => !!(grid && grid.items.length)
);
export const selectInitialLoad = createSelector(
    selectInvoiceState,
    store => store.main.initialLoad
);

export const selectSelectedFilter = createSelector(
    selectInvoiceState,
    store => store.main.selectedFilter
);

export const selectSingleInvoice = createSelector(
    selectInvoiceState,
    RouterStoreSelectors.getRouterStoreState,
    (store, routerState) => routerState && routerState.state && routerState.state.params && routerState.state.params['id']
        && store.main.invoices && store.main.invoices.length && store.main.invoices.find( x => x.id === routerState.state.params['id'])
);

export const selectSelectedInvoice = createSelector(
    selectMainState,
    RouterStoreSelectors.getRouterStoreState,
    (state, routerState) => routerState && routerState.state.params['id'] && state.entities[routerState.state.params['id']]
)

export const getLoadedInvoices = createSelector(
    selectInvoiceState,
    state => state.main.invoices
);

export const selectAllAgencies = createSelector(
    selectedAgenciesByUser,
    agencies =>  [{"id": "0", "name": "All"}, ...agencies]
);

export const selectContract = createSelector(
    selectInvoiceState,
    state => state.main.selectedContract
);

export const selectAgencies = createSelector(
    selectedAgenciesByUser,
    agencies => [...agencies]
);

export const selectProjects = createSelector(
    getAllProjects,
    projects => [...projects]
);

export const selectViewOnly = createSelector(
    selectInvoiceState,
    state => state.main.viewOnly
);

