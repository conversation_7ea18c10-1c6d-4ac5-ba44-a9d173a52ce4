import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, select } from '@ngrx/store';
import { of as observableOf, pipe, of } from 'rxjs';
import { catchError, map, switchMap, tap, withLatestFrom, exhaustMap, concatMap } from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state'
import * as featureSelectors from './selectors'

import { Router } from '@angular/router';
import { Page } from '../../core/page.service';
import { FormResetAction } from '../../shared/forms/connect-form-ngrx';
import { displayMessages } from '../../core/resources/display-messages';
import { UserService } from '../../configuration/shared/user-and-groups/users.service';
import { UserIdentity } from '@econolite/identity-client';
import { MatDialog } from '@angular/material/dialog';
import { LocationSubtypeService } from '../../configuration/location-subtype/services/location-subtype.service';
import { LocationSubtype } from '../../configuration/location-subtype/models/location-subtype.model';

@Injectable()
export class LocationSubtypeStoreEffects {
  constructor(
    private router: Router,
    private locationSubtypeService: LocationSubtypeService,
    private actions$: Actions,
    private store$: Store<featureState.State>,
    private page: Page,
    private userService: UserService,
    private userIdentity: UserIdentity,
    public dialog: MatDialog) { }

  loadRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(
      featureActions.ActionTypes.LOAD_REQUEST
    ),
    switchMap(action =>
      this.locationSubtypeService
        .getLocationSubtypes()
        .pipe(
          map(
            items =>
              new featureActions.LoadSuccessAction({
                items
              })
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  saveRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveRequestAction>(
      featureActions.ActionTypes.SAVE_REQUEST
    ),
    exhaustMap((action) =>
      this.locationSubtypeService
        .addLocationSubtype(action.payload.item)
        .pipe(
          concatMap((locationSubtype) => {
            return [
              new featureActions.SaveSuccessAction({
                item: locationSubtype!
              }),
              new featureActions.LoadRequestAction()
            ]
          }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  updateRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateRequestAction>(
      featureActions.ActionTypes.UPDATE_REQUEST
    ),
    exhaustMap((action) =>
      this.locationSubtypeService
        .updateLocationSubtype(action.payload.item)
        .pipe(
          switchMap(
            () => [
            new featureActions.UpdateSuccessAction({ item: { id: action.payload.item.id!, changes: action.payload.item } }),
            new featureActions.LoadRequestAction()]
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  updateRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateSuccessAction>(
      featureActions.ActionTypes.UPDATE_SUCCESS
    ),
    tap(() => {
      this.page.notification.show('Location Category updated');
    })
  ), { dispatch: false });

  saveRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveSuccessAction>(
      featureActions.ActionTypes.SAVE_SUCCESS
    ),
    map(() => {
      this.page.notification.show('Location Category added');
      return new FormResetAction();
    })
  ));

  confirmDeleteLocationSubtypeEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmDeleteLocationSubtypeAction>(
      featureActions.ActionTypes.CONFIRM_DELETE_LOCATION_SUBTYPE
    ),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectedLocationSubtype))),
    exhaustMap(([action, selectedLocationSubtype]: [featureActions.ConfirmDeleteLocationSubtypeAction, LocationSubtype]) =>
      this.page
        .confirm.show('Are you sure you want to delete this location category?', 'Are you sure?')
        .pipe(
          map(
            (result) => {
              if (result) {
                return new featureActions.DeleteRequestAction();
              }
              return new featureActions.NoAction();
            }
          )
        )
    )
  ));

  deleteRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteRequestAction>(
      featureActions.ActionTypes.DELETE_REQUEST
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedLocationSubtype))
    ),
    switchMap(([_, selectedLocationSubtype]: [featureActions.DeleteRequestAction, LocationSubtype]) =>
      this.locationSubtypeService
        .deleteLocationSubtype(selectedLocationSubtype.id!)
        .pipe(
          switchMap(_ => [
            new featureActions.DeleteSuccessAction({
              itemId: selectedLocationSubtype.id!
            }),
          ]),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  deleteSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteSuccessAction>(
      featureActions.ActionTypes.DELETE_SUCCESS
    ),
    pipe(
      map(() => {
        this.page.notification.show('Location Category deleted');
      }),
      switchMap(_ => [
        new FormResetAction(),
        new featureActions.AddNewAction()
      ])
    )
  ));

  addNewEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AddNewAction>(
      featureActions.ActionTypes.ADD_NEW
    ),
    tap(() => {
      this.router.navigate(['/configuration/types/location-subtypes']);
    })
  ), { dispatch: false });

  saveEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveAction>(
      featureActions.ActionTypes.SAVE
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedLocationSubtype)),
    ),
    map(([action, selectedLocationSubtype]: [featureActions.SaveAction, LocationSubtype]) => {
      if (selectedLocationSubtype == null) {
        return new featureActions.SaveRequestAction({ item: action.payload.item })
      } else {
        return new featureActions.UpdateRequestAction({ item: action.payload.item  })
      }
    })
  ));

  canBeDeletedEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.CanBeDeletedAction>(
      featureActions.ActionTypes.CAN_BE_DELETED
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedLocationSubtype)),
    ),
    switchMap(([_, selectedLocationSubtype]: [featureActions.CanBeDeletedAction, LocationSubtype]) =>
      this.locationSubtypeService.canBeDeleted(selectedLocationSubtype.id!).pipe(
        map((response) => {
          if (!response) {
            return new featureActions.ConfirmDeleteLocationSubtypeAction();
          } else {
            this.page.alert.warning(displayMessages.exceptions.itemIsAssociated('location category'));
            return new featureActions.CanceledDeleteLocationSubtypeAction();
          }
        }),
        catchError(error =>
          of(new featureActions.LoadFailureAction({ error }))
        )
      )
    )
  ));
}