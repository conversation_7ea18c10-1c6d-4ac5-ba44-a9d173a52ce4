import { Component, Inject, OnInit } from "@angular/core";
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { FormBuilder, FormGroup } from "@angular/forms";
import { Store } from "@ngrx/store";
import { Ticket } from "../../../../configuration/tickets/tickets.model";
import { TicketService } from "../../../../configuration/tickets/tickets.service";
import { TimesheetStoreSelectors } from "../../../../root-store";
import { TicketStoreActions, TicketStoreSelectors } from "../../../../root-store/ticket-store";
import { MergeTicketModel } from "../../models/merge-ticket.model";
import { MMSSelectMenuComponent } from '../../../../shared/forms/controls/mms-select-menu/mms-select-menu.component';
import { ValidationMessageComponent } from '../../../../shared/forms/validation-message/validation-message.component';
import { BlockUIDirective } from '../../../../shared/block-ui/block-ui.directive';

@Component({
    selector: 'merge-task.component',
    templateUrl: './merge-task.component.html',
    styleUrls: ['./merge-task.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatDialogModule,
        MatFormFieldModule,
        MatButtonModule,
        MMSSelectMenuComponent,
        ValidationMessageComponent,
        BlockUIDirective
    ]
})
export class MergeTaskComponent implements OnInit{
    
    showDialog: boolean = true;
    dataSource$ = this.store$.select(TicketStoreSelectors.selectAllTicketsList);
    tasks$ = this.store$.select(TimesheetStoreSelectors.selectTasksForWorkEditor);
    isLoading$ = this.store$.select(TicketStoreSelectors.selectIsTicketsLoading);

    formGroup!: FormGroup;
    data: any;
    
    constructor(private formBuilder: FormBuilder, 
    private store$: Store<any>,@Inject(MAT_DIALOG_DATA) data: any, 
    private ticketService: TicketService,
    private dialogRef: MatDialogRef<MergeTaskComponent>){
      this.data = Object.values(data);
    }

    ngOnInit(){

       if(this.data && this.data[0].project && this.data[0].project.id && this.data[0].type) {
          this.store$.dispatch(new TicketStoreActions.LoadAllTicketsRequestAction({agencyId: this.data[0].project.agencyId, type: this.data[0].type}));
       }
       
      this.formGroup = this.formBuilder.group({
        tickets: [null]
      });
    }

    valueMapper(option: any) {
        return option.ticketNo;
      }
      compareFn(c1: any, c2: any): boolean {
        return c1 == c2
      }


      ticketValueMapper(option: any) {
        return option.id;
      }
    
      ticketTextMapper(option: Ticket) {
        return option.ticketNo;
      } 
    
      ticketCompareFn(c1: any, c2: any): boolean {
        return c1 == c2
      }

      mergeTask() {
      var model= new MergeTicketModel;
      const rawValue = this.formGroup.getRawValue();
      model.sourceId = this.data[0].id;
      model.destinationId = rawValue.tickets;
      this.store$.dispatch(new TicketStoreActions.SaveMergeRequestAction(model));
      this.dialogRef.close();
      this.showDialog = false;
     }
    
}