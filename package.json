{"name": "centracs-mms-module-ui", "version": "1.0.0", "scripts": {"ng": "ng", "build:prod": "node --max_old_space_size=8000 ./node_modules/@angular/cli/bin/ng build --configuration production", "start": "ng serve --port 4100 -o", "start:demo": "ng serve --port 4176 -o", "start:staging": "ng serve --port 3242 -o", "build": "ng build --configuration production", "test": "ng test", "test:ci": "npm run test -- --codeCoverage=true", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/cdk": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/elements": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/material": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/platform-server": "^19.0.0", "@angular/router": "^19.0.0", "@econolite/centracs-theme": "^1.0.12", "@econolite/dashboard": "^1.0.3", "@econolite/identity-client": "^19.0.2", "@econolite/limestone": "^1.0.30", "@econolite/mobility-alert-escalations": "^1.0.4", "@econolite/mobility-alert-notifications": "^1.0.5", "@econolite/mobility-alert-recipients": "^1.0.5", "@econolite/mobility-events": "^1.0.8", "@econolite/mobility-triggers": "^1.0.4", "@econolite/toolkit": "^1.0.0", "@ngrx/effects": "19.0.0", "@ngrx/entity": "19.0.0", "@ngrx/router-store": "19.0.0", "@ngrx/store": "19.0.0", "@ngrx/store-devtools": "19.0.0", "@popperjs/core": "^2.11.8", "@types/google.maps": "3.45.0", "@types/googlemaps": "3.39.0", "@types/jest": "^29.5.12", "@types/keycloak-js": "3.4.1", "@types/mocha": "^10.0.6", "ajv": "^8.12.0", "angular-calendar": "0.31.1", "angular-resizable-element": "7.0.2", "angular2-text-mask": "9.0.0", "azure-maps-control": "^2.0.22", "azure-maps-spatial-io": "^0.1.2", "bootstrap": "^5.3.3", "classlist.js": "^1.1.20150312", "core-js": "^3.36.0", "cron-parser": "^4.9.0", "date-fns": "^3.3.1", "file-saver": "^2.0.5", "hammerjs": "^2.0.8", "highcharts": "11.4.8", "highcharts-angular": "4.0.1", "jquery": "^3.7.1", "jwt-decode": "^4.0.0", "keycloak-angular": "19.0.2", "keycloak-js": "21.0.0", "lightbox2": "^2.11.4", "mapbox-gl": "^3.1.2", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "ng-agm-core-lib": "1.0.5", "ngx-dropzone-wrapper": "^17.0.0", "ngx-mask": "17.1.8", "ngx-mat-select-search": "8.0.1", "ngx-print": "^2.0.0", "oidc-client": "^1.11.5", "pdfmake": "^0.2.9", "perfect-scrollbar": "^1.5.5", "rrule": "2.8.1", "rxjs": "^7.8.1", "text-mask-addons": "3.8.0", "three": "^0.161.0", "tinycolor2": "^1.6.0", "uuid": "^9.0.1", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "19.2.12", "@angular/cli": "^19.0.0", "@angular/compiler-cli": "^19.0.0", "@angular/language-service": "^19.0.0", "@ngrx/schematics": "19.0.0", "@types/bootstrap": "^5.2.10", "@types/file-saver": "^2.0.7", "@types/jasmine": "5.1.4", "@types/jquery": "3.5.29", "@types/lodash": "4.14.202", "@types/node": "20.11.19", "@types/pdfmake": "0.2.11", "@types/uuid": "10.0.0", "@types/yargs": "17.0.32", "cross-env": "^7.0.3", "increase-memory-limit": "^1.0.7", "jasmine-core": "^5.1.1", "jasmine-spec-reporter": "^7.0.0", "karma": "^6.4.2", "karma-chrome-launcher": "^3.2.0", "karma-cli": "^2.0.0", "karma-coverage-istanbul-reporter": "^3.0.3", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "sass": "^1.71.0", "tslib": "^2.6.2", "typescript": "~5.8.3"}}