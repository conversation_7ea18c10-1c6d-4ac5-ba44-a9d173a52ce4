import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { PermissionsEnum } from '../../../../core/auth/permissions.enum';
import { LocationSubtypeStoreActions, LocationSubtypeStoreSelectors, LocationSubtypeStoreState } from '../../../../root-store/location-subtype-store';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { RouterOutlet } from '@angular/router';
import { IdentityClientModule } from '@econolite/identity-client';
import { BlockUIDirective } from '../../../../shared/block-ui/block-ui.directive';

@Component({
  selector: 'mms-location-subtypes',
  templateUrl: './location-subtypes.component.html',
  styleUrls: ['./location-subtypes.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    RouterOutlet,
    BlockUIDirective,
    // IdentityClientModule
  ]
})
export class LocationSubtypesComponent implements OnInit {

  locationSubtypes$ = this.store$.select(LocationSubtypeStoreSelectors.selectedFilteredLocationSubtypes);
  error$ = this.store$.select(LocationSubtypeStoreSelectors.selectLocationSubtypeError);
  isLoading$ = this.store$.select(LocationSubtypeStoreSelectors.selectLocationSubtypeIsLoading);
  isSearchActive$ = this.store$.select(LocationSubtypeStoreSelectors.selectSearchActive);
  selectedLocationSubtype$ = this.store$.select(LocationSubtypeStoreSelectors.selectedLocationSubtype);
  Permissions = PermissionsEnum;
  @ViewChild('searchInput', { static: false }) searchInput!: ElementRef;

  constructor(private title: Title, private store$: Store<LocationSubtypeStoreState.State>) {
  }

  ngOnInit() {
      this.store$.dispatch(new LocationSubtypeStoreActions.LoadRequestAction());
      this.title.setTitle('Location Categories');
  }

  add(): void {
      this.store$.dispatch(new LocationSubtypeStoreActions.AddNewAction());
  }

  delete() {
    this.store$.dispatch(new LocationSubtypeStoreActions.CanBeDeletedAction());
  }

  activateSearch() {
      this.store$.dispatch(new LocationSubtypeStoreActions.ActivateSearchAction());
      setTimeout(() => {
          this.searchInput.nativeElement.focus();
        }, 20);
  }

  cancelSearch() {
      this.store$.dispatch(new LocationSubtypeStoreActions.DeactivateSearchAction())
  }

  searchChange(value: string) {
      this.store$.dispatch(new LocationSubtypeStoreActions.SearchTextChangedAction(value))
  }

}
