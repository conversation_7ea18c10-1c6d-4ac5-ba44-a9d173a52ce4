import { SummaryStatistic } from './../../configuration/shared/models/summary-statistic.model';
import { EntityAdapter, createEntityAdapter, EntityState } from '@ngrx/entity';
import { GridStoreState } from '../shared-store/material-grid-store';
import { Ticket } from '../../configuration/tickets/tickets.model';
import { LocationInstanceGridModel } from '../../configuration/location-instances/shared/location-instance-grid-element';
import { Caller } from '../../configuration/callers/callers.model';
import { Filter } from '../../configuration/tickets/filter/filter.model';
import { User } from '../../configuration/shared/user-and-groups/user.model';
import { CallerCallHistory } from '../../configuration/shared/callers-details/caller-call-history.model';
import { Project } from '../../configuration/projects/models/project.model';
import { TicketType } from '../../configuration/ticket-types/models/ticket-type.model';
import { TicketQuickFilter } from '../../configuration/tickets/filter/quick-filter.model';
import { Contract } from '../../payroll-and-billing/contracts/models/contract.model';



export const featureAdapter: EntityAdapter<Ticket> = createEntityAdapter<Ticket>( {
    selectId: model => model.id!
});

export interface State {
    main: MainState;
    grid: GridStoreState.State<Ticket>;
    ticketListDashboardGrid: GridStoreState.State<Ticket>;
}


export interface MainState extends EntityState<Ticket> {
    isLoading?: boolean;
    initialLoad: boolean;
    isSidebarLoading?: boolean;
    searchText: string | null;
    searchActive: boolean;
    callersList: Caller[];
    ticketFormData: Ticket | null;
    returnPath: string | null;
    selectedFilter: Filter | null;
    users: User[];
    callerDetails: CallerCallHistory[];
    ticketNumber: Number | null;
    locationTickets: Ticket[];
    selectedTicketProject: Project | null;
    filteredTypesByProject: TicketType[];
    showAuxData: boolean;
    showTag: boolean;
    latestTicketNo: number;
    quickFilter: TicketQuickFilter;
    selectedContract: Contract | null;
    summaryStatistic: SummaryStatistic | null;
    averageDailyResponseChartData: any;
    averageWeeklyResponseChartData: any;
    averageMonthlyResponseChartData: any;
    ticketDashboardSearchText: string | null;
    ticketDashboardSearchActive: boolean;
    allTickets: Ticket[] | null;
    selectedOneTicket: Ticket | null;
}

export const initialState: MainState = featureAdapter.getInitialState({
        isLoading: false,
        initialLoad: true,
        isSidebarLoading: false,
        searchText: null,
        searchActive: false,
        locationList: Array<LocationInstanceGridModel>(),
        callersList: Array<Caller>(),
        ticketFormData: null,
        returnPath: null,
        selectedFilter: null,
        users:  Array<User>(),
        callerDetails: Array<CallerCallHistory>(),
        ticketNumber: null,
        locationTickets: Array<Ticket>(),
        selectedTicketProject: null,
        filteredTypesByProject: [],
        showAuxData: true,
        showTag: true,
        latestTicketNo: 0,
        quickFilter: { callType: true, woType: true, pmType: false, finalized: false },
        selectedContract: null,
        summaryStatistic: null,
        averageDailyResponseChartData: null,
        averageWeeklyResponseChartData: null,
        averageMonthlyResponseChartData: null,
        ticketDashboardSearchText: null,
        ticketDashboardSearchActive: false,
        allTickets: null,
        selectedOneTicket: null
});

export const TICKET_GRID_ID = 'TICKETS';
export const TICKETS_LIST_DASHBOARD_GRID_ID = 'TICKETS_LIST_DASHBOARD_GRID_ID';

