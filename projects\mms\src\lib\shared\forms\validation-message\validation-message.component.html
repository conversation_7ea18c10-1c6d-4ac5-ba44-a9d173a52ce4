<ng-container *ngIf="formControl">
<span *ngIf="formControl.hasError('required')">
    {{formControl.errors["required"].message!=null? formControl.errors["required"].message: "Required"}}
</span>
<span *ngIf="formControl.hasError('minlength')">
   {{formControl.errors["minlength"].message!=null? formControl.errors["minlength"].message: "Minimum length is " + formControl.errors["minlength"].requiredLength}}
</span>
<span *ngIf="formControl.hasError('maxlength')">
   {{formControl.errors["maxlength"].message!=null? formControl.errors["maxlength"].message: "Max length is " + formControl.errors["maxlength"].requiredLength}}
</span>
<span *ngIf="formControl.hasError('maxDate')">
   {{formControl.errors["maxDate"].message!=null? formControl.errors["maxDate"].message: "Maximum date is " + formControl.errors["maxDate"].maxDate.toDateString()}}
</span>
<span *ngIf="formControl.hasError('minDate')">
   {{formControl.errors["minDate"].message!=null? formControl.errors["minDate"].message: "Minimum date is " + formControl.errors["minDate"].minDate.toDateString()}}
</span>
<span *ngIf="formControl.hasError('min')">
   {{formControl.errors["min"].message!=null? formControl.errors["min"].message: "Minimum value is " + formControl.errors["min"].min}}
</span>
<span *ngIf="formControl.hasError('max')">
   {{formControl.errors["max"].message!=null? formControl.errors["max"].message: "Maximum value is " + formControl.errors["max"].max}}
</span>
<span *ngIf="formControl.hasError('email')">
   {{formControl.errors["email"].message!=null? formControl.errors["email"].message: "Email is not valid"}}
</span>
<span *ngIf="formControl.hasError('emoji')">
    {{formControl.errors["emoji"].message!=null? formControl.errors["emoji"].message: "Invalid characters entered."}}
 </span>
 <span *ngIf="formControl.hasError('phone')">
    {{formControl.errors["phone"].message!=null? formControl.errors["phone"].message: "Phone Number is not valid"}}
 </span>
 <span *ngIf="formControl.hasError('pattern')">
   {{formControl.errors["pattern"].message!=null? formControl.errors["pattern"].message: "Not valid"}}
</span>
<span *ngIf="formControl.hasError('regularExpression')">
    {{formControl.errors["regularExpression"].message!=null? formControl.errors["regularExpression"].message: "Not valid"}}
</span>
<span *ngIf="formControl.hasError('integer')">
    {{formControl.errors["integer"].message!=null? formControl.errors["integer"].message: "Not a valid integer"}}
</span> 
<span *ngIf="formControl.hasError('greaterThan')">
    {{formControl.errors["greaterThan"].message!=null? formControl.errors["greaterThan"].message: 
        formControl.errors["greaterThan"].controlToValidateName + 
        " cannot be smaller than " + 
        formControl.errors["greaterThan"].controlToCompareName 
    }}
</span>
<span *ngIf="formControl.hasError('greaterThanField')">
    {{formControl.errors["greaterThanField"].message!=null? formControl.errors["greaterThanField"].message: 
        formControl.errors["greaterThanField"].controlToValidateName + 
        " cannot be smaller than " + 
        formControl.errors["greaterThanField"].controlToCompareName 
    }}
</span>
<span *ngIf="formControl.hasError('lessThan')">
    {{formControl.errors["lessThan"].message!=null? formControl.errors["lessThan"].message: 
        formControl.errors["lessThan"].controlToValidateName + 
        " cannot be bigger than " + 
        formControl.errors["lessThan"].controlToCompareName 
    }}
</span>
<span *ngIf="formControl.hasError('lessThanField')">
    {{formControl.errors["lessThanField"].message!=null? formControl.errors["lessThanField"].message: 
        formControl.errors["lessThanField"].controlToValidateName + 
        " cannot be bigger than " + 
        formControl.errors["lessThanField"].controlToCompareName 
    }}
</span>
<span *ngIf="formControl.hasError('async')">
    {{formControl.errors["async"].message}}
</span>
<span *ngIf="formControl.hasError('wrongFileType')">
{{ formControl.errors["wrongFileType"].message!=null? formControl.errors["wrongFileType"].message: "You can't upload files of this type" }}
</span>
<span *ngIf="formControl.hasError('custom')">
    {{formControl.errors["custom"].message}}
</span> 
<span *ngIf="formControl.hasError('allowedCharacters')">
    {{formControl.errors["allowedCharacters"].notAllowedCharacter}} Not Allowed
</span>
<span *ngIf="formControl.hasError('sameAsPattern')">
    Not valid. Valid format example: {{formControl.errors["sameAsPattern"].pattern}}
</span>  
<span *ngIf="formControl.hasError('sameAs')">
    {{formControl.errors["sameAs"].message!=null? formControl.errors["sameAs"].message: "Passwords are not matching."}}
</span>
</ng-container>