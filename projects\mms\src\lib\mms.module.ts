import { PriorityModule } from './configuration/priority/priority.module';
import { InvoicesModule } from './payroll-and-billing/invoices/invoices.module';
import { NgModule, ModuleWithProviders } from '@angular/core';
import { CommonModule, DatePipe, CurrencyPipe} from '@angular/common'
import { Config } from './core/config.model';
import { CoreModule } from './core/core.module';

import { NgxMaskDirective, NgxMaskPipe, provideNgxMask } from 'ngx-mask'
// import { AgmCoreModule } from 'ng-agm-core-lib';
import { MmsRoutingModule } from './mms-routing.module';
import { TypeAndFieldDefinitionsModule } from './configuration/type-and-field-definitions/type-and-field-definitions.module';
import { MmsComponent } from './mms.component';
import { MMSConfigModule } from './settings/mms_config/mms-config.module';
import { ManufacturersModule } from './configuration/manufacturers/manufacturers.module';
import { SharedFieldDefinitionsModule } from './configuration/shared-field-definitions/shared-field-definitions.module';
import { LocationInstancesModule } from './configuration/location-instances/location-instances.module';
import { TestComponent } from './test/test.component';
import { MaterialModule } from './material.module';
import { RootStoreModule } from './root-store';
import { MasterInventoryModule } from './configuration/master-inventory/master-inventory.module'
import { PmChecklistModule } from './configuration/pm-checklist/pm-checklist.module';
import { RegionsModule } from './configuration/regions/regions.module';
import { PMChecklistItemModule } from './configuration/pm-checklist-items/pm-checklist-items.module';
import { SchedulerModule } from './scheduler/scheduler.module';
import { TicketsModule } from './configuration/tickets/tickets.module';
import { CallersModule } from './configuration/callers/callers.module';
import { WorkordersModule } from './configuration/tickets/workorders/workorders.module';
import { ReportsModule } from './reports/reports.module';
import { AuthGuard } from './core/auth/auth.guard';
import { NgxPrintModule} from 'ngx-print';
import { CanDeactivateGuard } from './core/can-deactivate.guard';
import { ProjectsModule } from './configuration/projects/projects.module';
import { AgenciesModule } from './configuration/agencies/agencies.module'
import { ProjectTypesModule } from './configuration/project-types/project-types.module';
import { TicketTypesModule } from './configuration/ticket-types/ticket-types.module';
import { ReasonCodesModule } from './configuration/reason-codes/reason-codes.module';
import { ProjectSchedulerModule } from './project-scheduler/project-scheduler.module';
import { AlertsModule } from './configuration/alerts/alerts.module';
import { OnCallSchedulerModule } from './on-call-scheduler/on-call-scheduler.module';
import { TagsModule } from './settings/tags/tags.module';
import { JobCodesModule } from './configuration/job-codes/job-codes.module';
import { EmployeesModule } from './configuration/employees/employees.module';
import { HolidayModule } from './payroll-and-billing/holidays/holiday.module';
import { ShiftModule } from './payroll-and-billing/shifts/shift.module';
import { JurisdictionModule } from './configuration/jurisdiction/jurisdiction.module';
import { ContractModule } from './payroll-and-billing/contracts/contract.module';
import { MapLayersModule } from './settings/map-layers/map-layers.module';
import { MmsMapModule } from './mms-map/mms-map.module';
import { MapConfigurationModule } from './settings/map-configuration/map-configuration.module';
import { LocationSubtypeModule } from './configuration/location-subtype/location-subtype.module';
import { TimesheetsModule } from './payroll-and-billing/timesheets/timesheets.module';
import { BillingModule } from './payroll-and-billing/billing/billing.module';
import { WarehousesModule } from './configuration/warehouses/warehouses.module';
import { FailureTypesModule } from './configuration/failure-types/failure-types.module';
import { CostListModule } from './payroll-and-billing/inventory-cost-lists/cost-list.module';
import { QuotesModule } from './configuration/quotes/quotes.module';
import { ReorderedEquipmentModule } from './configuration/reordered-equipment/reordered-equipment.module';
import { ConfigurationSharedModule } from './configuration/shared/configuration-shared.module';

@NgModule({
  imports: [
    CommonModule,
    CoreModule,
    MmsRoutingModule,
    TypeAndFieldDefinitionsModule,
    MMSConfigModule,
    ManufacturersModule,
    SharedFieldDefinitionsModule,
    LocationInstancesModule,
    MasterInventoryModule,
    PMChecklistItemModule,
    PmChecklistModule,
    SchedulerModule,
    TicketsModule,
    CallersModule,
    WorkordersModule,
    AgenciesModule,
    JobCodesModule,
    // Material ans shared module is needed for TestComponent, remove this with TestComponent
    MaterialModule,
    RootStoreModule,
    RegionsModule,
    ReportsModule,
    NgxMaskDirective,
    NgxMaskPipe,
    // AgmCoreModule.forRoot(),
    NgxPrintModule,
    ProjectsModule,
    ProjectTypesModule,
    TicketTypesModule, 
    ReasonCodesModule,
    ProjectSchedulerModule,
    AlertsModule,
    OnCallSchedulerModule,
    TagsModule,
    EmployeesModule,
    HolidayModule,
    JurisdictionModule,
    ShiftModule,
    ContractModule,
    MapLayersModule,
    MmsMapModule,
    MapConfigurationModule,
    LocationSubtypeModule,
    TimesheetsModule,
    BillingModule,
    InvoicesModule,
    WarehousesModule,
    PriorityModule,
    FailureTypesModule,
    CostListModule,
    QuotesModule,
    ReorderedEquipmentModule,
    ConfigurationSharedModule,
    TestComponent,
    MmsComponent
  ],
  providers: [AuthGuard, DatePipe, CanDeactivateGuard, CurrencyPipe, provideNgxMask()],
})
export class MmsModule {
  static forRoot(config?: Config): ModuleWithProviders<MmsModule> {
    return {
      ngModule: MmsModule,
      providers: [
        {
          provide: Config,
          useValue: config
        }
      ]
    }
  }
}
