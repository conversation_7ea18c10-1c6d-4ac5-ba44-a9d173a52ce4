import { WarehouseReportService } from './../../configuration/warehouses/services/warehouse-report.service';
import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { of as observableOf, of } from 'rxjs';
import { catchError, map, switchMap, withLatestFrom, exhaustMap } from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state'
import * as featureSelectors from './selectors'

import { Router } from '@angular/router';
import { Page } from '../../core/page.service';
import { UserService } from '../../configuration/shared/user-and-groups/users.service';
import { UserIdentity } from '@econolite/identity-client';
import { MatDialog } from '@angular/material/dialog';
import { ProjectService } from '../../configuration/projects/services/projects.service';
import { LocationInstancesService } from '../../configuration/location-instances/shared/location-instances.service';
import { WarehouseService } from '../../configuration/warehouses/services/warehouse.service';
import { PermissionsEnum } from '../../core/auth/permissions.enum';

@Injectable()
export class WarehouseStoreEffects {
    constructor(
        private router: Router,
        private warehouseService: WarehouseService,
        private projectService: ProjectService,
        private actions$: Actions,
        private store$: Store<featureState.State>,
        private page: Page,
        private userService: UserService,
        private userIdentity: UserIdentity,
        private locationInstanceService: LocationInstancesService,
        private warehouseReportService: WarehouseReportService,
        public dialog: MatDialog) { }

    loadWarehousesRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadRequestAction>(
            featureActions.ActionTypes.LOAD_REQUEST
        ),
        switchMap(_ => 
            this.warehouseService.getWarehouses(this.userIdentity.hasPermission([PermissionsEnum.SeeAllAgencies])).pipe(
                map(items => new featureActions.LoadSuccessAction({
                    items
                })),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        )
    ));

    updateRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateWarehouseRequestAction>(
            featureActions.ActionTypes.UPDATE_WAREHOUSE_REQUEST
        ),
        withLatestFrom(this.store$.select(featureSelectors.selectedWarehouse)),
        exhaustMap(([action, warehouseInventory]) => {
            const warehouse = { ...warehouseInventory.warehouse, lowThreshold: action.payload.lowThreshold };

            return this.locationInstanceService
                .updateLocationInstance(warehouse as any)
                .pipe(
                    map(
                        () =>
                            new featureActions.UpdateWarehouseSuccessAction({ item: { id: warehouse.id, changes: warehouse } })
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadFailureAction({ error }))
                    )
                )
        })
    ));

    updateWarehouseSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateWarehouseSuccessAction>(
            featureActions.ActionTypes.UPDATE_WAREHOUSE_SUCCESS
        ),
        map((_) => {
            return new featureActions.LoadRequestAction();
        })
    ));

    loadAssetInventoryTypeReportDataRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.GetAssetInventoryTypeReportDataRequest>(
            featureActions.ActionTypes.GET_ASSET_INVENTORY_TYPE_REPORT_DATA_REQUEST
        ),
        switchMap(_ =>
            this.warehouseService.getAssetInventoryTypeReportData().pipe(
                map(items => {
                    this.warehouseReportService.generateAssetInventoryTypeQuantityPDF(items);
                    return new featureActions.GetAssetInventoryTypeReportDataSuccess();
                }),
                catchError(error => {
                    console.log(error);
                    return of(new featureActions.LoadFailureAction({ error }));
                })
            )
        )
    ));

    loadSelectedWarehouseRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadSelectedWarehouse>(
            featureActions.ActionTypes.LOAD_SELECTED_WAREHOUSE
        ),
        exhaustMap((action) => {
            return this.warehouseService
                .getInventoriesOftWarehouse(action.id)
                .pipe(
                    map(
                        (warehouse) =>
                            new featureActions.LoadSelectedWarehouseSuccess({ warehouseInventory: warehouse})
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadFailureAction({ error }))
                    )
                )
        })
    ));
}
