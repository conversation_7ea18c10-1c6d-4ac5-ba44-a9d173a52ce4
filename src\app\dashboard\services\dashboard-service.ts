import { HttpClient } from '@angular/common/http';
<<<<<<< HEAD
import { Injectable, Inject } from '@angular/core';
import { Observable, of } from 'rxjs';
=======
import { Injectable } from '@angular/core';
import { map, Observable, of } from 'rxjs';
>>>>>>> fc58fb675dd045332fb1a43a12123dfb044441ba
import { Dashboard, DashboardServiceInterface } from '../../../../projects/dashboard/src';
import { UserIdentity } from '@econolite/identity-client';

@Injectable({
  providedIn: 'root'
})
export class DashboardService implements DashboardServiceInterface {
  
<<<<<<< HEAD
  // Mock data for development
  private mockDashboards: Dashboard[] = [
    {
      id: '1',
      name: 'Default Dashboard',
      added: false,
      editing: false,
      rows: [
        {
          title: 'Dashboard Row 1',
          height: 'fit',
          columns: [
            {
              index: 0,
              component: 'blank',
              width: '100%',
              height: 'fit',
              properties: null
            }
          ]
        }
      ]
    }
  ];

  constructor(private httpClient: HttpClient, @Inject(UserIdentity) private userIdentity: UserIdentity) {
=======
  constructor(private httpClient: HttpClient, private userIdentity: UserIdentity) {
>>>>>>> fc58fb675dd045332fb1a43a12123dfb044441ba
  }

  getUserDashboards(): Observable<Array<Dashboard>> {
    return this.httpClient.get<Array<Dashboard>>('user/v1/dashboard')
      .pipe(map((result: any) => { return result }));
  }

  saveUserDashboard(dashboard: Dashboard): Observable<Dashboard> {
    (dashboard as any).tenantId = this.userIdentity.tenantId;
    return this.httpClient.put<Dashboard>('user/v1/dashboard', dashboard);
  }

  deleteUserDashboard(dashboard: Dashboard): Observable<boolean> {
    var id = dashboard.id;
    return this.httpClient.delete('user/v1/dashboard/' + id )
      .pipe(map(() => {
        return true;
      }))
  }
}