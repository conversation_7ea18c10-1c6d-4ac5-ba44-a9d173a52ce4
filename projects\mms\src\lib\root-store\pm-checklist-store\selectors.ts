import { MemoizedSelector, createFeatureSelector, createSelector } from '@ngrx/store';

import { State, featureAdapter, MainState } from './state';
import { RouterStoreSelectors } from '../router-store';
import { GridStoreState } from '../shared-store/material-grid-store';
import { ChecklistItem } from '../../configuration/pm-checklist-items/shared/checklist-item.model';
import { Checklist } from '../../configuration/pm-checklist/checklist.model';
import { ChecklistItemGroup } from '../../configuration/pm-checklist-items/shared/checklist-item-group.model';
import * as equal from 'fast-deep-equal';
import { ChecklistInstance } from '../../configuration/pm-checklist-items/checklistInstance.model';

export const selectChecklistState: MemoizedSelector<object, State> = createFeatureSelector<State>('pmChecklist');

export const selectMainState: MemoizedSelector<object, MainState> = createSelector(
    selectChecklistState,
    checklistState => checklistState.main
);

export const selectGridState: MemoizedSelector<object, GridStoreState.State<ChecklistItem>> = createSelector(
    selectChecklistState,
    checklistState => checklistState.grid
);

export const selectGrid: MemoizedSelector<object, GridStoreState.State<ChecklistItem>> = createSelector(
    selectChecklistState,
    (state): GridStoreState.State<ChecklistItem> => state.grid
);

export const selectGridItems: MemoizedSelector<object, ChecklistItem[]> = createSelector(
    selectGridState,
    (gridState) => gridState.items
);

export const selectCkeckListInstancesGrid: MemoizedSelector<object, GridStoreState.State<ChecklistInstance>> = createSelector(
    selectChecklistState,
    (state): GridStoreState.State<ChecklistInstance> => state.gridChecklistInstances
);

export const selectAllChecklists: (state: object) => Checklist[]
    = featureAdapter.getSelectors(selectMainState).selectAll;

export const selectChecklistIsLoading: MemoizedSelector<object, boolean> = createSelector(
    selectMainState,
    (mainState): boolean => !!mainState.isLoading
);

export const selectChecklistError: MemoizedSelector<object, any> = createSelector(
    selectMainState,
    (mainState): any => mainState.error
);

export const selectFilteredChecklists: MemoizedSelector<object, Checklist[]> = createSelector(
    selectMainState,
    selectAllChecklists,
    (state, checklists): Checklist[] => {
        return checklists.filter(x => state.searchText == null || x.name && x.name.toLocaleLowerCase().indexOf(state.searchText.toLocaleLowerCase()) > -1)
    }
);

export const selectChecklistItemGroups: MemoizedSelector<object, ChecklistItemGroup[]> = createSelector(
    selectMainState,
    (mainState): ChecklistItemGroup[] => mainState.checklistItemGroups
);

export const selectSearchActive: MemoizedSelector<object, boolean> = createSelector(
    selectMainState,
    (mainState): boolean => mainState.searchActive
);

export const selectSelectedChecklist: MemoizedSelector<object, Checklist | undefined> = createSelector(
    selectMainState,
    RouterStoreSelectors.getRouterStoreState,
    (mainState, routerState): Checklist | undefined => {
        if (routerState && routerState.state.params['id'] && mainState.entities[routerState.state.params['id']]) {
            return mainState.entities[routerState.state.params['id']];
        }
        else if(routerState && routerState.state.url.indexOf('create-event') > -1){
            let id= routerState.state.url.split('/')[3];
            return mainState.entities[id];
        }
    }
);

export const selectSelectedChecklistItems: MemoizedSelector<object, ChecklistItem[]> = createSelector(
    selectMainState,
    (mainState): ChecklistItem[] => mainState.selectedChecklistsItems
);

export const selectChecklistItemsChanged: MemoizedSelector<object, boolean> = createSelector(
    selectGrid,
    selectSelectedChecklist,
    (grid, checklist): boolean => {
        if (checklist && checklist.checklistItems) {
            return checklist.checklistItems.length !== grid.items.length || grid.items.some((value, index) => {
                if(checklist.checklistItems)
                    return (!equal(value, checklist.checklistItems[index]));
                return false;
            })
        }

        return checklist == null && grid.items.length>0;
    }
);

export const selectShowSidebar: MemoizedSelector<object, boolean> = createSelector(
    selectMainState,
    (mainState) => mainState.showSidebar
);

export const selectShowSidebarItems: MemoizedSelector<object, boolean> = createSelector(
    selectMainState,
    (mainState) => mainState.showSidebarItems 
);

export const selectShowSidebarCreateEvent: MemoizedSelector<object, boolean> = createSelector(
    selectMainState,
    RouterStoreSelectors.getRouterStoreState,
    (mainState, routerState) => mainState.showSidebarCreateEvent && routerState && routerState.state.url.indexOf('create-event') == -1
);


export const selectChecklistInstancesRelatedToLocation: MemoizedSelector<object, ChecklistInstance[]> = createSelector(
    selectMainState,
    RouterStoreSelectors.getRouterStoreState,
    (mainState, routerState): ChecklistInstance[] => {
        let locationChecklists = new Array<ChecklistInstance>()
        if (routerState && routerState.state.params['locationId'] && mainState.checklistInstances) {
           
            locationChecklists = mainState.checklistInstances.filter(x => x.locationId === routerState.state.params['locationId']);
        }
        return locationChecklists;
    }
);

export const selectNotSchedulableChecklists = createSelector(
    selectMainState,
    (mainState) => mainState.notSchedulableChecklists
);

export const selectSchedulableChecklists = createSelector(
    selectAllChecklists,
    (checklists) => checklists.filter(x => x.notSchedulable !== true)
);

export const getChecklistsForImport = createSelector(
    selectMainState,
    (mainState) => mainState.checklistsForImport
);

export const selectChecklistForLocationType = createSelector(
    selectSchedulableChecklists,
    (checklists) => {
      var unspecified = { id: '0', name: 'Unspecified', description: '', checklistItems: [], notSchedulable: false, agencyId: ''} as Checklist
      checklists.unshift(unspecified);

      return checklists;
    }
);

