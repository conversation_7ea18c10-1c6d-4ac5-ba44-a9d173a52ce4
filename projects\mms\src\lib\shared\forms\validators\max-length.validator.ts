﻿import { Validator, AbstractControl, ValidationErrors, FormControl, FormArray } from '@angular/forms'

export class MaxLengthValidator implements Validator {

    constructor(private maxLength: number) {
    }

    validate(control: AbstractControl): ValidationErrors | null {
        if (control instanceof FormControl || control instanceof FormArray) {
            if (control.value == null || ((typeof control.value === 'string')
            && (control.value.trim() === '' || control.value.trim().length <= this.maxLength))
            || ((control.value instanceof Array) && (<Array<any>>control.value).length <= this.maxLength)) {
                return null;
            }

            return {
                maxlength: {
                    current: control.value,
                    requiredLength: this.maxLength
                }
            }
        }
        return null;
    }
}
