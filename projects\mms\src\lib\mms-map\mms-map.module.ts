import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MmsMapService } from './mms-map.service';
import { MmsMapComponent } from './mms-map.component';
import { MapFilterComponent } from './filter/map-filter.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CentracsMapComponent } from './components/centracs-map/centracs-map.component';
import { LimestoneModule } from '@econolite/limestone';
import { ToolkitModule } from '@econolite/toolkit';
import { ResizableModule } from 'angular-resizable-element';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';

@NgModule({
  providers: [MmsMapService],
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    LimestoneModule,
    ToolkitModule.forRoot(),
    ReactiveFormsModule,
    FormsModule,
    ResizableModule,
    MmsMapComponent,
    // Standalone components
    MapFilterComponent,
    CentracsMapComponent
  ],
  exports: [
    MmsMapComponent,
    CentracsMapComponent
  ]
})
export class MmsMapModule { }
