import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Page } from '../../core/page.service';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { of, forkJoin } from 'rxjs';
import { map, catchError, concatMap, switchMap, tap, withLatestFrom, exhaustMap, filter } from 'rxjs/operators';
import * as featureActions from './actions';
import * as featureState from './state';
import { ProjectStoreSelectors } from '.';
import { AgencyStoreSelectors } from '../agency-store';
import { Project } from '../../configuration/projects/models/project.model';
import { ProjectService } from '../../configuration/projects/services/projects.service';
import { GridStoreActions } from '../shared-store/material-grid-store';
import { RouterStoreSelectors } from '../router-store';
import { SidebarType } from '../../configuration/projects/enums/sidebar-type.enum';
import { TicketService } from '../../configuration/tickets/tickets.service';
import { PagedResults } from '../../configuration/projects/models/paged-results.model';
import { RegionsService } from '../../configuration/regions/regions.service';
import { ProjectTypesService } from '../../configuration/project-types/services/project-types.service';
import { displayMessages } from '../../core/resources/display-messages';
import { UserIdentity } from '@econolite/identity-client';
import { GridLoadItemsAction } from '../shared-store/material-grid-store/actions';

@Injectable()
export class ProjectsStoreEffects {
    constructor(
        private readonly actions$: Actions,
        private readonly page: Page,
        private readonly router: Router,
        private readonly projectService: ProjectService,
        private readonly ticketService: TicketService,
        private readonly projectTypesService: ProjectTypesService,
        private readonly store$: Store<featureState.State>,
        private readonly regionService: RegionsService,
        private readonly userIdentity: UserIdentity
    ) {}

    loadRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType(featureActions.ActionTypes.LOAD_PROJECTS_REQUEST),
        switchMap(() => of(new GridLoadItemsAction(featureState.PROJECTS_GRID_ID)))
    ));

    gridLoadSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType(GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_SUCCESS(featureState.PROJECTS_GRID_ID)),
        map(() => new featureActions.NoAction())
    ));

    gridLoadItems$ = createEffect(() => this.actions$.pipe(
        ofType(GridStoreActions.ActionTypes.GRID_LOAD_ITEMS(featureState.PROJECTS_GRID_ID)),
        withLatestFrom(
            this.store$.select(RouterStoreSelectors.getRouterStoreState),
            this.store$.select(ProjectStoreSelectors.getNextPageToken),
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
            this.store$.select(ProjectStoreSelectors.selectSelectedFilter),
            this.store$.select(ProjectStoreSelectors.selectInitialLoad),
            this.store$.select(ProjectStoreSelectors.getIsClearGrid)
        ),
        switchMap(([action, routerState, nextPageToken, agency, selectedFilter, initialLoad, isClear]) => {
            if (!agency) {
                return of(new GridStoreActions.GridNoAction());
            }

            const filter = {
                ...selectedFilter,
                agencyId: selectedFilter?.agencyId && selectedFilter.agencyId !== agency.id ? selectedFilter.agencyId : agency.id,
                contractId: routerState.state.params['contractId']
            };

            return this.projectService.getProjects(filter, (action as any).payload?.nextPageToken ?? null).pipe(
                concatMap((result: PagedResults<Project>) => {
                    const items = (result?.items ?? []).map((item: Project) => ({
                        ...item,
                        defaultLocations: (item.defaultLocations || []).concat(item.regions || [])
                    }));

                    return [
                        new featureActions.LoadProjectsAllStatus(),
                        new featureActions.LoadProjectsSuccessAction({ 
                            projects: items, 
                            nextPageToken: result.nextPageToken 
                        }),
                        new GridStoreActions.GridLoadItemsSuccessAction(
                            featureState.PROJECTS_GRID_ID,
                            { 
                                items, 
                                nextPageToken: result.nextPageToken, 
                                clearGrid: !!(selectedFilter && !(action as any).payload) || isClear 
                            }
                        )
                    ];
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            );
        }),
        catchError(error => of(new GridStoreActions.GridLoadItemsFailedAction(featureState.PROJECTS_GRID_ID, error)))
    ));

    gridLoadItemsSuccess$ = createEffect(() => this.actions$.pipe(
        ofType(GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_SUCCESS(featureState.PROJECTS_GRID_ID)),
        map(() => new GridStoreActions.GridNoAction())
    ));

    gridLoadItemsFailed$ = createEffect(() => this.actions$.pipe(
        ofType(GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_FAILED(featureState.PROJECTS_GRID_ID)),
        map(() => new GridStoreActions.GridNoAction())
    ));

    showSidebarEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ShowSidebarAction>(featureActions.ActionTypes.SHOW_SIDEBAR),
        withLatestFrom(
            this.store$.select(ProjectStoreSelectors.getProjectsAllStatus),
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
        ),
        switchMap(([action, projects, agency]) => {
            if (!agency) {
                return of(new featureActions.LoadFailureAction({ error: 'No agency selected' }));
            }

            return forkJoin([
                this.projectTypesService.getTypes(),
                this.regionService.getRegions(agency)
            ]).pipe(
                map(([types, regions]) => {
                    const project = action.payload.projectId ? projects.find(x => x.id === action.payload.projectId) : action.payload.project;
                    if (!project) {
                        return new featureActions.LoadFailureAction({ error: 'Project not found' });
                    }
                    project.hasOpenTickets = action.payload.hasOpenTicket;
                    return new featureActions.LoadSidebarDataAction({
                        types,
                        regions,
                        urlSuffix: action.payload.urlSuffix,
                        selectedProject: project
                    });
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            );
        })
    ));

    updateProjectSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateProjectSuccessAction>(featureActions.ActionTypes.UPDATE_PROJECT_SUCCESS),
        map((action) => {
            this.page.notification.show('Project updated');
            this.router.navigate(['/projects/manage-projects'], { state: { shouldDeactivate: true } });
            return new GridStoreActions.GridItemUpdatedAction(
                featureState.PROJECTS_GRID_ID, 
                { 
                    itemId: action.payload.project.id, 
                    item: action.payload.project.changes 
                }
            );
        })
    ));

    saveProjectEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SaveProjectAction>(featureActions.ActionTypes.SAVE_PROJECT),
        withLatestFrom(this.store$.select(AgencyStoreSelectors.selectDefaultAgency)),
        exhaustMap(([action, agency]) => {
            if (!agency) {
                return of(new featureActions.LoadFailureAction({ error: 'No agency selected' }));
            }
            const project = {
                ...action.payload.project,
                agency
            };
            return this.projectService.createProject(project).pipe(
                filter((createdProject): createdProject is Project => createdProject !== null),
                concatMap((createdProject: Project) => {
                    this.page.notification.show('Project added');
                    this.router.navigate(['/projects/manage-projects'], { state: { shouldDeactivate: true } });
                    createdProject.defaultLocations = createdProject.defaultLocations && createdProject.defaultLocations.length ? 
                        createdProject.defaultLocations.concat(createdProject.regions) : createdProject.regions;
                    return [
                        new GridStoreActions.GridLoadItemsAction(featureState.PROJECTS_GRID_ID),
                        new featureActions.SaveProjectSuccessAction({ project: createdProject })
                    ];
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            );
        })
    ));

    updateProjectEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateProjectAction>(featureActions.ActionTypes.UPDATE_PROJECT),
        withLatestFrom(this.store$.select(AgencyStoreSelectors.selectDefaultAgency)),
        exhaustMap(([action, agency]) => {
            if (!agency) {
                return of(new featureActions.LoadFailureAction({ error: 'No agency selected' }));
            }
            const project = {
                ...action.payload.project,
                agency
            };
            return this.projectService.updateProject(project).pipe(
                map(() => new featureActions.UpdateProjectSuccessAction({
                    project: { 
                        id: action.payload.project.id, 
                        changes: project 
                    },
                    projectsAllStatus: { 
                        id: action.payload.project.id, 
                        changes: project 
                    },
                    projects: { 
                        id: action.payload.project.id, 
                        changes: project 
                    }
                }))
            );
        })
    ));

    confirmDeleteTicketEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ConfirmDeleteAction>(featureActions.ActionTypes.CONFIRM_DELETE),
        exhaustMap((action) =>
            this.page.confirm.show('Are you sure you want to delete this project?', 'Are you sure?').pipe(
                map((result) => {
                    if (result) {
                        return new featureActions.DeleteProjectAction(action.payload);
                    }
                    return new featureActions.NoAction();
                })
            )
        )
    ));

    deleteProjectEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteProjectAction>(featureActions.ActionTypes.DELETE_PROJECT),
        switchMap((action) =>
            this.projectService.deleteProject(action.payload.projectId).pipe(
                map(() => new featureActions.DeleteProjectSuccessAction(action.payload)),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        )
    ));

    deleteSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteProjectSuccessAction>(featureActions.ActionTypes.DELETE_PROJECT_SUCCESS),
        map((action) => {
            this.page.notification.show('Project deleted');
            return new GridStoreActions.GridItemRemovedAction(featureState.PROJECTS_GRID_ID, { itemId: action.payload.projectId });
        })
    ));

    loadFilterData$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadSidebarDataAction>(featureActions.ActionTypes.LOAD_SIDEBAR_DATA),
        withLatestFrom(
            this.store$.select(RouterStoreSelectors.getRouterStoreState)
        ),
        tap(([action, routerState]) => {
            if (!routerState.state.url.includes('projects/manage-projects/') || !routerState.state.url.includes('projects/manage-projects/filter/')) {
                const id = action.payload.selectedProject.id ? `/${action.payload.selectedProject.id}` : '';
                this.router.navigate([`/projects/manage-projects/${action.payload.urlSuffix}${id}`]);
            }
        }),
        map(() => new featureActions.NoAction())
    ));

    closeFilter$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.BackToProjectsGridAction>(featureActions.ActionTypes.BACK_TO_PROJECTS_GRID),
        withLatestFrom(
            this.store$.select(RouterStoreSelectors.getRouterStoreState)
        ),
        tap(([_, routerState]) => {
            this.router.navigate(['/projects/manage-projects']);
        }),
        map(([_, routerState]) => {
            if (routerState.state.url.includes(SidebarType.Filter)) {
                return new featureActions.CloseSidebarAction();
            }
            return new featureActions.NoAction();
        })
    ));

    canBeDeletedEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.CanBeDeletedAction>(featureActions.ActionTypes.CAN_BE_DELETED),
        exhaustMap((action) =>
            this.projectService.canBeDeleted(action.payload.projectId).pipe(
                map((response) => {
                    if (!response) {
                        return new featureActions.ConfirmDeleteAction({ projectId: action.payload.projectId });
                    }
                    this.page.alert.warning(displayMessages.exceptions.itemIsAssociated('project'));
                    return new featureActions.NoAction();
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        )
    ));

    canBeClosedEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.CanBeClosedAction>(featureActions.ActionTypes.CAN_BE_CLOSED),
        exhaustMap((action) =>
            this.projectService.canBeClosed(action.payload.project.id).pipe(
                map((response: { items?: any[] }) => {
                    if (response.items && response.items.length) {
                        this.page.alert.warning(displayMessages.exceptions.projectCantBeClosed);
                        return new featureActions.NoAction();
                    }
                    return new featureActions.UpdateProjectAction({ project: action.payload.project });
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        )
    ));

    openTickets$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.OpenTicketsAction>(featureActions.ActionTypes.OPEN_TICKETS),
        tap((action) => {
            this.router.navigate([`/tickets/${action.payload.projectId}`]);
        }),
        map(() => new featureActions.NoAction())
    ));

    loadAllProjects$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadAllProjects>(featureActions.ActionTypes.LOAD_ALL_PROJECTS),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
            this.store$.select(ProjectStoreSelectors.selectSelectedFilter)
        ),
        switchMap(([_, agency, selectedFilter]) => {
            const agencyId = selectedFilter?.agencyId || (agency && agency.id);
            if (!agencyId || agencyId === "0") {
                return of(new featureActions.NoAction());
            }
            return this.projectService.getProjectsByAgency(agencyId).pipe(
                map((response: Project[]) => {
                    if (response && response.length > 0) {
                        const projects = response.filter(x => x.status === 1);
                        return new featureActions.LoadAllProjectsSuccess({ allProjects: projects });
                    }
                    return new featureActions.LoadProjectsAllStatusSuccess({ Projects: [] });
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            );
        })
    ));

    loadProjectsAllStatus$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadProjectsAllStatus>(featureActions.ActionTypes.LOAD_PROJECTS_ALL_STATUS),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
            this.store$.select(ProjectStoreSelectors.selectSelectedFilter)
        ),
        switchMap(([_, agency, selectedFilter]) => {
            const agencyId = selectedFilter?.agencyId || (agency && agency.id);
            if (!agencyId || agencyId === "0") {
                return of(new featureActions.NoAction());
            }
            return this.projectService.getProjectsByAgency(agencyId).pipe(
                map((response: Project[]) => {
                    if (response && response.length > 0) {
                        return new featureActions.LoadProjectsAllStatusSuccess({ Projects: response });
                    }
                    return new featureActions.LoadProjectsAllStatusSuccess({ Projects: [] });
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            );
        })
    ));

    loadProjectsAllAgency$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadProjectsAllAgency>(featureActions.ActionTypes.LOAD_PROJECTS_ALL_AGENCY),
        exhaustMap(() => {
            return this.projectService.getAllProjects().pipe(
                map((response: Project[]) => {
                    if (response && response.length > 0) {
                        return new featureActions.LoadProjectsAllAgencySuccess({ Projects: response });
                    }
                    return new featureActions.LoadProjectsAllAgencySuccess({ Projects: [] });
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            );
        })
    ));
}




