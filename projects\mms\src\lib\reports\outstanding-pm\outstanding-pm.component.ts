import { Component, OnInit } from "@angular/core";
import { Title } from "@angular/platform-browser";
import { Store } from "@ngrx/store";
import { Observable, Subscription } from "rxjs";
import { Column } from "../../root-store/shared-store/material-grid-store/state";
import {
  OutstandingPmReportStoreActions,
  OutstandingPmReportStoreSelectors,
  OutstandingPmReportStoreState,
} from "../../root-store/outstanding-pm-report-store";
import { ExportService } from "../../shared/export/export.service";
import { OutstandingPmDataModel } from "./outstanding-pm-report.model";
import { CommonModule } from "@angular/common";
import { AgencyStoreSelectors } from "../../root-store";
import { GridComponent } from "../../shared/grid/grid.component";
import { GridColumnComponent } from "../../shared/grid/grid-column.component";
import { MatSidenavModule } from "@angular/material/sidenav";
import { MatIconModule } from "@angular/material/icon";
import { MatTooltipModule } from "@angular/material/tooltip";
import { BlockUIDirective } from "../../shared/block-ui/block-ui.directive";

@Component({
  selector: "app-outstanding-pm",
  templateUrl: "./outstanding-pm.component.html",
  styleUrls: ["./outstanding-pm.component.scss"],
  standalone: true,
  imports: [
    CommonModule,
    GridComponent,
    GridColumnComponent,
    MatSidenavModule,
    MatIconModule,
    MatTooltipModule,
    BlockUIDirective
  ]
})
export class OutstandingPmComponent implements OnInit {
  private subscriptions: Subscription[] = [];
  reportData$: Observable<OutstandingPmDataModel[]> = this.store$.select(
    OutstandingPmReportStoreSelectors.selectGridItems
  );
  isLoading$: Observable<boolean> = this.store$.select(
    OutstandingPmReportStoreSelectors.selectIsLoading
  ) as Observable<boolean>;
  showPmDetails: boolean = false;

  columns: Column[] = [
    { name: "name", hidden: false, displayText: "Name" },
    { name: "tasksCount", hidden: false, displayText: "Tasks Count" },
  ];
  key = "name";

  gridStoreSelector: any =
    OutstandingPmReportStoreSelectors.selectOutstandingPmGrid;
  id = OutstandingPmReportStoreState.OUTSTANDING_PM_REPORT_GRID_ID;

  reportData: OutstandingPmDataModel[] = [];
  constructor(
    private store$: Store<OutstandingPmReportStoreState.State>,
    private exportService: ExportService,
    title: Title,
  ) {
    title.setTitle("Outstanding PM Report");
  }

  ngOnInit() {
    this.store$.dispatch(
      new OutstandingPmReportStoreActions.SearchRequestAction()
    );

    const reportDataSub = this.reportData$.subscribe((data) => {
      this.reportData = data;
    });
    this.subscriptions.push(reportDataSub);

    const defaultAgencySub = this.store$
      .select(AgencyStoreSelectors.selectDefaultAgency)
      .subscribe((defaultAgency) => {
        if (defaultAgency) {
          this.store$.dispatch(
            new OutstandingPmReportStoreActions.SearchRequestAction()
          );
          const reportDataSubInner = this.reportData$.subscribe((data) => {
            this.reportData = data;
          });
          this.subscriptions.push(reportDataSubInner);
        }
      });
    this.subscriptions.push(defaultAgencySub);
  }

  ngOnDestroy() {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  initPrintPdf() {
    let jsonData = this.formatReportDataForExport();
    let pdfHeader = "Reports";
    let currentTime = new Date();
    this.exportService.initPrintPdf(
      jsonData,
      pdfHeader,
      "Outstanding PM Report",
      "Created at " + currentTime.toDateString()
    );
  }

  initExport() {
    let jsonData = this.formatReportDataForExport();
    let pdfHeader = "Reports";
    let currentTime = new Date();
    let fileName = "OutstandingPMReport_" + currentTime.toDateString();
    this.exportService.initExportToPdf(
      jsonData,
      fileName,
      pdfHeader,
      "Outstanding PM Report",
      "Created at " + currentTime.toDateString()
    );
  }

  formatReportDataForExport() {
    let jsonReportData: any[] = [];

    this.reportData.forEach((x) => {
      jsonReportData.push({
        Name: x.name,
        "Tasks Count": x.tasksCount ? x.tasksCount : "0",
      });
    });

    return jsonReportData;
  }
}
