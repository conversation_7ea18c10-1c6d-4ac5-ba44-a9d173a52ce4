import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { map } from 'rxjs/operators';
import { JurisdictionConfiguration } from './models/jurisdiction-configuration.model';
import { Contract } from './models/contract.model';
import { ContractFilter } from './models/contract-filter.model';
import { PagedResult } from './models/paged-result.model';
import { JurisdictionConfFilter } from './models/jurisdiction-conf-filter.model';
import { JurisdictionConfCopyRequest } from './models/copy-jurisdiction-configuration.model';

@Injectable({
  providedIn: 'root'
})
export class ContractService {

  constructor(private httpClient: HttpClient) {
  }

  getContracts(filter: ContractFilter, nextPageToken: string): Observable<PagedResult<Contract>> {
    const paramsWithoutUndefined: { [key: string]: any } = {};
    if (filter) {
      if (nextPageToken) {
        filter.nextPageToken = encodeURIComponent(nextPageToken);
      }

      Object.entries(filter || {}).forEach(([key, value]: [string, any]) => {
        if (value !== undefined && value !== null) {
          paramsWithoutUndefined[key] = value;
        }
      });
    }

    return this.httpClient.get<Array<Contract>>('payrollandbilling/v1/contract/getByAgency', { params: paramsWithoutUndefined as unknown as HttpParams })
      .pipe(map((result: any) => { return result }));
  }

  createContract(contract: Contract): Observable<Contract | null> {
    return this.httpClient.post<Contract>('payrollandbilling/v1/contract/', contract, { observe: 'response' })
      .pipe(map((response: HttpResponse<Contract>) => {
        return response.body;
      }));
  }

  updateContract(contract: Contract): Observable<Contract> {
    return this.httpClient.put<Contract>('payrollandbilling/v1/contract/', contract).pipe(map((response: Contract) => {
      return response;
    }));
  }

  deleteContract(id: string): Observable<boolean> {
    return this.httpClient.delete(`payrollandbilling/v1/contract/${id}`)
      .pipe(map(() => {
        return true;
      }));
  }

  canContractBeDeleted(id: string): Observable<boolean> {
    return this.httpClient.get<boolean>(`aggregator/v1/contract/associated/${id}`)
      .pipe(map((response: boolean) => {
        return response;
      }))
  }

  getJurisdictionConfigurations(filter: JurisdictionConfFilter): Observable<Array<JurisdictionConfiguration>> {
    let params = new HttpParams();
    if (filter && filter.agencyId) {
      params = params.append('agencyId', filter.agencyId);
    }
    if (filter && filter.contractId) {
      params = params.append('contractId', filter.contractId);
    }
    return this.httpClient.get<Array<JurisdictionConfiguration>>(`payrollandbilling/v1/jurisdictionConfiguration`, { params }).pipe(map(
      (data: any) => {
        return data
      })
    )
  }

  createJurisdictionConfiguration(jurisdictionConfiguration: JurisdictionConfiguration): Observable<JurisdictionConfiguration | null> {
    return this.httpClient.post('payrollandbilling/v1/jurisdictionConfiguration/', jurisdictionConfiguration, { observe: 'response' })
      .pipe(map((response: HttpResponse<JurisdictionConfiguration>) => {
        return response.body;
      }));
  }

  updateJurisdictionConfiguration(jurisdictionConfiguration: JurisdictionConfiguration): Observable<JurisdictionConfiguration> {
    return this.httpClient.put('payrollandbilling/v1/jurisdictionConfiguration/', jurisdictionConfiguration).pipe(map((response: JurisdictionConfiguration) => {
      return response;
    }));
  }

  deleteJurisdictionConfiguration(id: string): Observable<boolean> {
    return this.httpClient.delete(`payrollandbilling/v1/jurisdictionConfiguration/${id}`)
      .pipe(map(() => {
        return true;
      }));
  }

  copyJurisdictionConfigurationFromContract(requestModel: JurisdictionConfCopyRequest): Observable<JurisdictionConfiguration[] | null> {
    return this.httpClient.post<JurisdictionConfiguration[]>(`aggregator/v1/contract/copy-jurisdictions-from-contract`, requestModel, { observe: 'response' })
    .pipe(map((response: HttpResponse<Array<JurisdictionConfiguration>>) => {
      return response.body;
    }))
  }

  getContractById(id: string): Observable<Contract> {
    return this.httpClient.get<Contract>('payrollandbilling/v1/contract/' + id)
      .pipe(map((result: any) => { return result }));
  }

  getJurisdictionConfigurationById(id: string): Observable<JurisdictionConfiguration> {
    return this.httpClient.get<JurisdictionConfiguration>('payrollandbilling/v1/jurisdictionConfiguration/' + id)
      .pipe(map((result: any) => { return result }));
  }

}
