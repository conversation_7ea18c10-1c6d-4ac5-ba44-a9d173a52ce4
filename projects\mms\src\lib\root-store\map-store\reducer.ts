import { Actions, ActionTypes } from './actions';
import { initialState, State } from './state';
import { MapConfiguration } from '../../mms-map/models/map.models';

export function mainReducer(state = initialState, action: Actions): State {

    switch (action.type) {
        case ActionTypes.TOGGLE_LAYER_VISIBILITY:
        {
            const index = state.mapConfig.layerConfigs.findIndex(l => l.id === action.payload.layerId);
            state.mapConfig.layerConfigs[index].options = {
                ...state.mapConfig.layerConfigs[index].options,
                visiblity: action.payload.visible
            }

            return { ...state };
        }
        case ActionTypes.LOAD_PMS:
        case ActionTypes.LOAD_TICKETS:
        case ActionTypes.LOAD_LOCATIONS:
        case ActionTypes.LOAD_LOCATION_TYPES:
        case ActionTypes.ADD_MAP_CONFIG:
        case ActionTypes.EDIT_MAP_CONFIG:
        case ActionTypes.DELETE_MAP_CONFIG:
        case ActionTypes.ADD_MAP_LAYER_CONFIG:
        case ActionTypes.EDIT_MAP_LAYER_CONFIG:
        case ActionTypes.DELETE_MAP_LAYER_CONFIG:
        case ActionTypes.LOAD_MAP_CONFIG_REQUEST: {
            return {
                ...state,
                isLoading: true,
                error: null,
            };
        }

        case ActionTypes.SET_LOCATION_FILTER: {
            state.filters.location = action.payload;
            return state;
        }

        case ActionTypes.CLEAR_TICKETS: {
            state.mapData.pmMapInstances = []
            state.mapData.ticketMapInstances = []
            return state
        }

        case ActionTypes.LOAD_PMS_SUCCESS: {
            state.mapData.pmMapInstances = action.payload.pms
            state.mapData.locationMapInstances.map(l => {
                l.properties.pms = action.payload.pms.filter(t => l.properties.id === t.properties.locationId);
                l.properties.allPms = action.payload.allPms.filter(t => l.properties.id === t.properties.locationId);
                return l;
            });            
            state.mapData.locationMapInstances = [...state.mapData.locationMapInstances];            
            return state
        }

        case ActionTypes.LOAD_TICKETS_SUCCESS: {
            state.mapData.ticketMapInstances = action.payload
            state.mapData.locationMapInstances.map(l => {
                l.properties.workOrderTickets = action.payload.filter(t => l.properties.id === t.properties.locationId);
                return l;
            });
            state.mapData.locationMapInstances = [...state.mapData.locationMapInstances];
            return state
        }

        case ActionTypes.LOAD_CALL_TICKETS_SUCCESS: {
            state.mapData.callMapInstances = action.payload
            state.mapData.locationMapInstances.map(l => {
                l.properties.callTickets = action.payload.filter(t => l.properties.id === t.properties.locationId);
                return l;
            });

            state.mapData.locationMapInstances = [...state.mapData.locationMapInstances];
            return state
        }

        case ActionTypes.LOAD_LOCATIONS_SUCCESS: {
            state.mapData.locationMapInstances = action.payload
            return state
        }

        case ActionTypes.LOAD_LOCATION_TYPES_SUCCESS: {
            state.mapData = {
                ...state.mapData,
                locationTypes: action.payload
            }

            return state
        }

        case ActionTypes.LOAD_MAP_CONFIG_SUCCESS: {
            state.mapConfig = {
                ...state.mapConfig,
                mapConfigs: action.payload
            }

            return { ...state }
        }

        case ActionTypes.LOAD_MAP_LAYER_CONFIG_SUCCESS: {
            state.mapConfig = {
                ...state.mapConfig,
                layerConfigs: action.payload
            }

            return { ...state }
        }

        case ActionTypes.ADD_MAP_CONFIG_SUCCESS: {
            const mapConfigs = new Array<MapConfiguration>();
            state.mapConfig.mapConfigs.forEach(m => mapConfigs.push({...m}));
            mapConfigs.push(action.payload);
            state.mapConfig = {
                ...state.mapConfig,
                mapConfigs: mapConfigs
            }

            return { ...state }
        }

        case ActionTypes.EDIT_MAP_CONFIG_SUCCESS: {
            const mapConfigs = new Array<MapConfiguration>();
            state.mapConfig.mapConfigs.forEach(m => mapConfigs.push({...m}));
            const index = mapConfigs.findIndex(m => m.id === action.payload.id);
            if (index !== -1) {
                mapConfigs[index] = action.payload;
            }

            state.mapConfig = {
                ...state.mapConfig,
                mapConfigs: mapConfigs
            }

            return { ...state }
        }

        case ActionTypes.DELETE_MAP_CONFIG_SUCCESS: {
            const mapConfigs = new Array<MapConfiguration>();
            state.mapConfig.mapConfigs.forEach(m => mapConfigs.push({...m}));
            const index = mapConfigs.findIndex(m => m.id === action.payload);

            if (index > -1) {
                mapConfigs.splice(index, 1);
            }

            state.mapConfig = {
                ...state.mapConfig,
                mapConfigs: mapConfigs
            }

            return { ...state }
        }

        case ActionTypes.ADD_MAP_CONFIG_FAILURE:
        case ActionTypes.EDIT_MAP_CONFIG_FAILURE:
        case ActionTypes.DELETE_MAP_CONFIG_FAILURE:
        case ActionTypes.ADD_MAP_LAYER_CONFIG_FAILURE:
        case ActionTypes.EDIT_MAP_LAYER_CONFIG_FAILURE:
        case ActionTypes.DELETE_MAP_LAYER_CONFIG_FAILURE:
        case ActionTypes.LOAD_MAP_CONFIG_FAILURE: {
            return {
                ...state,
                isLoading: false,
                error: action.payload.error
            };
        }
        case ActionTypes.SET_SELECTED_LOCATION: {
            return {
                ...state,
               selectedLocationId: action.payload.locationId
            };
        }
        case ActionTypes.SET_SELECTED_LOCATION_DATA: {
            return {
                ...state,
                selectedLocationForTicket: action.payload.location
            };
        }
        case ActionTypes.SET_SELECTED_LOCATION_ON_MAP_SUCCESS: {
            return {
                ...state,
                selectedLocationOnMap: action.payload.location
            };
        }
        case ActionTypes.LOAD_ALL_MAP_KEY_SUCCESS:
            return {
              ...state,
              isLoading: false,
              error: '',
              mapKey: action.payload === null ? "" : action.payload.subscriptionKey!,
              googleMapsApiKey: action.payload === null ? "" : action.payload.googleMapsApiKey!
            };
        case ActionTypes.LOAD_MAP_KEY_FAILURE:
        return {
            ...state,
            isLoading: false,
            error: action.payload
        };        
        default: {
            return state;
        }
    }
}

export const reducer = mainReducer;
