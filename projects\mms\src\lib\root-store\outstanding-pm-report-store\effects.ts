import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import {
  withLatestFrom,
  map,
  switchMap,
  catchError,
  concatMap
} from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state';

import { ReportsService } from '../../reports/reports.service';
import { Router } from '@angular/router';
import { GridStoreActions } from '../shared-store/material-grid-store';
import { AgencyStoreSelectors } from '../agency-store';


@Injectable()
export class OutstandingPmReportStoreEffects {
  constructor(
    private actions$: Actions,
    private reportsService: ReportsService,
    private store$: Store<featureState.State>,
    private router: Router
  ) { }

  searchReportRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SearchRequestAction>(
      featureActions.ActionTypes.SEARCH_REQUEST
    ),
    withLatestFrom(
      this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
    ),
    switchMap(([_, agency]) => {
      if (!agency) {
        return of(new GridStoreActions.GridNoAction());
      }
      return this.reportsService.getOutstandingPmReport(agency)
        .pipe(
          map(
            reportData => {
              return new featureActions.SearchSuccessAction({ reportData });
            }
          ),
          catchError(error =>
            of(new featureActions.SearchFailureAction({ error }))
          )
        )
    })
  ));

  searchSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SearchSuccessAction>(
      featureActions.ActionTypes.SEARCH_SUCCESS
    ),
    withLatestFrom(
      this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
    ),
    concatMap(([action, _]) => {
      this.router.navigate(['/reports/outstanding-pm-report']);
      return [
        new GridStoreActions.GridLoadItemsSuccessAction(featureState.OUTSTANDING_PM_REPORT_GRID_ID, { items: action.payload.reportData }),
      ]
    })
  ));
}
