﻿import { Validator, AbstractControl, ValidationErrors, FormControl, FormArray  } from '@angular/forms'

export class MinLengthValidator implements Validator {

    constructor(private minLength: number) {
    }

    validate(control: AbstractControl): ValidationErrors | null {
        if (control instanceof FormControl || control instanceof FormArray) {
            if (control.value == null ||
                ((typeof control.value === 'string') && (control.value.trim() === ''
                || control.value.trim().length >= this.minLength))
                || ((control.value instanceof Array)
                && (<Array<any>>control.value).length >= this.minLength)) {
                return null;
            }

            return {
                minlength: {
                    current: control.value,
                    requiredLength: this.minLength
                }
            }
        }
        return null;
    }
}
