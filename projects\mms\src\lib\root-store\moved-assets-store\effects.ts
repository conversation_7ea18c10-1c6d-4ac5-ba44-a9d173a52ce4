import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, Action, select } from '@ngrx/store';
import { Observable, of } from 'rxjs';
import {
	withLatestFrom,
	map,
	catchError,
	exhaustMap,
	tap
} from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state';
import * as featureSelectors from './selectors';

import { ReportsService } from '../../reports/reports.service';
import { Router } from '@angular/router';
import { IGridStoreEffects } from '../shared-store/material-grid-store/effects';
import { GridStoreActions } from '../shared-store/material-grid-store';
import { MovedAssetsReportModel } from '../../reports/moved-assets/moved-assets-report.model';

@Injectable()
export class MovedAssetsReportStoreEffects implements IGridStoreEffects {
	constructor(
		private actions$: Actions,
		private reportsService: ReportsService,
		private store$: Store<featureState.State>,
		private router: Router
	) {}

	gridLoadItems(actionType: string): Observable<Action> {
		return this.actions$.pipe(
			ofType<GridStoreActions.GridLoadItemsAction<MovedAssetsReportModel>>(actionType),
		   withLatestFrom(this.store$.select(featureSelectors.selectGridItems)),
		   map(([action, items]) => {
			   return new GridStoreActions.GridLoadItemsSuccessAction(featureState.MOVED_ASSETS_REPORT_GRID_ID, { items: items });
		   })
		);
	}

	gridLoadItemsSuccess(actionType: string): Observable<Action> {
		return this.actions$.pipe(
			ofType(actionType),
			map(action => new GridStoreActions.GridNoAction())
		);
	}

	gridLoadItemsFailed(actionType: string): Observable<Action> {
		return this.actions$.pipe(
			ofType(actionType),
			map(action => new GridStoreActions.GridNoAction())
		);
	}

	gridLoadItems$ = createEffect(() => this.gridLoadItems(
		GridStoreActions.ActionTypes.GRID_LOAD_ITEMS(
			featureState.MOVED_ASSETS_REPORT_GRID_ID
		)
	));

	gridLoadItemsSuccess$ = createEffect(() => this.gridLoadItemsSuccess(
		GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_SUCCESS(
			featureState.MOVED_ASSETS_REPORT_GRID_ID
		)
	));

    searchRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SearchRequestAction>(
            featureActions.ActionTypes.SEARCH_REQUEST
        ),
		exhaustMap((action) =>
			this.reportsService.searchMovedAssetsReport(action.payload)
				.pipe(
					map(
						(reportData: MovedAssetsReportModel[]) => {
							return new featureActions.SearchSuccessAction(reportData);
						}
					),
					catchError(error =>
						of(new featureActions.SearchFailureAction({ error }))
					)
				)
		)
	));
		
	searchSuccessEffect$ = createEffect(() => this.actions$.pipe(
		ofType<featureActions.SearchSuccessAction>(
			featureActions.ActionTypes.SEARCH_SUCCESS
		),
		map((action) => {
			this.router.navigate(['/reports/moved-assets']);
			return new GridStoreActions.GridLoadItemsSuccessAction(featureState.MOVED_ASSETS_REPORT_GRID_ID, { items: action.payload });
		})
	));

	showFilterEffect$ = createEffect(() => this.actions$.pipe(
		ofType<featureActions.ShowFilterAction>(
			featureActions.ActionTypes.SHOW_FILTER
		),
		map((action) => {
			this.router.navigate(['/reports/moved-assets-filter']);
			return new featureActions.NoAction();
		})
	));

	getIsLoading$ = createEffect(() => this.actions$.pipe(
		ofType<featureActions.CheckIfIsLoading>(
			featureActions.ActionTypes.IS_LOADING
		),
		withLatestFrom(
			this.store$.pipe(select(featureSelectors.selectMovedAssetsIsLoading)),
		),
		tap(([action, isLoading]: [featureActions.CheckIfIsLoading, boolean]) => {
			if (isLoading === null) {
				this.router.navigate(['/reports/moved-assets-filter']);
			}
		})
	), { dispatch: false });
}
