import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, select } from '@ngrx/store';
import { of as observableOf, pipe } from 'rxjs';
import { catchError, map, switchMap, tap, withLatestFrom, exhaustMap } from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state'
import * as featureSelectors from './selectors'

import { Router } from '@angular/router';
import { Page } from '../../core/page.service';
import { FormResetAction } from '../../shared/forms/connect-form-ngrx';
import { AlertsService } from '../../configuration/alerts/alerts.service';
import { Alert } from '../../configuration/alerts/models/alert.model';


@Injectable()
export class AlertStoreEffects {
  constructor(
    private router: Router,
    private alertService: AlertsService,
    private actions$: Actions,
    private store$: Store<featureState.State>,
    private page: Page) { }

  loadRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(
      featureActions.ActionTypes.LOAD_REQUEST
    ),
    switchMap(action =>
      this.alertService
        .getAlerts()
        .pipe(
          map(
            items =>
              new featureActions.LoadSuccessAction({
                items
              })
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  saveRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveRequestAction>(
      featureActions.ActionTypes.SAVE_REQUEST
    ),
    exhaustMap((action) =>
      this.alertService
        .addAlert(action.payload.item)
        .pipe(
          switchMap((alert) => [
            new featureActions.SaveSuccessAction({
              item: alert!
            }),
            new featureActions.LoadRequestAction()
          ]),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  updateRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateRequestAction>(
      featureActions.ActionTypes.UPDATE_REQUEST
    ),
    exhaustMap((action) =>
      this.alertService
        .updateAlert(action.payload.item)
        .pipe(
          switchMap(() => [
            new featureActions.UpdateSuccessAction({ item: { id: action.payload.item.id!, changes: action.payload.item } }), 
            new featureActions.LoadRequestAction()
          ]),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  updateRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateSuccessAction>(
      featureActions.ActionTypes.UPDATE_SUCCESS
    ),
    tap(() => {
      this.page.notification.show('Alert updated');
    })
  ), { dispatch: false });

  saveRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveSuccessAction>(
      featureActions.ActionTypes.SAVE_SUCCESS
    ),
    map(() => {
      this.page.notification.show('Alert added');
      return new FormResetAction();
    })
  ));

  confirmDeleteAlertEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmDeleteAlertAction>(
      featureActions.ActionTypes.CONFIRM_DELETE_ALERT
    ),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectedAlert))),
    exhaustMap(([action, selectedAlert]: [featureActions.ConfirmDeleteAlertAction, Alert]) =>
      this.page
        .confirm.show('Are you sure you want to delete this alert?', 'Are you sure?')
        .pipe(
          map(
            (result) => {
              if (result) {
                return new featureActions.DeleteRequestAction();
              }
              return new featureActions.NoAction();
            }
          )
        )
    )
  ));

  deleteRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteRequestAction>(
      featureActions.ActionTypes.DELETE_REQUEST
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedAlert)),
    ),
    switchMap(([action, selectedAlert]: [featureActions.DeleteRequestAction, Alert]) =>
      this.alertService
        .deleteAlert(selectedAlert.id!)
        .pipe(
          map(
            () =>
              new featureActions.DeleteSuccessAction({
                itemId: selectedAlert.id!
              })
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  deleteSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteSuccessAction>(
      featureActions.ActionTypes.DELETE_SUCCESS
    ),
    pipe(
      map(() => {
        this.page.notification.show('Alert deleted');
      }),
      switchMap(res => [
        new FormResetAction(),
        new featureActions.AddNewAction()
      ])
    )
  ));

  addNewEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AddNewAction>(
      featureActions.ActionTypes.ADD_NEW
    ),
    tap(() => {
      this.router.navigate(['/configuration/alerts']);
    })
  ), { dispatch: false });

  saveEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveAction>(
      featureActions.ActionTypes.SAVE
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedAlert)),
    ),
    map(([action, selectedAlert]: [featureActions.SaveAction, Alert]) => {
      if (selectedAlert == null) {
        const item = { ...action.payload.item, isEditable: true, isPredefined: false };
        return new featureActions.SaveRequestAction({ item })
      } else {
        const alert = action.payload.item;
        alert.id = selectedAlert.id;
        alert.tenantId = selectedAlert.tenantId;
        return new featureActions.UpdateRequestAction({ item: alert })
      }
    })
  ));
}
