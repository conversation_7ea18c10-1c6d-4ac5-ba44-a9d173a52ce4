import { LayerConfig, PopupTriggerType, SymbolLayerConfig, TileLayerConfig } from '@econolite/limestone';
import { createFeatureSelector, createSelector, MemoizedSelector } from '@ngrx/store';
import { SymbolLayerOptions, TileLayerOptions } from 'azure-maps-control';
import { of } from 'rxjs';
import { MapLayer, OpenStreetMapLayerOptions } from '../../settings/map-layers/models/map-layer.model';
import { LocationInstanceFeatures, MapConfiguration, TicketFeatures } from '../../mms-map/models/map.models';
import { MapConfigState, State } from './state';
import { PopupTemplates } from '../../mms-map/models/popup-templates.model';
import { LocationType } from '../../configuration/type-and-field-definitions/locations/locations-manage/location-type.model';

export const getError = (state: State): any => state.error;
export const getIsLoading = (state: State): boolean => !!state.isLoading;
export const getInitialLoad = (state: State): boolean => !!state.initialLoad;

export const selectMapState: MemoizedSelector<object, State> = createFeatureSelector<State>('map');

export const selectMapIsLoading = createSelector(
    selectMapState,
    getIsLoading
);

export const selectMapError = createSelector(
    selectMapState,
    getError
);

export const selectMapConfigState = createSelector(
    selectMapState,
    (state) => state.mapConfig
);

export const selectMapDataState = createSelector(
    selectMapState,
    (state) => state.mapData
);

export const selectMapFilterState = createSelector(
    selectMapState,
    (state) => state.filters
);

export const selectMapFilterLocation = createSelector(
    selectMapFilterState,
    (state) => state.location
);

export const selectMapIcons = createSelector(
    selectMapConfigState,
    (state) => state.icons
);

export const selectMapConfigs = createSelector(
    selectMapConfigState,
    selectMapIcons,
    (state, icons) => state.mapConfigs.map(c => {
            return {
                ...c,
                icons: icons
            } as MapConfiguration
        })
);

export const selectMapConfig = createSelector(
    selectMapConfigs,
    (state) => state[0] ? state[0] : null
);

export const selectLayerConfigs = createSelector(
    selectMapConfigState,
    (state) => {
        const reverse = [...state.layerConfigs];
        return reverse.reverse();
    }
);

export const selectMapLayerConfigs = createSelector<object, MapConfigState, any>(
    selectMapConfigState,
    (state) => ToLayerConfigs(state.layerConfigs)
);

export const selectLocationTypes = createSelector(
    selectMapDataState,
    (state): LocationType[] => state.locationTypes
);

export const selectLocations = createSelector(
    selectMapDataState,
    (state): LocationInstanceFeatures[] => state.locationMapInstances
);

export const selectTickets = createSelector(
    selectMapDataState,
    (state): TicketFeatures[] => state.ticketMapInstances
);

export const selectCallTickets = createSelector(
    selectMapDataState,
    (state): TicketFeatures[] => state.callMapInstances
);

export const selectMapFilterLocationState = createSelector(
    selectMapFilterState,
    (state) => state.location
);

export function ToLayerTypes(configs: MapLayer[]) {
    const mapLayers: MapLayer[] = [];
    configs.forEach(layer => {
        const config: MapLayer = ToLayerType(layer);
        mapLayers.push(config)
    });
    return mapLayers.length === 0 ? null : mapLayers;
}

export function ToLayerType(layer: MapLayer) {
    const config: MapLayer = layer;
        switch (layer.layerType) {
            case 'OpenStreetMap.org':
                config.options = new OpenStreetMapLayerOptions();
                break;
            case 'Tile':
                config.options = config.options as TileLayerOptions;
                break;
            case 'Web Map Service (WMS)':
                config.options = config.options as TileLayerOptions;
                break;
            case 'Symbol':
                config.options = config.options as SymbolLayerOptions;
                break;
        }
    return config;
}

export function ToLayerConfigs(configs: MapLayer[]) {
    const mapLayers: LayerConfig[] = [];
    const popupTemplate = new PopupTemplates();
    configs.forEach(layer => {
        let config: LayerConfig | undefined;
        switch (layer.layerType) {
            case 'OpenStreetMap.org':
            case 'Tile':
                config = new TileLayerConfig({
                    id: layer.id,
                    name: layer.name,
                    enabled: layer.enabled,
                    matIconName: layer.icon,
                    options: of({...layer.options, opacity: layer.opacity, maxZoom: layer.maxZoom, minZoom: layer.minZoom} as TileLayerOptions)
                })
            break;
            case 'Web Map Service (WMS)':
                config = new TileLayerConfig({
                    id: layer.id,
                    name: layer.name,
                    enabled: layer.enabled,
                    matIconName: layer.icon,
                    options: of({
                        ...layer.options,
                    } as TileLayerOptions)
                })
            break;
            case 'Symbol':
                config = new SymbolLayerConfig({
                    id: layer.id,
                    name: layer.name,
                    enabled: layer.enabled,
                    matIconName: layer.icon,
                    selector: layer.mapSelector,
                    onClick: layer.onClick,
                    options: of({
                        ...layer.options,
                    } as SymbolLayerOptions)
                }) as SymbolLayerConfig

                if (layer.popupTemplate && layer.mapSelector) {
                    (config as any)['popupTriggerType'] = PopupTriggerType.OnMouseOver;
                    // tslint:disable-next-line: no-eval
                    (config as any)['popupTemplate'] = popupTemplate.getTemplate(layer.mapSelector)
                } else {
                    (config as any)['popupTriggerType'] = PopupTriggerType.None;
                }
        }
        if (config) {
            mapLayers.push(config);
        }
    });
    return mapLayers;
}

export const selectedLocationId = createSelector(
    selectMapState,
    (state) => state.selectedLocationId
);

export const selectedLocationOnMap = createSelector(
    selectMapState,
    (state) => state.selectedLocationOnMap
);

export const selectedLocationForTicket = createSelector(
    selectMapState,
    (state) => state.selectedLocationForTicket
);

export const getMapKey = createSelector(
    selectMapState,
    state => state.mapKey
);

export const getGoogleMapsApiKey = createSelector(
    selectMapState,
    state => state.googleMapsApiKey
);