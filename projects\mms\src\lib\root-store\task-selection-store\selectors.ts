import { createFeatureSelector, createSelector } from '@ngrx/store';
import { MainState } from './state';

export const selectTaskState = createFeatureSelector<MainState>('taskSelectionData');

export const selectTaskNumber = createSelector(
  selectTaskState,
  (state: MainState) => state.taskSelectionData.currentTaskNumber
);

export const selectTaskLoading = createSelector(
  selectTaskState,
  (state: MainState) => state.taskSelectionData.isLoading
);

export const selectedTask = createSelector(
  selectTaskState,
  (state: MainState) => state.ticket
);
