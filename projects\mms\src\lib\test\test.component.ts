import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>erContent } from '@angular/material/sidenav';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MasterInventoryStoreState } from '../root-store';
import { MasterInventoryStoreSelectors } from '../root-store';
import { Column } from '../root-store/shared-store/material-grid-store/state';
import { GridComponent } from '../shared/grid/grid.component';
import { GridColumnComponent } from '../shared/grid/grid-column.component';

@Component({
  templateUrl: './test.component.html',
  standalone: true,
  imports: [
    CommonModule,
    Mat<PERSON><PERSON>er<PERSON>ontainer,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>ontent,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatButtonModule,
    GridComponent,
    GridColumnComponent,
  ]
})
export class TestComponent {

  columns: Column[] = [
    { name: 'select' },
    { name: 'position', hidden: true },
    { name: 'name', hidden: false },
    { name: 'weight', hidden: true },
    { name: 'symbol', hidden: false }
  ];

  key = 'name';
  gridStoreSelector = MasterInventoryStoreSelectors.selectGrid;
  id = MasterInventoryStoreState.MASTER_INVENTORY_GRID_ID;

}
