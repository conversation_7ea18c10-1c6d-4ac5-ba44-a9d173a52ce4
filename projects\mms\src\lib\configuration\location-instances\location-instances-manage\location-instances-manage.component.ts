import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup, FormBuilder } from '@angular/forms';
import { <PERSON><PERSON><PERSON><PERSON>, MatSidenavModule } from '@angular/material/sidenav';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { Store } from '@ngrx/store';
import { Observable, Subscription } from 'rxjs';

import { LocationInstanceStoreState, LocationInstanceStoreSelectors, LocationInstanceStoreActions } from '../../../root-store';
import { LocationInstance, LocationRegionInstance } from '../shared/location-instance.model';
import { Field } from '../../shared/asset-field/field.model';
import { FieldValue } from '../../shared/asset-field/field-value.model';
import { LocationTypeEntity } from '../shared/location-type-entity';
import { Validators } from '../../../shared/forms/validators/validators';
import { CanDeactivate } from '../../../core/can-deactivate.model';
import { map, take } from 'rxjs/operators';
import { Router } from '@angular/router';
import { FieldTypeEnum } from '../../shared/asset-field/field-type.enum';
import { FormRevertChangesAction } from '../../../shared/forms/connect-form-ngrx';
import { AreaTypeEnum } from '../shared/area-type.enum';
import * as uuid from 'uuid';
import { ValidationMessageComponent } from '../../../shared/forms/validation-message/validation-message.component';
import { BlockUIDirective } from '../../../shared/block-ui/block-ui.directive';
import { AssetFieldsComponent } from '../../shared/asset-fields/asset-fields.component';
import { MMSSelectMenuComponent } from '../../../shared/forms/controls/mms-select-menu/mms-select-menu.component';
import { VarDirective } from '../../../shared/forms/variable.directive';

@Component({
    selector: 'mms-location-instance-manage',
    templateUrl: './location-instances-manage.component.html',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatSidenavModule,
        MatButtonModule,
        MatIconModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatCheckboxModule,
        ValidationMessageComponent,
        BlockUIDirective,
        AssetFieldsComponent,
        MMSSelectMenuComponent,
        VarDirective,
    ]
})
export class LocationInstancesManageComponent implements OnInit, OnDestroy, CanDeactivate {
    isLoading$!: Observable<boolean>;
    formGroup!: FormGroup;
    locationTypes$!: Observable<LocationTypeEntity[]>;
    locationInstance$!: Observable<LocationInstance>;
    fields!: Field[];
    oldFields!: Field[];
    location!: LocationInstance;
    regions$ = this.store$.select(LocationInstanceStoreSelectors.selectRegions);
    selectedLocationType$!: Observable<LocationTypeEntity>;
    subscriptions: Array<Subscription> = new Array<Subscription>();
    instanceId!: string;
    selectedLocation!: LocationInstance;

    constructor(
        private formBuilder: FormBuilder,
        private store$: Store<LocationInstanceStoreState.State>,
        private matDrawer: MatDrawer,
        private router: Router
    ) { }

    ngOnInit() {
        this.store$.dispatch(new LocationInstanceStoreActions.LoadLocationDataRequestAction());

        this.formGroup = this.formBuilder.group({
            id: [null],
            tenantId: [null],
            imageURL: [null],
            locationType: [null, [Validators.required()]],
            isScrap: [null],
            fieldValues: this.formBuilder.array([]),
            alternateId: [null]
        });

        this.isLoading$ = this.store$.select(
            LocationInstanceStoreSelectors.selectIsLocationInstanceLoading
        );

        this.locationTypes$ = this.store$.select(
            LocationInstanceStoreSelectors.selectLocationTypes
        );

        this.locationInstance$ = this.store$.select(
            LocationInstanceStoreSelectors.selectedLocationInstance
        );

        this.selectedLocationType$ = this.store$.select(
            LocationInstanceStoreSelectors.selectedLocationType
        );

        this.subscriptions.push(this.formGroup.get('locationType')!.valueChanges.subscribe((locationType: LocationTypeEntity) => {
            if (locationType == null) {
                this.store$.dispatch(new LocationInstanceStoreActions.ClearSelectedDataAction());
            } else {
                this.store$.dispatch(new LocationInstanceStoreActions.LocationTypeChangedAction({ locationType: locationType }));
            }
        })
        );

        this.subscriptions.push(this.selectedLocationType$.subscribe((locationType: LocationTypeEntity) => {
            if (locationType) {
                const fields: Field[] = JSON.parse(JSON.stringify(locationType.fields || []));

                if (this.location) {
                    this.location.fieldValues.forEach((fieldValue: FieldValue) => {
                        const field = fields.find(x => x.name?.toLocaleLowerCase() === fieldValue.name?.toLocaleLowerCase());
                        if (field) {
                            field.value = fieldValue.value;
                        }
                    });

                    this.location = null!;
                } else
                    this.instanceId = uuid.v4();

                this.fields = fields;
                this.oldFields = fields;
            } else {
                this.fields = [];
            }
        }));

        this.subscriptions.push(this.locationInstance$.subscribe((instance) => {
            if (instance && instance.locationType) {
                this.instanceId = instance.id;
                if(instance.fieldValues){
                  instance.fieldValues.map((x) => {
                    if (x.name == "Regions" && x.value && x.multipleValues) {
                      x.value.map((y: any) => {
                        if (!y.areaType) {
                          y.areaType = AreaTypeEnum.Region;
                        }
                      });
                      x.multipleValues.map((z) => {
                        if (!z.areaType) {
                          z.areaType = AreaTypeEnum.Region;
                        }
                      });
                    }
                  });
                }
                this.location = instance;
                this.selectedLocation = instance;
                this.formGroup.patchValue({
                    id: instance.id,
                    locationType: instance.locationType,
                    imageURL: instance.imageURL,
                    isScrap: instance.isScrap,
                    tenantId: instance.tenantId,
                    alternateId: instance.alternateId
                });
            }
        }));
    }

    save() {
        let isValidCheckbox = false;
        let fieldValues = this.formGroup.get('fieldValues')!.value;
        if (this.fields && this.fields.length > 0) {
            for (let i = 0; i < this.fields.length; i++) {
                isValidCheckbox = false;
                if (fieldValues && fieldValues.length > 0 && (this.fields[i].name === "Checkbox field" || this.fields[i].type === FieldTypeEnum.CheckBox)) {
                    for (let j = 0; j < fieldValues.length; j++) {
                        if (this.fields[i].type === FieldTypeEnum.CheckBox) {
                            isValidCheckbox = true;
                        }
                        else {
                            isValidCheckbox = false;
                        }
                    }
                }
                else {
                    isValidCheckbox = true;
                }
            }
        } else {
            isValidCheckbox = true;
        }

        if (this.formGroup.valid && isValidCheckbox) {
            this.formGroup.value.fieldValues = this.formatFieldValues(this.formGroup.value.fieldValues);
            var instance = this.formGroup.value;
            instance.id = this.instanceId;
            instance.assignedAgencies = this.selectedLocation && this.selectedLocation.assignedAgencies ? this.selectedLocation.assignedAgencies : null;
            this.store$.dispatch(
                new LocationInstanceStoreActions.SaveAction({ item: instance })
            );
        } else if (this.formGroup.valid) {
            close();
        }
    }

    ngOnDestroy(): void {
        if (this.subscriptions.length > 0) {
            this.subscriptions.forEach(element => {
                element.unsubscribe();
            });
        }
    }
    clear() {
        this.formGroup.patchValue({
            fieldValues: this.oldFields
        });
        this.store$.dispatch(new FormRevertChangesAction());
        this.formGroup.markAsPristine();
        this.formGroup.markAsUntouched();
    }
    reset() {
        this.formGroup.reset();
    }
    close() {
        this.store$.dispatch(new LocationInstanceStoreActions.CloseSidebarAction());

    }
    closeSideNav() {
        this.store$.dispatch(new LocationInstanceStoreActions.CloseSidebarAction());
    }

    formatFieldValues(fields: Array<any>) {
        const fieldValues: FieldValue[] = Array<FieldValue>();
        fields.forEach(field => {
            fieldValues.push({ name: field.name, type: field.type, value: field.value, id: field.id });
        });
        this.formatTypeValue(fieldValues);
        this.formatRegionsValues(fieldValues);
        return fieldValues;
    }

    formatRegionsValues(fieldValues: Array<FieldValue>) {
        const regionField = fieldValues.find(field => field.type === FieldTypeEnum.Regions);
        if (regionField && regionField.value && regionField.value.length) {
            regionField.value = regionField.value.map((value: any) =>({id: value.id, name: value.name, areaType: value.areaType} as LocationRegionInstance));
            regionField.multipleValues = regionField.value;
        }
    }

    formatTypeValue(fieldValues: Array<FieldValue>) {
        const typeField = fieldValues.find(field => field.type === FieldTypeEnum.BillingCategory);
        if (typeField && typeField.value) {
            typeField.value = {id: typeField.value.id, name: typeField.value.name};
        }
    }

    locationItemValueMapper(option: any) {
        return option;
    }


    locationItemCompareFn(c1: any, c2: any): boolean {
        return c1 && c2 ? (c1.id === c2.id) : c1 === c2
    }

    canDeactivate() {
        const shouldDeactivate = this.router.getCurrentNavigation()?.extras?.state?.['shouldDeactive'];
        if (shouldDeactivate === false || (this.formGroup.pristine && this.formGroup.touched == false)) {
            return true;
        }

        return this.checkIsFormModified();
    }

    /**
     * Triggered on any unload browser event (refresh, back, exit, tenant change, sign out) if there are any unsaved changes on form
     */
    @HostListener('window:beforeunload')
    canUnloadPage(): boolean {
        var returnValue = false;
        this.checkIsFormModified().subscribe(x => returnValue = x);
        return returnValue;
    }

    /**
     * Checks if there was any changes on the form
     */
    checkIsFormModified() {
        return this.store$.select(LocationInstanceStoreSelectors.selectedLocationInstance).pipe(map((location) => {
            if (location != null) {
                var newFieldValueObject: any = {
                    id: null,
                    name: null,
                    type: null,
                    value: null,
                    isRequired: null,
                    availableValues: null
                }
                var newLocationObject: any = {
                    fieldValues: [],
                    id: location.id,
                    imageURL: location.imageURL,
                    isScrap: location.isScrap,
                    locationType: location.locationType,
                    tenantId: location.tenantId,
                    alternateId: location.alternateId
                }
                const locFieldValues = location.fieldValues;
                const formGroupFieldValues = this.formGroup.get('fieldValues')!.value;
                for (var i = 0; i < locFieldValues.length && i < formGroupFieldValues.length; i++) {
                    newFieldValueObject = {
                        id: locFieldValues[i].id || null,
                        name: locFieldValues[i].name || null,
                        type: locFieldValues[i].type || null,
                        value: locFieldValues[i].value,
                        isRequired: formGroupFieldValues[i].isRequired,
                        availableValues: formGroupFieldValues[i].availableValues
                    }
                    newLocationObject.fieldValues.push(newFieldValueObject);
                }
                return Object.equal(this.formGroup.value, newLocationObject, ['tenantId', 'lastLoggedUser', 'availableValues']);
            } else {
                const emptyLocationObject = {
                    fieldValues: [],
                    id: null,
                    imageURL: null,
                    isScrap: null,
                    locationType: null,
                    tenantId: null,
                    alternateId: null
                }
                return Object.equal(this.formGroup.value, emptyLocationObject, ['tenantId', 'lastLoggedUser', 'availableValues']);

            }
        }),
            take(1)
        );
    }

    regionCompareFn(c1: any, c2: Array<any>): boolean {
        return c1 && c2 ? c2.findIndex(x => x.id === c1.id) !== -1 : c1 === c2;
    }
}
