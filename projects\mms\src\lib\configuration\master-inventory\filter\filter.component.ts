import { Component, OnInit, ViewChild, AfterViewInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatSidenavModule } from '@angular/material/sidenav';

import { MatDrawer } from '@angular/material/sidenav';
import { ErrorStateMatcher } from '@angular/material/core';
import { Store } from '@ngrx/store';
import { Observable, Subscription } from 'rxjs';
import { map, take, filter } from 'rxjs/operators';
import { MasterInventoryStoreState, MasterInventoryStoreSelectors, MasterInventoryStoreActions, LocationInstanceStoreState, LocationInstanceStoreActions, GridStoreActions } from '../../../root-store';
import { Field } from '../../shared/asset-field/field.model';
import { FormArray, FormBuilder, FormGroup, FormGroupDirective, FormControl, NgForm } from '@angular/forms';
import { Validators } from '../../../shared/forms/validators/validators';
import { getFieldOperators, FieldOperatorEnum } from './field-operators';
import { InventoryLocation } from '../../master-inventory/inventory-location.model';
import { FilterModel } from './filter.model';
import { FieldTypeEnum } from '../../shared/asset-field/field-type.enum';
import { LocationTypeEntity } from '../../location-instances/shared/location-type-entity';
import { MMSSelectMenuComponent } from '../../../shared/forms/controls/mms-select-menu/mms-select-menu.component';
import { LocationType } from '../../type-and-field-definitions/locations/locations-manage/location-type.model';
import { ValidationMessageComponent } from '../../../shared/forms/validation-message/validation-message.component';
import { BlockUIDirective } from '../../../shared/block-ui/block-ui.directive';
import { MmsSelectLocationComponent } from '../../../shared/forms/controls/mms-select-location/mms-select-location.component';

export class AssetFieldsErrorStateMatcher implements ErrorStateMatcher {
  constructor(private parentFormGroupDirective: FormGroupDirective) {
  }
  isErrorState(control: FormControl | null, form: FormGroupDirective | NgForm | null): boolean {
    const isSubmitted = (form && form.submitted) || this.parentFormGroupDirective.submitted;
    return !!(control && control.invalid && (control.dirty || control.touched || isSubmitted));
  }
}

@Component({
  templateUrl: './filter.component.html',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatSidenavModule,
    MMSSelectMenuComponent,
    ValidationMessageComponent,
    BlockUIDirective,
    MmsSelectLocationComponent,
  ]
})
export class FilterComponent implements OnInit, AfterViewInit, OnDestroy {

  filterAvailableAssetFields$!: Observable<Field[]>;
  isFieldFilterDisabled$!: Observable<boolean>;
  isLoading$!: Observable<boolean>;
  locationTypes$!: Observable<LocationTypeEntity[]>;
  formGroup!: FormGroup;
  matcher: any;
  @ViewChild('fieldsDdl', { static: false}) fieldsMatSelect!: MMSSelectMenuComponent;
  @ViewChild('form', { read: FormGroupDirective, static: true }) formGroupDirective!: FormGroupDirective;

  @ViewChild('locationTypeMenu', { static: false}) locationsTypeMenu!: MMSSelectMenuComponent;

  subscriptions: Subscription[] = [];

  get fieldsFormArray() {
    return <FormArray>this.formGroup.get('fields');
  }

  constructor(private store$: Store<MasterInventoryStoreState.State>,
    private matDrawer: MatDrawer,
    private formBuilder: FormBuilder,
    private locationStore$: Store<LocationInstanceStoreState.State>) { }

  ngOnInit() {
    this.filterAvailableAssetFields$ = this.store$.select(MasterInventoryStoreSelectors.selectFilterAvailableAssetFields);
    this.isFieldFilterDisabled$ = this.filterAvailableAssetFields$.pipe(map((fields: Field[]) => fields === null || fields.length === 0))
    this.isLoading$ = this.store$.select(MasterInventoryStoreSelectors.selectIsMasterInventoryLoading);
    this.locationTypes$ = this.store$.select(MasterInventoryStoreSelectors.selectLocationTypes);

    this.formGroup = this.formBuilder.group({
      location: [null],
      locationTypeId: [null],
      fields: this.formBuilder.array([])
    });

    this.subscriptions.push(this.store$.select(MasterInventoryStoreSelectors.selectSelectedFilterField).pipe(
      filter((field): field is Field => field !== null)
    ).subscribe((field: Field) => {
        if (field) {
          this.generateFieldFormGroup(field);
        }
      }));

    this.store$.dispatch(new MasterInventoryStoreActions.GetFilteredLocationAction());
  }

  ngAfterViewInit() {
    this.matcher = new AssetFieldsErrorStateMatcher(this.formGroupDirective);
    this.locationStore$.dispatch(new LocationInstanceStoreActions.SearchTextChangedAction(['']));
    setTimeout(() => {
      this.store$.select(MasterInventoryStoreSelectors.selectSelectedFilter)
      .pipe(
        take(1),
        filter((filter): filter is FilterModel => filter !== null)
      )
      .subscribe((filter: FilterModel) => {
        if (filter != null) {
          this.formGroup.patchValue({
            location: filter.location,
            locationTypeId: filter.locationTypeId
          });
            if (filter.fields && filter.fields.length > 0) {
              filter.fields.forEach(x => {
                this.generateFieldFormGroup(x);
                this.store$.dispatch(new MasterInventoryStoreActions.RemoveAvailableFieldAction({ fieldId: x.id! }));
              });
            }
            if (filter.location) {
              this.store$.dispatch(new LocationInstanceStoreActions.SearchTextChangedAction([filter.location]));
            }
          }
        })
    });
  }

  ngOnDestroy() {
    this.clear();
    this.subscriptions.forEach((subscription: Subscription) => subscription.unsubscribe());
  }

  close() {
    this.matDrawer.close();
    this.store$.dispatch(new MasterInventoryStoreActions.CloseSidebarAction());
  }

  setFilter(fieldId: string) {
    this.store$.dispatch(new MasterInventoryStoreActions.SetFilterFieldAction({ fieldId: fieldId }));
    setTimeout(() => {
      this.fieldsMatSelect.value = null;
      this.fieldsMatSelect.focused = false;
    });
  }

  clear() {
    this.store$.dispatch(new MasterInventoryStoreActions.ResetFilterAction());
    this.locationStore$.dispatch(new LocationInstanceStoreActions.SearchTextChangedAction(['']));
    while (this.fieldsFormArray.length !== 0) {
      this.fieldsFormArray.removeAt(0)
    }
    this.formGroupDirective.resetForm({
      location: null,
      locationTypeId: null,
      fields: []
    });
  }

  removeFilterAt(index: number) {
    this.store$.dispatch(new MasterInventoryStoreActions.RemoveFilterFieldAction({ fieldId: this.fieldsFormArray.value[index].id }));
    this.fieldsFormArray.removeAt(index);
  }

  search() {
    const location = this.formGroup.controls['location'].value;
    this.formGroup.controls['location'].setValue(location ? location.id : null);
    const data = this.formGroup.value;
    if (this.formGroup.valid) {
      this.store$.dispatch(new MasterInventoryStoreActions.SearchRequestAction(data));
    }
  }

  private generateFieldFormGroup(field: { id?: string, name?: string, type?: FieldTypeEnum, availableValues?: string[], value?: any, operator?: number }) {
    const fieldOperators = getFieldOperators();
    const fOperators = fieldOperators.find(x => x.fieldType === field.type)
    let fOperator: FieldOperatorEnum;
    const operatorValidators = [];

    if (fOperators != null) {
      operatorValidators.push(Validators.required());
      fOperator = null!;
    } else {
      fOperator = FieldOperatorEnum.Equals;
    }

    if ((field.type === FieldTypeEnum.CheckBox || field.type === FieldTypeEnum.File) && field.value == null) {
      field.value = false;
    }

    this.fieldsFormArray.push(this.formBuilder.group({
      id: [field.id],
      name: [field.name],
      type: [field.type],
      availableValues: [field.availableValues],
      operators: [fOperators != null ? fOperators.operators : null],
      operator: [field.operator != null ? field.operator : fOperator, operatorValidators],
      value: [field.value, [Validators.required()]]
    }))
  }

  locationChanged(location: InventoryLocation) {
    this.formGroup.patchValue({
      locationTypeId: location.locationTypeId
    })
  }

  operatorValueMapper(option: any) {
    return option.value;
  }

  operatorTextMapper(option: any) {
    return option.text;
  }

  loadMoreData(value: string) {
    if (value !== undefined) {
      this.locationStore$.dispatch(new LocationInstanceStoreActions.SearchTextChangedAction([value]));
    }
    this.locationStore$.dispatch(new GridStoreActions.GridLoadItemsAction(LocationInstanceStoreState.LOCATION_INSTANCES_GRID_ID, { nextPageToken: '' }));
  }

  clearLocations(){
    // if there is value in locationsTypeMenu then the state is loaded with the selected type
    if(this.locationsTypeMenu.value !== null) return;
    this.locationStore$.dispatch(new LocationInstanceStoreActions.SearchTextChangedAction(['']));
  }

  changeLocationType(newLocationType : LocationType){
    if(!newLocationType) return;
    if(!newLocationType.name) newLocationType.name = '';
    this.locationStore$.dispatch(new LocationInstanceStoreActions.SearchTextChangedAction([newLocationType.name]));
  }

  clearLocationTypes(){
    this.locationStore$.dispatch(new LocationInstanceStoreActions.SearchTextChangedAction(['']));
  }
}
