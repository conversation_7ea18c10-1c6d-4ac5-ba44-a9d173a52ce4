import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Page } from '../../core/page.service';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import { map, catchError, concatMap, switchMap, tap, withLatestFrom, exhaustMap } from 'rxjs/operators';
import * as featureActions from './actions';
import * as featureState from './state';
import { RouterStoreSelectors } from '../router-store';
import { RouterReducerState } from '@ngrx/router-store';
import { RouterStateUrl } from '../router-store/router-state.serializer';
import { FormResetAction } from '../../shared/forms/connect-form-ngrx';
import { displayMessages } from '../../core/resources/display-messages';
import { FailureTypesService } from '../../configuration/failure-types/services/failure-type.service';
import { GridStoreActions } from '../shared-store/material-grid-store';

@Injectable()
export class FailureTypesStoreEffects {
    constructor(
        private actions$: Actions,
        private page: Page,
        private router: Router,
        private failureTypesService: FailureTypesService,
        private store$: Store<featureState.State>) { }

    loadFailureTypesRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadFailureTypesRequestAction>(
            featureActions.ActionTypes.LOAD_FAILURE_TYPES_REQUEST
        ),
        switchMap(_ =>
            this.failureTypesService.getFailureTypes().pipe(
                map(failureTypes => new featureActions.LoadFailureTypesSuccessAction({ failureTypes })),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        )
    ));

    addNewEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.AddNewAction>(
            featureActions.ActionTypes.ADD_NEW
        ),
        tap(() => {
            this.router.navigate(['/configuration/failure-types']);
        })
    ), { dispatch: false });

    confirmDeleteFailureTypeEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ConfirmDeleteFailureTypeAction>(
            featureActions.ActionTypes.CONFIRM_DELETE_FAILURE_TYPE
        ),
        exhaustMap((_) =>
            this.page.confirm.show('Are you sure you want to delete this failureType?', 'Are you sure?').pipe(
                map(
                    (result) => {
                        if (result) {
                            return new featureActions.DeleteFailureTypeRequestAction();
                        }
                        return new featureActions.NoAction();
                    }
                )
            )
        )
    ));

    deleteFailureTypeEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteFailureTypeRequestAction>(
            featureActions.ActionTypes.DELETE_FAILURE_TYPE_REQUEST
        ),
        withLatestFrom(
            this.store$.select(RouterStoreSelectors.getRouterStoreState),
        ),
        switchMap(([_, routerState]: [featureActions.DeleteFailureTypeRequestAction, RouterReducerState<RouterStateUrl>]) =>
            this.failureTypesService.deleteFailureType(routerState.state.params['id']).pipe(
                map(
                    () => {
                        return new featureActions.DeleteFailureTypeSuccessAction({ id: routerState.state.params['id'] });
                    }
                ),
                catchError(error =>
                    of(new featureActions.LoadFailureAction({ error }))
                )
            )
        )
    ));

    deleteFailureTypeSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteFailureTypeSuccessAction>(
            featureActions.ActionTypes.DELETE_FAILURE_TYPE_SUCCESS
        ),
        concatMap((_) => {
            this.page.notification.show('FailureType deleted');
            return [
                new FormResetAction(),
                new featureActions.AddNewAction()
            ];
        })
    ));

    saveRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SaveFailureTypeAction>(
            featureActions.ActionTypes.SAVE_FAILURE_TYPE_REQUEST
        ),
        exhaustMap((action) =>
            this.failureTypesService.createFailureType(action.payload.failureType).pipe(
                map((failureType) => {
                    return new featureActions.SaveFailureTypeSuccessAction({ failureType: failureType! });
                }),
                catchError(error =>
                    of(new featureActions.LoadFailureAction({ error }))
                )
            )
        )
    ));

    saveRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SaveFailureTypeSuccessAction>(
            featureActions.ActionTypes.SAVE_FAILURE_TYPE_SUCCESS
        ),
        map(() => {
            this.page.notification.show('FailureType added');
            return new FormResetAction();
        })
    ));

    updateRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateFailureTypeAction>(
            featureActions.ActionTypes.UPDATE_FAILURE_TYPE_REQUEST
        ),
        exhaustMap((action) =>
            this.failureTypesService.updateFailureType(action.payload.failureType).pipe(
                map(() =>
                    new featureActions.UpdateFailureTypeSuccessAction({ failureType: { id: action.payload.failureType.id, changes: action.payload.failureType } })
                ),
                catchError(error =>
                    of(new featureActions.LoadFailureAction({ error }))
                )
            )
        )
    ));

    updateRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateFailureTypeSuccessAction>(
            featureActions.ActionTypes.UPDATE_FAILURE_TYPE_SUCCESS
        ),
        tap(() =>
            this.page.notification.show('FailureType updated')
        )
    ), { dispatch: false });

    canBeDeletedEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.CanBeDeletedAction>(
            featureActions.ActionTypes.CAN_BE_DELETED
        ),
        withLatestFrom(
            this.store$.select(RouterStoreSelectors.getRouterStoreState),
        ),
        exhaustMap(([_, routerState]: [featureActions.CanBeDeletedAction, RouterReducerState<RouterStateUrl>]) =>
            this.failureTypesService.canBeDeleted(routerState.state.params['id']).pipe(
                map((response) => {
                    if (!response) {
                        return new featureActions.ConfirmDeleteFailureTypeAction();
                    } else {
                        this.page.alert.warning(displayMessages.exceptions.itemIsAssociated('failureType'));
                        return new featureActions.DeleteFailureTypeCancelation();
                    }
                }),
                catchError(error =>
                    of(new featureActions.LoadFailureAction({ error }))
                )
            )
        )
    ));

    searchReportRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadFailureRatesReportRequestAction>(
            featureActions.ActionTypes.LOAD_FAILURE_RATES_REPORT_REQUEST
        ),
        exhaustMap((action) =>
            this.failureTypesService.getFailureRateReportData(action.payload.filter).pipe(
                map((data) =>
                    new featureActions.LoadFailureRatesReportSuccessAction({ data })
                ),
                catchError(error =>
                    of(new featureActions.LoadFailureAction({ error }))
                )
            )
        )
    ));
    
    searchReportSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadFailureRatesReportSuccessAction>(
            featureActions.ActionTypes.LOAD_FAILURE_RATES_REPORT_SUCCESS
        ),
        map((action) => {
            this.router.navigate(['/reports/failure-rates']);
            return new GridStoreActions.GridLoadItemsSuccessAction(featureState.FAILURE_RATES_REPORT_GRID_ID, { items: action.payload.data.reportGridData! });
        })
    ));

    showReportFilter$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ShowReportFilterAction>(
            featureActions.ActionTypes.SHOW_REPORT_FILTER
        ), 
        tap((action: featureActions.ShowReportFilterAction) => {
            this.router.navigate(['/reports/failure-rate-filter']);
        })
    ), { dispatch: false });
}