import { Component, OnInit, Output, EventEmitter, Input, OnDestroy, ViewChild, AfterViewInit, Optional, Self, ElementRef, HostBinding, OnChanges, DoCheck, TemplateRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroupDirective, NgControl, ControlValueAccessor, NgForm, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';
import { MatSelect, MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ErrorStateMatcher } from '@angular/material/core';
import { Subject, ReplaySubject } from 'rxjs';
import { takeUntil, debounceTime } from 'rxjs/operators';
import { FocusMonitor } from '@angular/cdk/a11y';

import { OptionGroup } from './option-group.model';
import { TicketUserModel } from '../../../../configuration/shared/user-and-groups/ticket-user.model';

@Component({
    selector: 'mms-select-menu',
    templateUrl: './mms-select-menu.component.html',
    styleUrls: ['./mms-select-menu.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatSelectModule,
        MatFormFieldModule,
        MatIconModule,
        MatButtonModule,
        MatTooltipModule, 
    ],
    providers: [
        {
            provide: MatFormFieldControl,
            useExisting: MMSSelectMenuComponent
        }
    ]
})
export class MMSSelectMenuComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy, DoCheck, ControlValueAccessor, MatFormFieldControl<any> {
    static nextId = 0;
    @HostBinding() id = `mms-select-menu-${MMSSelectMenuComponent.nextId++}`;
    
    // Form control properties
    stateChanges = new Subject<void>();
    focused = false;
    touched = false;
    controlType = 'mms-select-menu';
    @Input() disabled = false;
    @Input() placeholder = '';
    @Input() required = false;
    @Input() errorState = false;

    // Component specific properties
    @ViewChild('select') select!: MatSelect;
    @Input() options: any[] = [];
    @Input() multiple = false;
    @Input() isTicketUser = false;
    @Input() buttonTrigger = false;
    @Input() buttonTriggerText = '';
    @Input() optionGroups: OptionGroup[] = [];
    @Input() groupClassifier: Function = () => true;
    @Input() valueAsOptions = false;
    @Input() disableOptionCentering = false;
    @Input() disableClearValue = false;
    @Input() disableControl = false;
    @Input() headerTemplate: TemplateRef<any> | null = null;
    @Input() readonly = false;
    @Output() selectionChange = new EventEmitter<any>();
    @Output() onOpenedChangeEmitter = new EventEmitter<boolean>();
    @Output() clearValueEmitter = new EventEmitter<void>();
    @Output() saveSelect = new EventEmitter<void>();
    @Output() blur = new EventEmitter<void>();

    formControl = new FormControl();
    formControlFilter = new FormControl();
    filteredOptions = new ReplaySubject<any[]>(1);
    
    private _value: any = null;
    private _destroy = new Subject<void>();

    constructor(
        @Optional() @Self() public ngControl: NgControl,
        private _focusMonitor: FocusMonitor,
        private _elementRef: ElementRef<HTMLElement>,
        @Optional() private _formField: MatFormFieldModule,
        @Optional() public _parentForm: NgForm,
        @Optional() public _parentFormGroup: FormGroupDirective,
        private _defaultErrorStateMatcher: ErrorStateMatcher
    ) {
        if (this.ngControl != null) {
            this.ngControl.valueAccessor = this;
        }
    }

    @Input()
    textMapper = (option: any) => {
        return option['name'];
    }

    @Input()
    valueMapper = (option: any) => {
        return option['id'];
    }

    @Input()
    additionalInfoMapper = (option: any) => {
        return option['email'];
    }

    @Input()
    compareFn = (c1: any, c2: any) => {
        return c1 === c2;
    }

    compareUserFn(c1: TicketUserModel, c2: any): boolean {
        return c1 && c2 && c1.id === c2;
    }

    toggleSelect() {
        if (this.select) {
            this.select.toggle();
        }
    }

    onOpenedChange(isOpen: boolean) {
        this.onOpenedChangeEmitter.emit(isOpen);
    }

    clearValue() {
        this.value = null;
        this.clearValueEmitter.emit();
    }

    onBlur() {
        this.focused = false;
        this.blur.emit();
        this._onTouched();
    }

    // ControlValueAccessor methods
    writeValue(value: any) {
        this._value = value;
        this.stateChanges.next();
    }

    registerOnChange(fn: any) {
        this._onChange = fn;
    }

    registerOnTouched(fn: any) {
        this._onTouched = fn;
    }

    setDisabledState(isDisabled: boolean) {
        this.disabled = isDisabled;
        this.stateChanges.next();
    }

    // MatFormFieldControl methods
    get empty() {
        return !this._value;
    }

    get shouldLabelFloat() {
        return this.focused || !this.empty;
    }

    get value(): any {
        return this._value;
    }

    @Input()
    set value(value: any) {
        this._value = value;
        this._onChange(value);
        this.stateChanges.next();
    }

    setDescribedByIds(ids: string[]) {
        const controlElement = this._elementRef.nativeElement
            .querySelector('.mms-select-menu-container');
        if (controlElement) {
            controlElement.setAttribute('aria-describedby', ids.join(' '));
        }
    }

    onContainerClick(event: MouseEvent) {
        if ((event.target as Element).tagName.toLowerCase() != 'input') {
            this._elementRef.nativeElement.querySelector('input')?.focus();
        }
    }

    // Private methods
    private _onChange = (_: any) => {};
    private _onTouched = () => {};

    ngOnInit() {
        // Filter setup
        this.formControlFilter.valueChanges
            .pipe(
                takeUntil(this._destroy),
                debounceTime(200)
            )
            .subscribe(() => {
                this.filterOptions();
            });
    }

    ngAfterViewInit() {
        this._focusMonitor
            .monitor(this._elementRef.nativeElement, true)
            .subscribe(origin => {
                this.focused = !!origin;
                this.stateChanges.next();
            });
    }

    ngOnChanges() {
        this.stateChanges.next();
    }

    ngOnDestroy() {
        this._destroy.next();
        this._destroy.complete();
        this._focusMonitor.stopMonitoring(this._elementRef.nativeElement);
    }

    ngDoCheck() {
        if (this.ngControl) {
            // We need to re-evaluate this on every change detection cycle, because there are some
            // error triggers that we can't subscribe to (e.g. parent form submissions). This means
            // that whatever logic is in here has to be super lean or we risk destroying the performance.
            this.updateErrorState();
        }
    }

    private filterOptions() {
        if (!this.options) {
            return;
        }

        let search = this.formControlFilter.value;
        if (!search) {
            this.filteredOptions.next(this.options.slice());
            return;
        }

        search = search.toLowerCase();
        this.filteredOptions.next(
            this.options.filter(option => 
                this.textMapper(option).toLowerCase().indexOf(search) > -1)
        );
    }

    private updateErrorState() {
        const oldState = this.errorState;
        const parent = this._parentFormGroup || this._parentForm;
        const matcher = this._defaultErrorStateMatcher;
        const control = this.ngControl ? this.ngControl.control as FormControl : null;
        const newState = matcher.isErrorState(control, parent);

        if (newState !== oldState) {
            this.errorState = newState;
            this.stateChanges.next();
        }
    }
} 