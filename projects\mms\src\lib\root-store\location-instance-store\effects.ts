import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, Action, select } from '@ngrx/store';
import { Observable, of as observableOf, timer, pipe, forkJoin, of } from 'rxjs';
import { switchMap, map, catchError, exhaustMap, withLatestFrom, tap, debounce } from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state';
import * as featureSelectors from './selectors'
import { AgencyStoreSelectors } from '../agency-store';

import { Page } from '../../core/page.service';
import { LocationInstancesService } from '../../configuration/location-instances/shared/location-instances.service';
import { IGridStoreEffects } from '../shared-store/material-grid-store/effects';
import { GridStoreActions, GridStoreState } from '../shared-store/material-grid-store';
import { LocationInstanceGridModel } from '../../configuration/location-instances/shared/location-instance-grid-element';
import { LocationInstance } from '../../configuration/location-instances/shared/location-instance.model';
import { Region } from '../../configuration/regions/region.model';
import { Router } from '@angular/router';
import { FieldValue } from '../../configuration/shared/asset-field/field-value.model';
import { StatusDetailsService } from '../../configuration/shared/pm-status-details/pm-status-details.service';
import { LatLong } from '@econolite/limestone';
import { StatusDetails } from '../../configuration/shared/pm-status-details/pm-status-details.model';
import { LocationInstancesListData } from '../../configuration/location-instances/shared/location-data.model';
import { RouterStoreSelectors } from '../router-store';
import { RegionsService } from '../../configuration/regions/regions.service';
import { UserIdentity } from '@econolite/identity-client';
import { AreaTypeEnum } from '../../configuration/location-instances/shared/area-type.enum';
import { FieldTypeEnum } from '../../configuration/shared/asset-field/field-type.enum';
import { MapStoreSelectors } from '../map-store';
import { UserStoreSelectors } from '../user-store';
import { TimesheetStoreSelectors } from '../timesheet-store';

interface ExtendedLocationInstance extends LocationInstance {
    latitudeLongitude?: LatLong;
    lastLoggedUser?: string;
    vehicleUser?: string;
}

@Injectable()
export class LocationInstanceStoreEffects implements IGridStoreEffects {
    constructor(
        private locationInstancesService: LocationInstancesService,
        private actions$: Actions,
        private store$: Store<featureState.State>,
        private page: Page,
        private router: Router,
        private statusDetialsService: StatusDetailsService,
        private regionsService: RegionsService,
        private userIdentity: UserIdentity
    ) { }

    gridLoadItems(actionType: string): Observable<Action> {
        return this.actions$.pipe(
            ofType<GridStoreActions.GridLoadItemsAction<LocationInstanceGridModel>>(actionType),
            withLatestFrom(
                this.store$.select(featureSelectors.selectSearchText),
                this.store$.select(featureSelectors.selectNextPageToken),
                this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
                this.store$.select(AgencyStoreSelectors.selectAssignedAgenciesToUser),
                this.store$.select(AgencyStoreSelectors.isAdmin),
                this.store$.select(featureSelectors.getSelectedFilter),
                this.store$.select(AgencyStoreSelectors.isTech)
            ),
            switchMap(([action, searchText, nextPageToken, agency, assignedAgencies, isAdmin, selectedFilter, isTech]) => {
                if (!agency) {
                    return of(new GridStoreActions.GridNoAction());
                }
                if (searchText[0].length > 0 || searchText[0] === '') {
                    if (searchText[0] === 'scrap') {
                        return this.locationInstancesService.getScrapLocations(agency, (action.payload !== undefined && action.payload.nextPageToken != '') ? action.payload.nextPageToken : nextPageToken)
                        .pipe(
                            switchMap(result => {
                                var res = result.items?.filter(function(el) { return el; }) || [];
                                return [
                                    new GridStoreActions.GridLoadItemsSuccessAction(featureState.LOCATION_INSTANCES_GRID_ID, { items: res, nextPageToken: result.nextPageToken, clearGrid: (action.payload === undefined  || action.payload.nextPageToken === null) })
                                ];
                            }),
                        )
                    } else {
                        let elseValue = nextPageToken;
                        if (this.router.url === '/locations') elseValue = '';
                        if (isAdmin || isTech) {
                            let numberPerPage = 50;
                            return this.locationInstancesService.getLocationInstancesGridItems(numberPerPage, searchText, agency, selectedFilter?.locationTypes || undefined, (action.payload !== undefined && action.payload.nextPageToken != '') ? action.payload.nextPageToken : elseValue)
                            .pipe(
                                switchMap(result => {
                                    var res = result.items?.filter(function(el) { return el; }) || [];
                                    return [
                                        new GridStoreActions.GridLoadItemsSuccessAction(featureState.LOCATION_INSTANCES_GRID_ID, { items: res, nextPageToken: result.nextPageToken, clearGrid: (action.payload === undefined  || action.payload.nextPageToken === null) })
                                    ];
                                }),
                            );
                        } else if (assignedAgencies?.length) {
                            // If not Admin, search locations for all assigned agencies he has
                            const filteredAgencies = assignedAgencies.filter((agency): agency is string => agency !== undefined);
                            return this.locationInstancesService.getLocationInstancesGridItemsForAgencies(searchText, filteredAgencies, selectedFilter?.locationTypes || undefined, (action.payload !== undefined && action.payload.nextPageToken != '') ? action.payload.nextPageToken : elseValue)
                            .pipe(
                                switchMap(result => {
                                    var res = result.items?.filter(function(el) { return el; }) || [];
                                    return [
                                        new GridStoreActions.GridLoadItemsSuccessAction(featureState.LOCATION_INSTANCES_GRID_ID, { items: res, nextPageToken: result.nextPageToken, clearGrid: (action.payload === undefined  || action.payload.nextPageToken === null) })
                                    ];
                                }),
                            );
                        }
                    }
                }
                return observableOf(new GridStoreActions.GridNoAction());
            }

            ),
            catchError((error) => observableOf(new GridStoreActions.GridLoadItemsFailedAction(featureState.LOCATION_INSTANCES_GRID_ID, error)))
        );
    }

    gridLoadItemsSuccess(actionType: string): Observable<Action> {
        return this.actions$.pipe(ofType(actionType)
            , map((action) => new GridStoreActions.GridNoAction));
    }

    gridLoadItemsFailed(actionType: string): Observable<Action> {
        return this.actions$.pipe(ofType(actionType)
            , map((action) => new GridStoreActions.GridNoAction));
    }

    // initiate the fetch actions for each role
    // tslint:disable-next-line:member-ordering
    gridLoadItems$ = createEffect(() => 
        this.gridLoadItems(GridStoreActions.ActionTypes.GRID_LOAD_ITEMS(featureState.LOCATION_INSTANCES_GRID_ID))
    );

    // initiate the fetch actions for each role
    // tslint:disable-next-line:member-ordering
    gridLoadItemsSuccess$ = createEffect(() => 
        this.gridLoadItemsSuccess(GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_SUCCESS(featureState.LOCATION_INSTANCES_GRID_ID))
    );

    // initiate the fetch actions for each role
    // tslint:disable-next-line:member-ordering
    gridLoadItemsFailed$ = createEffect(() => 
        this.gridLoadItemsFailed(GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_FAILED(featureState.LOCATION_INSTANCES_GRID_ID))
    );
    

    // tslint:disable-next-line:member-ordering
    loadLocationsEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadLocationsAction>(featureActions.ActionTypes.LOAD_LOCATIONS),
        map(() => new GridStoreActions.GridLoadItemsAction(featureState.LOCATION_INSTANCES_GRID_ID))
    ));

    // tslint:disable-next-line:member-ordering
    loadLocationDataRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadLocationDataRequestAction>(featureActions.ActionTypes.LOAD_LOCATION_DATA_REQUEST),
        withLatestFrom(
            this.store$.select(RouterStoreSelectors.getRouterStoreState),
            this.store$.select(MapStoreSelectors.selectedLocationId)
        ),
        switchMap(([action, routerState, mapSelectedLocationId]) => {
            let locationInstanceId = routerState.state.params['id'];
            if (routerState.state.url.includes('locations/map/')) {
                locationInstanceId = routerState.state.params['locationId'];
            } else if (routerState.state.url.includes('/map') || routerState.state.url.includes('/dashboard')) {
                locationInstanceId = mapSelectedLocationId;
            }
            const locationTypeObservable = this.locationInstancesService.getLocationTypes();
            const locationInstance = locationInstanceId ? this.locationInstancesService.getLocationInstanceById(locationInstanceId) : of(null);

            return forkJoin([locationTypeObservable, locationInstance]).pipe(
                map(data => {
                    if (data) {
                        return new featureActions.LoadLocationDataSuccessAction({ locationTypes: data[0].locationTypes, locationInstance: data[1] as LocationInstance });
                    } else {
                        return new featureActions.NoAction();
                    }
                }),
                catchError(error =>
                    observableOf(new featureActions.LoadLocationDataFailureAction({ error }))
                )
            );
        })
    ));

    // tslint:disable-next-line:member-ordering
    gridLoadSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<GridStoreActions.GridLoadItemsSuccessAction<LocationInstanceGridModel>>(
            GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_SUCCESS(featureState.LOCATION_INSTANCES_GRID_ID)
        ),
        map((action) => new featureActions.SetInitialLoadAction())
    ));

    // tslint:disable-next-line:member-ordering
    loadLocationInstanceRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadRequestAction>(featureActions.ActionTypes.LOAD_REQUEST),
        exhaustMap(action =>
            this.locationInstancesService.getLocationInstanceById(action.payload.locationInstanceId)
                .pipe(
                    map(locationInstance => {
                        this.router.navigate(['/locations/edit-location/' + action.payload.locationInstanceId]);
                        return new featureActions.LoadSuccessAction({ locationInstance: locationInstance });
                    }),
                    catchError(error =>
                        observableOf(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    // tslint:disable-next-line:member-ordering
    saveEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SaveAction>(featureActions.ActionTypes.SAVE),
        withLatestFrom(
            this.store$.pipe(select(featureSelectors.selectedLocationInstance)),
            this.store$.pipe(select(AgencyStoreSelectors.selectDefaultAgency))
        ),
        map(([action, selectedLocationInstance, selectedAgency]) => {
            if (!selectedLocationInstance) {
                if (!selectedAgency) {
                    return new featureActions.NoAction();
                }
                action.payload.item.agency = selectedAgency;
                return new featureActions.SaveRequestAction({ item: action.payload.item });
            } else {
                const locationInstance = action.payload.item;
                locationInstance.id = action.payload.item.id;
                locationInstance.agency = selectedLocationInstance.agency;
                return new featureActions.UpdateRequestAction({ item: locationInstance });
            }
        })
    ));

    // tslint:disable-next-line:member-ordering
    saveRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SaveRequestAction>(featureActions.ActionTypes.SAVE_REQUEST),
        exhaustMap((action) =>
            this.locationInstancesService
                .addLocationInstance(action.payload.item)
                .pipe(
                    map((locationInstance) => {
                        if (!locationInstance) {
                            throw new Error('Failed to save location instance');
                        }
                        return new featureActions.SaveSuccessAction({ item: locationInstance });
                    }),
                    catchError(error => observableOf(new featureActions.LoadFailureAction({ error })))
                )
        )
    ));

    // tslint:disable-next-line:member-ordering
    saveRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SaveSuccessAction>(featureActions.ActionTypes.SAVE_SUCCESS),
        withLatestFrom(
            this.store$.pipe(select(featureSelectors.selectGrid)), 
            this.store$.pipe(select(featureSelectors.selectRegions))
        ),
        map(([action, gridState, regions]: [featureActions.SaveSuccessAction, GridStoreState.State<LocationInstanceGridModel>, Region[]]) => {
            const data = action.payload.item;
            let regionNames = '';
            const regionField = data.fieldValues?.find(value => value?.type === FieldTypeEnum.Regions);
            if (regionField?.value?.length) {
                regionNames = regionField.value.map((region: { name: string }) => region.name).join(", ");
            }
            
            const newItem = {
                id: data.id ?? '',
                name: this.getFieldByName(data.fieldValues ?? [], 'Name').value,
                locationTypeName: data.locationType?.name ?? '',
                description: this.getFieldByName(data.fieldValues ?? [], 'Description').value,
                latitudeLongitude: this.getFieldByName(data.fieldValues ?? [], 'Latitude Longitude').value,
                notes: this.getFieldByName(data.fieldValues ?? [], 'Notes').value,
                jurisdiction: this.getFieldByName(data.fieldValues ?? [], 'Jurisdiction').value,
                regions: regionNames,
                agency: data.agency?.name ?? ''
            };

            this.page.notification.show('Location added');
            this.router.navigateByUrl('/locations', { state: { shouldDeactive: false } });
            return new GridStoreActions.GridNewItemAddedAction(featureState.LOCATION_INSTANCES_GRID_ID, { item: newItem });
        })
    ));

    // tslint:disable-next-line:member-ordering
    updateRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateSuccessAction>(
            featureActions.ActionTypes.UPDATE_SUCCESS
        ),
        withLatestFrom(this.store$.pipe(select(featureSelectors.selectRegions))),
        map(([action, regions]: [featureActions.UpdateSuccessAction, Region[]]) => {
            const data = action.payload.item.changes;

            let regionNames = '';
            const regionField = data.fieldValues?.find(value => value?.type === FieldTypeEnum.Regions);

            if (regionField && regionField.multipleValues && regionField.multipleValues.length) {
                regionNames = regionField.multipleValues.map(x => x.name).join(", ");
            }

            const newItem = {
                id: data.id ?? '',
                name: this.getFieldByName(data.fieldValues ?? [], 'Name').value,
                locationTypeName: data.locationType?.name ?? '',
                description: this.getFieldByName(data.fieldValues ?? [], 'Description').value,
                latitudeLongitude: this.getFieldByName(data.fieldValues ?? [], 'Latitude Longitude').value,
                notes: this.getFieldByName(data.fieldValues ?? [], 'Notes').value,
                jurisdiction: this.getFieldByName(data.fieldValues ?? [], 'Jurisdiction').value,
                regions: regionNames,
                agency: data.agency?.name ?? ''
            };

            this.page.notification.show('Location updated');
            this.router.navigateByUrl('/locations', {state: {shouldDeactive: false}});
            return new GridStoreActions.GridItemUpdatedAction(featureState.LOCATION_INSTANCES_GRID_ID, { itemId: newItem.id.toString(), item: newItem });
        })
    ));

    // tslint:disable-next-line:member-ordering
    locationCanBeDeletedEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.CanBeDeletedAction>(
            featureActions.ActionTypes.CAN_BE_DELETED
        ),
        exhaustMap(action =>
            this.locationInstancesService
                .canLocationBeDeleted(action.payload.locationInstanceId)
                .pipe(
                    map(
                        (response) => {
                            if (response) {
                                return new featureActions.ConfirmDeleteAction({ locationInstanceId: action.payload.locationInstanceId });
                            } else {
                                this.page.alert.error('In order to delete this location, you need to remove all instances associated with it.', 'Warning');
                                return new featureActions.NoAction();
                            }
                        }
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    // tslint:disable-next-line:member-ordering
    confirmDeleteManufacturerEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ConfirmDeleteAction>(
            featureActions.ActionTypes.CONFIRM_DELETE
        ),
        exhaustMap(action =>
            this.page
                .confirm.show('Are you sure you want to delete this location?', 'Are you sure?')
                .pipe(
                    map(
                        (result) => {
                            if (result) {
                                return new featureActions.DeleteRequestAction({ locationInstanceId: action.payload.locationInstanceId });
                            }
                            return new featureActions.NoAction();
                        }
                    )
                )
        )
    ));

    // tslint:disable-next-line:member-ordering
    deleteRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteRequestAction>(
            featureActions.ActionTypes.DELETE_REQUEST
        ),
        exhaustMap(action =>
            this.locationInstancesService
                .deleteLocationInstance(action.payload.locationInstanceId)
                .pipe(
                    map(
                        () =>
                            new featureActions.DeleteSuccessAction({
                                itemId: action.payload.locationInstanceId
                            })
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    // tslint:disable-next-line:member-ordering
    deleteSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteSuccessAction>(
            featureActions.ActionTypes.DELETE_SUCCESS
        ),
        pipe(
            map((action) => {
                this.page.notification.show('Location deleted');
                return new GridStoreActions.GridItemRemovedAction(featureState.LOCATION_INSTANCES_GRID_ID, { itemId: action.payload.itemId });
            })
        )
    ));

    // tslint:disable-next-line:member-ordering
    searchChangeEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SearchTextChangedAction>(
            featureActions.ActionTypes.SEARCH_TEXT_CHANGED
        ),
        debounce(() => timer(300)),
        switchMap((action) => {
            return observableOf(new GridStoreActions.GridLoadItemsAction(featureState.LOCATION_INSTANCES_GRID_ID, { nextPageToken: '' }));
        })
    ));

    // tslint:disable-next-line:member-ordering
    selectedFilterChangeEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SetSelectedFilterAction>(
            featureActions.ActionTypes.SET_SELECTED_FILTER
        ),
        debounce(() => timer(300)),
        switchMap((action) => {
            return observableOf(new GridStoreActions.GridLoadItemsAction(featureState.LOCATION_INSTANCES_GRID_ID, { nextPageToken: '' }));
        })
    ));

    // tslint:disable-next-line:member-ordering
    showCreateLocation$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ShowCreateLocationAction>(
            featureActions.ActionTypes.SHOW_CREATE_LOCATION
        ),
        pipe(tap(action => {
            this.router.navigate(['/locations/add-location/']);
            return new featureActions.NoAction();
        }))
    ), { dispatch: false });

    // tslint:disable-next-line:member-ordering
    showEditLocation$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ShowEditLocationAction>(
            featureActions.ActionTypes.SHOW_EDIT_LOCATION
        ),
        pipe(map(action => {
            this.router.navigate(['locations/edit-location/' + action.payload.locationInstanceId]);
            return new featureActions.NoAction();
        }))
    ), { dispatch: false });

    // tslint:disable-next-line:member-ordering
    closeSidebar$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.CloseSidebarAction>(
            featureActions.ActionTypes.CLOSE_SIDEBAR
        ), tap((action: featureActions.CloseSidebarAction) => {
            this.router.navigate(['/locations']);
        })
    ), { dispatch: false });

    // tslint:disable-next-line:member-ordering
    ClearSelectedDataAction$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ClearSelectedDataAction>(
            featureActions.ActionTypes.CLEAR_SELECTED_DATA
        ),
        pipe(tap(action => {
            return new featureActions.NoAction();
        }))
    ), { dispatch: false });

    // tslint:disable-next-line:member-ordering
    showVehicleMapData$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ShowVehicleMapDataAction>(
            featureActions.ActionTypes.SHOW_VEHICLE_MAP_DATA
        ),
        exhaustMap(action =>
            this.statusDetialsService.getStatusDetailsByLocation(action.payload.locationInstance.id)
                .pipe(
                    map(
                        statusDetails => {
                            if (statusDetails) {
                                action.payload.locationInstance.lastLoggedUser = statusDetails.userId;
                                if (statusDetails.geolocation && (action.payload.locationInstance as ExtendedLocationInstance).latitudeLongitude == null) {
                                    (action.payload.locationInstance as ExtendedLocationInstance).latitudeLongitude = <LatLong>{ coordinates: statusDetails.geolocation.coordinates.split(',').map(Number) };
                                }
                            }
                            return new featureActions.ShowVehicleMapDataActionSuccess({ locationInstance: action.payload.locationInstance });
                        }
                    )
                )
        )
    ));

    showVehicleMapDataSuccess$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ShowVehicleMapDataActionSuccess>(
            featureActions.ActionTypes.SHOW_VEHICLE_MAP_DATA_SUCCESS
        ),
        pipe(
            map((action) => {
                this.router.navigate(['/locations/map/' + action.payload.locationInstance.id]);
                return new featureActions.NoAction();
            })
        )
    ), { dispatch: false });

    // tslint:disable-next-line:member-ordering
    showMapData$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ShowMapDataAction>(
            featureActions.ActionTypes.SHOW_MAP_DATA
        ),
        pipe(
            map((action) => {
                this.router.navigate(['/locations/map/' + action.payload.locationInstance.id]);
                return new featureActions.NoAction();
            })
        )
    ), { dispatch: false });

    // tslint:disable-next-line: member-ordering
    refreshMapData$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.RefreshMapDataAction>(
            featureActions.ActionTypes.REFRESH_MAP_DATA
        ),
        switchMap(action => {
            let numberPerPage = -1;
            const locationInstancesGridItem = this.locationInstancesService.getLocationInstancesGridItems(numberPerPage, [action.payload.locationInstanceId], undefined);
            const statusDetails = this.statusDetialsService.getStatusDetailsByLocation(action.payload.locationInstanceId);
            return forkJoin([locationInstancesGridItem, statusDetails]).pipe(
                map(([gridItems, driverDetails]: [LocationInstancesListData, StatusDetails]) => {
                    if (driverDetails && driverDetails.geolocation && gridItems.items && gridItems.items.length > 0) {
                        (gridItems.items[0] as any).latitudeLongitude = <LatLong>{ coordinates: driverDetails.geolocation.coordinates.split(',').map(Number) };
                        (gridItems.items[0] as any).vehicleUser = driverDetails.userId;
                    }
                    if (!gridItems.items || gridItems.items.length === 0) {
                        throw new Error('No location instance found');
                    }
                    return new featureActions.UpdateMapDataAction({ locationInstance: gridItems.items[0] });
                })
            );
        })
    ));

    getFieldByName(fields: Array<FieldValue>, name: string) {
        const field = fields.find(x => (x?.name ?? '').toLowerCase() === name.toLowerCase());
        return field ? field : <FieldValue>{};
    }

    // tslint:disable-next-line:member-ordering
    loadRegions$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadRegionsByAgencyAction>(
            featureActions.ActionTypes.LOAD_REGIONS_BY_AGENCY
        ),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
        ),
        switchMap(([_, agency]) =>
            this.regionsService.getRegions(agency || undefined)
                .pipe(
                    map(
                        data => new featureActions.LoadRegionsByAgencySuccessAction({ regions: data })
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadRegionsFailureAction({ error }))
                    )
                )
        )
    ));

    // tslint:disable-next-line: member-ordering
    loadLocationsAndRegions$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadLocationsAndRegionsRequest>(
            featureActions.ActionTypes.LOAD_LOCATIONS_AND_REGIONS_REQUEST
        ),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
            this.store$.select(featureSelectors.selectLocationNextPageToken),
            this.store$.select(featureSelectors.selectLocationsAndRegions),
            this.store$.select(RouterStoreSelectors.getRouterStoreState),
            this.store$.select(UserStoreSelectors.selectUserById),
            this.store$.select(AgencyStoreSelectors.selectAllAgencyItems),
            this.store$.select(AgencyStoreSelectors.isAdmin)
        ),
        switchMap(([action, agency, nextPageToken, existinglocationsAndRegions, routerState, selectCurrentUser, agencies, isAdmin]) => {
            if (routerState.state.url.includes('call-tickets') || routerState.state.url.includes('/dashboard')) {
                agency = undefined;
            }
            let numberPerPage = -1;
            const locationsRequest = this.locationInstancesService.getLocationInstancesGridItems(numberPerPage, undefined, agency || undefined, nextPageToken);
            const regionsRequest = nextPageToken ? of([]) : this.regionsService.getRegions(agency || undefined);
            return forkJoin([locationsRequest, regionsRequest]).pipe(
                map(([locations, regions]: [LocationInstancesListData, Region[]]) => {
                    let locationsAndRegions = existinglocationsAndRegions.options && existinglocationsAndRegions.options.length ? existinglocationsAndRegions.options : [];
                    const nextPageToken = locations.nextPageToken;
                    if (locations.items && locations.items.length) {
                        locations.items.forEach(data => {
                            (data as any).areaType = AreaTypeEnum.Location;
                            locationsAndRegions.push(data);
                        });
                    }
                    if (regions && regions.length) {
                        regions.forEach(data => {
                            (data as any).areaType = AreaTypeEnum.Region;
                            locationsAndRegions.push(data);
                        });
                    }

                    if (routerState.state.url.includes('call-tickets/add-ticket') || routerState.state.url.includes('call-tickets/edit-ticket') || routerState.state.url.includes('/dashboard')) {
                        if (locationsAndRegions && action.callTicketData && action.callTicketData.name == "All" && !isAdmin) {
                            let ids = [];
                            let defaultAgencyOfUser = selectCurrentUser && agencies ? agencies.filter(x => x.id == selectCurrentUser.defaultAgencyId) : null;

                            if (selectCurrentUser && selectCurrentUser.assignedAgencies != null) {
                                for (let i = 0; i < selectCurrentUser.assignedAgencies.length; i++) {
                                    ids.push(selectCurrentUser.assignedAgencies[i].id);
                                }
                            } else if (selectCurrentUser && defaultAgencyOfUser && defaultAgencyOfUser.length > 0) {
                                ids.push(defaultAgencyOfUser[0].name);
                            }

                            locationsAndRegions = ids && selectCurrentUser ? locationsAndRegions.filter((item: any) => ids.includes(typeof item.agency === 'string' ? item.agencyId : item.agency.id)) : null;
                        }
                    }

                    return new featureActions.LoadLocationsAndRegionsSuccess({ locationsAndRegions, nextPageToken });
                })
            );
        })
    ));

    // tslint:disable-next-line:member-ordering
    loadLocationInstancess$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadLocationsRequestAction>(
            featureActions.ActionTypes.LOAD_LOCATION_INSTANCES_REQUEST
        ),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
        ),
        switchMap(([_, agency]) =>
            this.locationInstancesService.getAllLocationsByAgency(agency?.id || '')
                .pipe(
                    map(
                        data => new featureActions.LoadLocationsSuccessAction({ locations: data })
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    // tslint:disable-next-line:member-ordering
    showFilterLocations$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ShowFilterLocationsAction>(
            featureActions.ActionTypes.SHOW_FILTER_LOCATIONS
        ),
        pipe(tap(action => {
            this.router.navigate(['/locations/filter/']);
            return new featureActions.NoAction();
        }))
    ), { dispatch: false });

    // tslint:disable-next-line:member-ordering
    loadLocationTypesRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadLocationTypesRequestAction>(
            featureActions.ActionTypes.LOAD_LOCATION_TYPES_REQUEST
        ),
        exhaustMap(_ =>
            this.locationInstancesService.getLocationTypes()
                .pipe(
                    map(
                        data => {
                            return new featureActions.LoadLocationTypesSuccessAction({ locationTypes: data.locationTypes })
                        }
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    loadVehicleLocationsRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadVehicleLocationsRequestAction>(featureActions.ActionTypes.LOAD_VEHICLE_LOCATIONS_REQUEST),
        withLatestFrom(this.store$.select(TimesheetStoreSelectors.selectSingleTimesheet)),
        switchMap(([_, timesheet]) => {
            const agencyId = timesheet.agencyId;
            return this.locationInstancesService.getVehicleLocations(agencyId).pipe(
                map((result: LocationInstance[]) => new featureActions.LoadVehicleLocationsSuccessAction({ locations: result })),
                catchError(e => of(new featureActions.LoadVehicleLocationsFailureAction({ error: e })))
            );
        })
    ));

    // tslint:disable-next-line:member-ordering
    loadLocationsByUserAssignedAgenciesRequestAction$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadLocationsByUserAssignedAgenciesRequestAction>(
            featureActions.ActionTypes.LOAD_LOCATION_BY_USER_ASSIGNED_AGENCIES_REQUEST
        ),
        withLatestFrom(
            this.store$.select(UserStoreSelectors.selectUserById)
        ),
        switchMap(([_, user]) =>
            this.locationInstancesService.getAllLocationsByUserAssignedAgencies(user?.id || '')
                .pipe(
                    map(
                        data => new featureActions.LoadLocationsByUserAssignedAgenciesSuccessAction( {locations: data.items || [] })
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));
}
