import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, select } from '@ngrx/store';
import { Router } from '@angular/router';

import * as featureActions from './actions';
import * as featureState from './state';
import * as featureSelectors from './selectors';

import { of } from 'rxjs';
import { switchMap, map, catchError, exhaustMap, withLatestFrom, concatMap } from 'rxjs/operators';
import { UserService } from '../../configuration/shared/user-and-groups/users.service';
import { AgencyStoreState } from '../agency-store';
import { RegionStoreState } from '../region-store';
import { MMSConfigService } from '../../settings/mms_config/mms-config.service';
import { OnCallSchedule, OnCallPeriod, OnCallPerson } from '../../on-call-scheduler/models/on-call-scheduler.model';
import { RegionsService } from '../../configuration/regions/regions.service';
import { UserIdentity } from '@econolite/identity-client';
import { GridStoreActions } from '../shared-store/material-grid-store';
import { Page } from '../../core/page.service';

@Injectable()
export class OnCallSchedulerStoreEffects {
    constructor(
        private actions$: Actions,
        private store$: Store<featureState.State>,
        private router: Router,
        private mmsConfigService: MMSConfigService,
        private userService: UserService,
        private regionService: RegionsService,
        private agencyStoreStore$: Store<AgencyStoreState.MainState>,
        private regionStore$: Store<RegionStoreState.MainState>,
        private userIdentity: UserIdentity,
        private page: Page
    ) { }

    loadOnCallAreasEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadOnCallAreaAction>(
            featureActions.ActionTypes.LOAD_ON_CALL_AREAS
        ), withLatestFrom(
            this.store$.pipe(select(featureSelectors.selectOnCallScheduler))
        ),
        exhaustMap((action) => {
            return this.userService.getOnCallAreas()
                .pipe(
                    concatMap(
                        (data) => {
                            data.forEach((schedule: OnCallSchedule) => {
                                schedule.periods?.forEach((period: OnCallPeriod) => {
                                    const localStart = new Date(period.start!);
                                    const localEnd = new Date(period.end!);
                                    period.start = new Date(localStart.getUTCFullYear(), localStart.getUTCMonth(), localStart.getUTCDate());
                                    period.end = new Date(localEnd.getUTCFullYear(), localEnd.getUTCMonth(), localEnd.getUTCDate());
                                    period.onCallPeople.forEach((person: OnCallPerson) => {
                                        if (person.exceptions) {
                                            person.exceptions.start = new Date(person.exceptions.start);
                                            person.exceptions.end = new Date(person.exceptions.end);
                                        }
                                    })
                                })
                            });
                            return [
                                new featureActions.LoadOnCallAreasSuccessAction(data),
                                new GridStoreActions.GridLoadItemsSuccessAction(
                                    featureState.ON_CALL_SCHEDULER_GRID_ID,
                                    { items: data, clearGrid: true }
                                )
                            ];
                        }
                    ),
                    catchError(error =>
                        of(new featureActions.LoadOnCallAreasFailureAction({ error }))
                    )
                )
        })
    ));

    loadOnCallTypesEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadOnCallTypesAction>(
            featureActions.ActionTypes.LOAD_ON_CALL_TYPES
        ), withLatestFrom(
            this.store$.pipe(select(featureSelectors.selectOnCallTypes))
        ),
        exhaustMap((action) => {
            return this.userService.getOnCallTypes()
                .pipe(
                    map(
                        (data) => {
                            return new featureActions.LoadOnCallTypesSuccessAction({ data: data });
                        }
                    ),
                    catchError(error =>
                        of(new featureActions.LoadOnCallTypesFailureAction({ error }))
                    )
                )
        })
    ));

    updateSelectedAgencyEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateSelectedAgencyAction>(
            featureActions.ActionTypes.UPDATE_SELECTED_AGENCY
        ),
        switchMap((action) =>
            this.regionService
                .getRegions(action.payload)
                .pipe(
                    map(
                        item =>
                            new featureActions.LoadRegionsSuccessAction({ data: item })
                    ),
                    catchError(error =>
                        of(new featureActions.LoadRegionsFailureAction({ error }))
                    )
                )
        )
    ));

    loadRegionsEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadRegionsAction>(
            featureActions.ActionTypes.LOAD_REGIONS
        ),
        switchMap(action =>
            this.regionService
                .getRegions(action.payload.data)
                .pipe(
                    map(
                        item =>
                            new featureActions.LoadRegionsSuccessAction({ data: item })
                    ),
                    catchError(error =>
                        of(new featureActions.LoadRegionsFailureAction({ error }))
                    )
                )
        )
    ));

    saveOnCallAreaEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SaveOnCallAreaAction>(
            featureActions.ActionTypes.SAVE_ON_CALL_AREA
        ),
        exhaustMap((action) => {
            return this.userService.editOnCallArea(action.payload)
                .pipe(
                    map(
                        (data) => {
                            data.periods.forEach((period: OnCallPeriod) => {
                                const localStart = new Date(period.start!);
                                const localEnd = new Date(period.end!);
                                period.start = new Date(localStart.getUTCFullYear(), localStart.getUTCMonth(), localStart.getUTCDate());
                                period.end = new Date(localEnd.getUTCFullYear(), localEnd.getUTCMonth(), localEnd.getUTCDate());
                                period.onCallPeople.forEach((person: OnCallPerson) => {
                                    if (person.exceptions) {
                                        person.exceptions.start = new Date(person.exceptions.start);
                                        person.exceptions.end = new Date(person.exceptions.end);
                                    }
                                })
                            });
                            return new featureActions.SaveOnCallAreaSuccessAction(data);
                        }
                    ),
                    catchError(error =>
                        of(new featureActions.SaveOnCallAreaFailureAction({ error }))
                    )
                )
        })
    ));

    createOnCallAreaEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.CreateOnCallAreaAction>(
            featureActions.ActionTypes.CREATE_ON_CALL_AREA
        ),
        exhaustMap((action) => {
            return this.userService.addOnCallArea(action.payload)
                .pipe(
                    map(
                        (data) => {
                            data.periods.forEach((period: OnCallPeriod) => {
                                const localStart = new Date(period.start!);
                                const localEnd = new Date(period.end!);
                                period.start = new Date(localStart.getUTCFullYear(), localStart.getUTCMonth(), localStart.getUTCDate());
                                period.end = new Date(localEnd.getUTCFullYear(), localEnd.getUTCMonth(), localEnd.getUTCDate());
                                period.onCallPeople.forEach((person: OnCallPerson) => {
                                    if (person.exceptions) {
                                        person.exceptions.start = new Date(person.exceptions.start);
                                        person.exceptions.end = new Date(person.exceptions.end);
                                    }
                                })
                            });
                            return new featureActions.CreateOnCallAreaSuccessAction(data);
                        }
                    ),
                    catchError(error =>
                        of(new featureActions.CreateOnCallAreaFailureAction({ error }))
                    )
                )
        })
    ));

    deleteOnCallAreaEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteOnCallAreaAction>(
            featureActions.ActionTypes.DELETE_ON_CALL_AREA
        ),
        exhaustMap((action) => {
            return this.userService.deleteOnCallArea(action.payload)
                .pipe(
                    map(
                        (data) => {
                            if (data) {
                                this.page.notification.show('Area Deleted');
                                return new featureActions.DeleteOnCallAreaSuccessAction(action.payload);
                            }

                            return new featureActions.DeleteOnCallAreaFailureAction({ error: 'Failed to delete schedule' });
                        }
                    ),
                    catchError(error =>
                        of(new featureActions.DeleteOnCallAreaFailureAction({ error }))
                    )
                )
        })
    ));

    createOnCallPeriodEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.CreateOnCallPeriodAction>(
            featureActions.ActionTypes.CREATE_ON_CALL_PERIOD
        ),
        exhaustMap((action) => {
            return this.userService.addNewPeriod(action.payload)
                .pipe(
                    map(
                        (data) => {
                            data.forEach((schedule: OnCallSchedule) => {
                                schedule.periods?.forEach((period: OnCallPeriod) => {
                                    const localStart = new Date(period.start!);
                                    const localEnd = new Date(period.end!);
                                    period.start = new Date(localStart.getUTCFullYear(), localStart.getUTCMonth(), localStart.getUTCDate());
                                    period.end = new Date(localEnd.getUTCFullYear(), localEnd.getUTCMonth(), localEnd.getUTCDate());
                                    period.onCallPeople.forEach((person: OnCallPerson) => {
                                        if (person.exceptions) {
                                            person.exceptions.start = new Date(person.exceptions.start);
                                            person.exceptions.end = new Date(person.exceptions.end);
                                        }
                                    })
                                })
                            });
                            return new featureActions.CreateOnCallPeriodSuccessAction(data);
                        }
                    ),
                    catchError(error =>
                        of(new featureActions.CreateOnCallPeriodFailureAction({ error }))
                    )
                )
        })
    ));
}
