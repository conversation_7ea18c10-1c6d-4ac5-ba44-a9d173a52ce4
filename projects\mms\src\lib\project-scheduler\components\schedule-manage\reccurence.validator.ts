import { Validator, ValidationErrors, ValidatorFn, AbstractControl } from '@angular/forms'
import { getCronType } from '../../../shared/cron-editor/cron-editor.utill';
import { CronType } from '../../../shared/cron-editor/cron-type.enum';

export class ReccurenceValidator implements Validator {

    validate(control: AbstractControl): ValidationErrors | null {
        if (control.value == null || this.getReccurenceType(control.value) !== 'Unknown') {
            return null;
        }
        return {
            reccurence: {
                current: control.value,
                message: 'Reccurence is not valid.'
            }
        }
    }

    private getReccurenceType(cron: string) {
        const cronType = getCronType(cron, 'standard');
        let reccurenceType: string;

        switch (cronType) {
          case CronType.Daily_EveryDays:
          case CronType.Daily_EveryWeekDay:
            reccurenceType = 'Daily';
            break;
          case CronType.Weekly:
            reccurenceType = 'Weekly';
            break;
          case CronType.Monthly_SpecificDay:
          case CronType.Monthly_SpecificWeekDay:
            reccurenceType = 'Monthly';
            break;
          case CronType.Yearly_SpecificMonthDay:
          case CronType.Yearly_SpecificMonthWeek:
            reccurenceType = 'Yearly';
            break;
          default:
            reccurenceType = 'Unknown';
        }
        return reccurenceType;
      }
}

export function reccurenceValidatorFn(): ValidatorFn {
    return (control: AbstractControl) => new ReccurenceValidator().validate(control);
}
