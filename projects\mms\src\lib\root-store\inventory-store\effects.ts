import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, select } from '@ngrx/store';
import { Router } from '@angular/router';
import { of as observableOf, of, pipe, timer } from 'rxjs';
import { switchMap, map, catchError, exhaustMap, tap, concatMap, withLatestFrom, debounce } from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state'
import * as featureSelectors from './selectors'

import { InventoryTypeService } from '../../configuration/type-and-field-definitions/inventory/shared/inventory-type.service';
import { Page } from '../../core/page.service';
import { FormResetAction } from '../../shared/forms/connect-form-ngrx';
import { InventoryType } from '../../configuration/type-and-field-definitions/inventory/shared/inventory-type.model';
import { InventoryTypeWithCostModel } from '../../payroll-and-billing/inventory-cost-lists/inventory-type-with-cost.model';
import { GridStoreActions } from '../shared-store/material-grid-store';

@Injectable()
export class InventoryTypeStoreEffects {
  constructor(
    private router: Router,
    private inventoryTypeService: InventoryTypeService,
    private actions$: Actions,
    private store$: Store<featureState.State>,
    private page: Page
  ) { }

  loadRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(
      featureActions.ActionTypes.LOAD_REQUEST
    ),
    switchMap(action =>
      this.inventoryTypeService
        .getInventoryTypes()
        .pipe(
          map(
            items => {
             return new featureActions.LoadSuccessAction({
                items
              })
            }              
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  saveRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveRequestAction>(
      featureActions.ActionTypes.SAVE_REQUEST
    ),
    withLatestFrom(this.store$.pipe(select(featureSelectors.getSelectedInventoryList))),
    exhaustMap(([action, selectedInventoryList]) => {
      const { inventoryList, ...rest } = action.payload.item;
      return this.inventoryTypeService
        .addInventoryType(rest as InventoryType)
        .pipe(
          map(
            (inventoryType) => {
              return new featureActions.SaveSuccessAction({
                item: inventoryType!
              });
            }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        ) }
    )
  ));

  updateRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateRequestAction>(
      featureActions.ActionTypes.UPDATE_REQUEST
    ),
    exhaustMap((action) =>
      this.inventoryTypeService
        .updateInventoryType(action.payload.item)
        .pipe(
          map(
            () =>
              new featureActions.UpdateSuccessAction({ item: { id: action.payload.item.id!, changes: action.payload.item } })
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  updateRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateSuccessAction>(
      featureActions.ActionTypes.UPDATE_SUCCESS
    ),
    tap(() => {
      this.page.notification.show('Inventory type updated');
    }
    )
  ), { dispatch: false });

  saveRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveSuccessAction>(
      featureActions.ActionTypes.SAVE_SUCCESS
    ),
    map(() => {
      this.page.notification.show('Inventory type added');
      return new FormResetAction();
    }
    )
  ));

  inventoryTypeCanBeDeletedEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.CanBeDeletedAction>(
      featureActions.ActionTypes.CAN_BE_DELETED
    ),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectedInventoryType))),
    exhaustMap(([action, selectedInventory]: [featureActions.CanBeDeletedAction, InventoryType | undefined]) => {
      if (!selectedInventory) {
        return observableOf(new featureActions.NoAction());
      }
      return this.inventoryTypeService
        .canInventoryTypeBeDeleted(selectedInventory.id!)
        .pipe(
          map(
            (response) => {
              if (response) {
                return new featureActions.ConfirmDeleteAction();
              } else {
                this.page.alert.error('In order to delete this inventory type, you need to remove all instances associated with it.', 'Warning');
                return new featureActions.NoAction();
              }
            }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    }
    )
  ), { dispatch: true });

  confirmDeleteEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmDeleteAction>(
      featureActions.ActionTypes.CONFIRM_DELETE
    ),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectedInventoryType))),
    exhaustMap(([action, selectedInventoryType]: [featureActions.ConfirmDeleteAction, InventoryType | undefined]) =>
      this.page
        .confirm.show('Are you sure you want to delete this inventory type?', 'Are you sure?')
        .pipe(
          map(
            (result) => {
              if (result) {
                return new featureActions.DeleteRequestAction();
              }
              return new featureActions.NoAction();
            }
          )
        )
    )
  ), { dispatch: true });

  deleteRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteRequestAction>(
      featureActions.ActionTypes.DELETE_REQUEST
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedInventoryType)),
    ),
    switchMap(([action, selectedInventoryType]: [featureActions.DeleteRequestAction, InventoryType | undefined]) => {
      if (!selectedInventoryType) {
        return observableOf(new featureActions.LoadFailureAction({ error: 'No inventory type selected' }));
      }
      return this.inventoryTypeService
        .deleteInventoryType(selectedInventoryType.id!)
        .pipe(
          map(
            () =>
              new featureActions.DeleteSuccessAction({
                itemId: selectedInventoryType.id!
              })
          )
        )
    }
    )
  ));

  deleteSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteSuccessAction>(
      featureActions.ActionTypes.DELETE_SUCCESS
    ),
    pipe(
      map(() => {
        this.page.notification.show('Inventory type deleted');
      })
    ),
    switchMap(() => [
      new FormResetAction(),
      new featureActions.AddNewAction()
    ])
  ));

  addNewEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AddNewAction>(
      featureActions.ActionTypes.ADD_NEW
    ),
    tap(() => {
      this.router.navigate(['/configuration/types/inventory']);
    })
  ), { dispatch: false });

  saveEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveAction>(
      featureActions.ActionTypes.SAVE
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedInventoryType)),
    ),
    map(([action, selectedInventoryType]: [featureActions.SaveAction, InventoryType | undefined]) => {
      if (selectedInventoryType == null) {
        return new featureActions.SaveRequestAction({ item: action.payload.item })
      } else {
        const inventoryType = action.payload.item;
        inventoryType.id = selectedInventoryType.id;
        return new featureActions.UpdateRequestAction({ item: inventoryType })
      }
    })
  ));

  inventoryListLoadRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(
      featureActions.ActionTypes.INVENTORY_LIST_LOAD_REQUEST
    ),
    switchMap(action =>
      this.inventoryTypeService
        .getInventoryLists()
        .pipe(
          map(
            items => {
             return new featureActions.InventoryListLoadSuccessAction({
                items
              })
            }              
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  inventoryListSaveRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.InventoryListSaveRequestAction>(
      featureActions.ActionTypes.INVENTORY_LIST_SAVE_REQUEST
    ),
    exhaustMap((action) =>
      this.inventoryTypeService
        .addInventoryList(action.payload.item)
        .pipe(
          concatMap(
            (inventoryList) => {
              return [new featureActions.InventoryListSaveSuccessAction({
                item: inventoryList!
              }),
              new featureActions.InventoryListLoadRequestAction(),
            ];
            }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  inventoryListSaveSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveSuccessAction>(
      featureActions.ActionTypes.INVENTORY_LIST_SAVE_SUCCESS
    ),
    map(() => {
      this.page.notification.show('Inventory list added');
      return new FormResetAction();
    }
    )
  ));

  loadByListRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(
      featureActions.ActionTypes.LOAD_INV_TYPES_BY_LIST_REQUEST
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.getSelectedInventoryList)),
    ),
    switchMap(([action, selectedInvList]) => {
      return this.inventoryTypeService
        .getInventoryTypesByInventoryList(selectedInvList && selectedInvList.id)
        .pipe(
          map(
            items => {
              return new featureActions.LoadInvTypesByListSuccessAction({
                items
              })
            }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    }
    )
  ));

  searchByCostListRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SearchByCostListRequest>(
      featureActions.ActionTypes.SEARCH_BY_COST_LIST_REQUEST
    ),
    debounce(() => timer(300)),
    exhaustMap((action) =>
      this.inventoryTypeService.getInventoryTypesByCostList(action.payload.filter)
        .pipe(
            map(
                (data: InventoryTypeWithCostModel[]) => {
                    return new featureActions.SearchByCostListSuccess(data);
                }
            ),
            catchError(error =>
                of(new featureActions.SearchByCostListFailure({ error }))
            )
        )
    )
  ));

  searchByCostListSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SearchByCostListSuccess>(
      featureActions.ActionTypes.SEARCH_BY_COST_LIST_SUCCESS
    ),
    map((action) => {
            return new GridStoreActions.GridLoadItemsSuccessAction(featureState.INVENTORY_TYPES_WITH_COSTS_GRID_ID, { items: action.payload });
    })
  ));

  inventoryCostListLoadRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.InventoryCostListLoadRequestAction>(
      featureActions.ActionTypes.INVENTORY_COST_LIST_LOAD_REQUEST
    ),
    switchMap(action =>
      this.inventoryTypeService
        .getInventoryCostLists()
        .pipe(
          map(
            items => {
              return new featureActions.InventoryCostListLoadSuccessAction({items})
            }              
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  inventoryCostListSaveRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.InventoryCostListSaveRequestAction>(
      featureActions.ActionTypes.INVENTORY_COST_LIST_SAVE_REQUEST
    ),
    exhaustMap((action) =>
      this.inventoryTypeService
        .addInventoryCostList(action.payload.item)
        .pipe(
          concatMap(
            (inventoryCostList) => {
              return [new featureActions.InventoryCostListSaveSuccessAction({item: inventoryCostList!}),
                      new featureActions.InventoryCostListLoadRequestAction()];
            }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  inventoryCostListSaveCopyRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.InventoryCostListSaveCopyRequestAction>(
      featureActions.ActionTypes.INVENTORY_COST_LIST_SAVE_COPY_REQUEST
    ),
    exhaustMap((action) =>
      this.inventoryTypeService
        .copyInventoryCostList(action.payload.currentInventoryCostListId, action.payload.name)
        .pipe(
          concatMap(
            (inventoryCostList) => {
              return [new featureActions.InventoryCostListSaveCopySuccessAction({item: inventoryCostList!}),
                      new featureActions.InventoryCostListLoadRequestAction()];
            }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  inventoryCostListImportRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.InventoryCostListImportRequestAction>(
      featureActions.ActionTypes.INVENTORY_COST_LIST_IMPORT_REQUEST
    ),
    exhaustMap((action) =>
      this.inventoryTypeService
        .importInventoryCostList(action.payload.name, action.payload.data)
        .pipe(
          concatMap(
            (inventoryCostList) => {
              return [new featureActions.InventoryCostListImportSuccessAction({item: inventoryCostList!}),
                      new featureActions.InventoryCostListLoadRequestAction()];
            }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  inventoryCostListSaveSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.InventoryCostListSaveSuccessAction>(
      featureActions.ActionTypes.INVENTORY_COST_LIST_SAVE_SUCCESS
    ),
    map(() => {
      this.page.notification.show('Inventory cost list added');
      return new FormResetAction();
    })
  ));

  inventoryCostListSaveCopySuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.InventoryCostListSaveCopySuccessAction>(
      featureActions.ActionTypes.INVENTORY_COST_LIST_SAVE_COPY_SUCCESS
    ),
    map(() => {
      this.page.notification.show('Copy of Inventory cost list added');
      return new FormResetAction();
    })
  ));

  inventoryCostListImportSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.InventoryCostListImportSuccessAction>(
      featureActions.ActionTypes.INVENTORY_COST_LIST_IMPORT_SUCCESS
    ),
    map(() => {
      this.page.notification.show('Inventory cost list imported');
      return new FormResetAction();
    })
  ));

  setInventoryTypeCostRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SetInventoryTypeCostRequestAction>(
      featureActions.ActionTypes.SET_INVENTORY_TYPE_COST_REQUEST
    ),
    exhaustMap((action) =>
      this.inventoryTypeService
        .setInventoryTypeCost(action.payload.inventoryCostListId, action.payload.inventoryTypeId, action.payload.inventoryTypeCost)
        .pipe(
          concatMap(
            (inventoryType) => {
              return [new featureActions.SetInventoryTypeCostSuccessAction({item: inventoryType!})];
          }),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  setInventoryTypeCostSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SetInventoryTypeCostSuccessAction>(
      featureActions.ActionTypes.SET_INVENTORY_TYPE_COST_SUCCESS
    ),
    map(() => {
      this.page.notification.show('Inventory type updated');
      return new FormResetAction();
    })
  ));

  removeInventoryTypeCostRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.RemoveInventoryTypeCostRequestAction>(
      featureActions.ActionTypes.REMOVE_INVENTORY_TYPE_COST_REQUEST
    ),
    exhaustMap((action) =>
      this.inventoryTypeService
        .removeInventoryTypeCost(action.payload.inventoryCostListId, action.payload.inventoryTypeId)
        .pipe(
          concatMap(
            (inventoryType) => {
              return [new featureActions.RemoveInventoryTypeCostSuccessAction({item: inventoryType!})];
            }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  removeInventoryTypeCostSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.RemoveInventoryTypeCostSuccessAction>(
      featureActions.ActionTypes.REMOVE_INVENTORY_TYPE_COST_SUCCESS
    ),
    map(() => {
      this.page.notification.show('Inventory type updated');
      return new FormResetAction();
    })
  ));

  loadByCostListRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadInvTypesByCostListRequestAction>(
      featureActions.ActionTypes.LOAD_INV_TYPES_BY_COST_LIST_REQUEST
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.getSelectedInventoryCostList)),
    ),
    switchMap(([action, selectedInvCostList]) => {
      return this.inventoryTypeService
        .getInventoryTypesByInventoryCostList(selectedInvCostList && selectedInvCostList.id)
        .pipe(
          map(
            items => {
              return new featureActions.LoadInvTypesByCostListSuccessAction({items})
            }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    })
  ));
}
