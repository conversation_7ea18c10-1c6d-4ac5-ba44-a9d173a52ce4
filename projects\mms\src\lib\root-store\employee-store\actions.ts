import { Action } from '@ngrx/store';
import { User } from '../../configuration/shared/user-and-groups/user.model';
import { Employee } from '../../configuration/employees/models/employee.model';
import { EmployeeConfiguration } from '../../configuration/employees/models/employee-configuration..model';
import { EmployeeShift } from '../../configuration/employees/models/employee-shift.model';

export enum ActionTypes {
    LOAD_REQUEST = '[Employee] Load Request',
    LOAD_FAILURE = '[Employee] Load Failure',
    LOAD_SUCCESS = '[Employee] Load Success',
    SAVE_EMPLOYEE_CONFIGURATION = '[Employee] Save Employee Configuration',
    SAVE_REQUEST = '[Employee] Save Request',
    SAVE_SUCCESS = '[Employee] Save Success',
    UPDATE_REQUEST = '[Employee] Update Request',
    UPDATE_SUCCESS = '[Employee] Update Success',
    ACTIVATE_SEARCH = '[Employee] Activate Search',
    DEACTIVATE_SEARCH = '[Employee] Deactivate Search',
    SEARCH_TEXT_CHANGED = '[Employee] Search Text Changed',
    LOAD_EMPLOYEE_REQUEST = '[Employee] Load Employee Request',
    LOAD_EMPLOYEE_SUCCESS = '[Employee] Load Employee Success',
    ADD_EMPLOYEE_CONFIG = '[Employee] Add employee config',
    LOAD_EMPLOYEES_SHIFTS_REQUEST = '[Employee] Load Employees Shifts Request',
    LOAD_EMPLOYEES_SHIFTS_SUCCESS = '[Employee] Load Employees Shifts Success',
    SAVE_EMPLOYEE_SHIFT_REQUEST = '[Employee] Save Employee Shift Request',
    SAVE_EMPLOYEE_SHIFT_SUCCESS = '[Employee] Save Employee Shift Success',
    EMPLOYEE_SHIFT_SEARCH_TEXT_CHANGED = '[Employee] Employee Shift Search Text Changed',
    LOAD_USERS_FOR_EMPLOYEE_CONFIGURATION_REQUEST = '[Users] Load Users For Employee Configuration Request',
    LOAD_USERS_FOR_EMPLOYEE_CONFIGURATION_IDENTITY_USER_REQUEST = '[Users] Load Users For Employee Configuration Identity User Request',
    LOAD_USERS_FOR_EMPLOYEE_CONFIGURATION_REQUEST_SUCCESS = '[Users] Load Users For Employee Configuration Request Success',
    CREATE_MULTIPLE_EMPLOYEES_REQUEST = '[Employee] Create Multiple Employees Request',
    NO_ACTION = '[Employee] No Action'
}

export class LoadRequestAction implements Action {
    readonly type = ActionTypes.LOAD_REQUEST;
}

export class LoadFailureAction implements Action {
    readonly type = ActionTypes.LOAD_FAILURE;
    constructor(public payload: { error: string }) { }
}

export class LoadSuccessAction implements Action {
    readonly type = ActionTypes.LOAD_SUCCESS;
    constructor(public payload: { users: Array<User> }) { }
}

export class SaveEmployeeConfigurationAction implements Action {
    readonly type = ActionTypes.SAVE_EMPLOYEE_CONFIGURATION;
    constructor(public payload: { items: Array<EmployeeConfiguration>, ticketAssignType: number, employeeRef: string, defaultTaskAssign?: any, defaultCaller?: any }) { }
}

export class SaveRequestAction implements Action {
    readonly type = ActionTypes.SAVE_REQUEST;
    constructor(public payload: { item: Employee }) { }
}

export class SaveSuccessAction implements Action {
    readonly type = ActionTypes.SAVE_SUCCESS;
    constructor(public payload: { item: Employee }) { }
}

export class UpdateRequestAction implements Action {
    readonly type = ActionTypes.UPDATE_REQUEST;
    constructor(public payload: { item: Employee }) { }
}

export class UpdateSuccessAction implements Action {
    readonly type = ActionTypes.UPDATE_SUCCESS;
    constructor(public payload: { item: Employee }) { }
}

export class ActivateSearchAction implements Action {
    readonly type = ActionTypes.ACTIVATE_SEARCH;
}

export class DeactivateSearchAction implements Action {
    readonly type = ActionTypes.DEACTIVATE_SEARCH;
}

export class SearchTextChangedAction implements Action {
    readonly type = ActionTypes.SEARCH_TEXT_CHANGED;
    constructor(public payload: string) { }
}

export class LoadEmployeeRequestAction implements Action {
    readonly type = ActionTypes.LOAD_EMPLOYEE_REQUEST;
    constructor(public idMMS?: string) { }
}

export class LoadEmployeeSuccessAction implements Action {
    readonly type = ActionTypes.LOAD_EMPLOYEE_SUCCESS;
    constructor(public payload: { employee: Employee }) { }
}

export class NoAction implements Action {
    readonly type = ActionTypes.NO_ACTION;
}

export class LoadEmployeesShiftsRequest implements Action {
    readonly type = ActionTypes.LOAD_EMPLOYEES_SHIFTS_REQUEST;
}

export class LoadEmployeesShiftsSuccess implements Action {
    readonly type = ActionTypes.LOAD_EMPLOYEES_SHIFTS_SUCCESS;
    constructor(public payload: { employeesShifts: Array<EmployeeShift> }) { }
}

export class SaveEmployeeShiftRequest implements Action {
    readonly type = ActionTypes.SAVE_EMPLOYEE_SHIFT_REQUEST;
    constructor(public payload: { item: EmployeeShift }) { }
}

export class SaveEmployeeShiftSuccess implements Action {
    readonly type = ActionTypes.SAVE_EMPLOYEE_SHIFT_SUCCESS;
    constructor(public payload: { item: EmployeeShift }) { }
}

export class EmployeeShiftSearchTextChangedAction implements Action {
    readonly type = ActionTypes.EMPLOYEE_SHIFT_SEARCH_TEXT_CHANGED;
    constructor(public payload: string) { }
}

export class LoadUsersForEmployeeConfiguration implements Action {
    readonly type = ActionTypes.LOAD_USERS_FOR_EMPLOYEE_CONFIGURATION_REQUEST;
    constructor(public payload: { userId: string, idMMS?: string }) {}
}

export class LoadUsersForEmployeeConfigurationWithIdentityUser implements Action {
    readonly type = ActionTypes.LOAD_USERS_FOR_EMPLOYEE_CONFIGURATION_IDENTITY_USER_REQUEST;
    constructor(public payload: { userId: string, isTech: boolean }) {}
}

export class LoadUsersForEmployeeConfigurationSuccess implements Action {
    readonly type = ActionTypes.LOAD_USERS_FOR_EMPLOYEE_CONFIGURATION_REQUEST_SUCCESS;
    constructor(public payload: { users: any}) {}
}

export class CreateMultipleEmployeeRequestAction implements Action {
    readonly type = ActionTypes.CREATE_MULTIPLE_EMPLOYEES_REQUEST;
    constructor() { }
}

export type Actions = ActivateSearchAction
    | DeactivateSearchAction
    | SearchTextChangedAction
    | SaveRequestAction
    | SaveSuccessAction
    | UpdateSuccessAction
    | UpdateRequestAction
    | LoadRequestAction
    | LoadSuccessAction
    | LoadFailureAction
    | LoadEmployeeRequestAction
    | LoadEmployeeSuccessAction
    | LoadEmployeesShiftsRequest
    | LoadEmployeesShiftsSuccess
    | SaveEmployeeShiftRequest
    | SaveEmployeeShiftSuccess
    | EmployeeShiftSearchTextChangedAction
    | LoadUsersForEmployeeConfiguration
    | LoadUsersForEmployeeConfigurationSuccess
    | LoadUsersForEmployeeConfigurationWithIdentityUser
    | CreateMultipleEmployeeRequestAction
    | NoAction;
