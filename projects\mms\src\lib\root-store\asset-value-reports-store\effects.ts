import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import {
  withLatestFrom,
  map,
  switchMap,
  catchError,
  exhaustMap,
  tap,
  concatMap
} from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state';
import * as featureSelectors from './selectors';

import { ReportsService } from '../../reports/reports.service';
import { Router } from '@angular/router';
import { GridStoreActions } from '../shared-store/material-grid-store';

@Injectable()
export class AssetValueReportStoreEffects {
  constructor(
    private actions$: Actions,
    private reportsService: ReportsService,
    private store$: Store<featureState.State>,
    private router: Router
  ) { }

  searchReportRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SearchRequestAction>(featureActions.ActionTypes.SEARCH_REQUEST),
    exhaustMap((action) => {
      return this.reportsService.searchAssetValueReport(action.payload)
        .pipe(
          map(reportData => new featureActions.SearchSuccessAction({ reportData })),
          catchError(error => of(new featureActions.SearchFailureAction({ error })))
        )
    })
  ));

  searchSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SearchSuccessAction>(featureActions.ActionTypes.SEARCH_SUCCESS),
    withLatestFrom(this.store$.select(featureSelectors.selectSelectedFilter)),
    concatMap(([action, _]) => {
      this.router.navigate(['/reports/asset-value-report']);
      return [
        new GridStoreActions.GridLoadItemsSuccessAction(featureState.ASSET_VALUE_REPORT_GRID_ID, { items: action.payload.reportData }),
      ]
    })
  ));

  showFilter$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ShowFilterAction>(featureActions.ActionTypes.SHOW_FILTER),
    tap(() => {
      this.router.navigate(['/reports/asset-value-report-filter']);
    })
  ), { dispatch: false });
}
