﻿import { Validator, ValidationErrors, AbstractControl, FormControl } from '@angular/forms'

export class EmailValidator implements Validator {

    constructor() {
    }

    validate(control: AbstractControl): ValidationErrors | null {
        if (control instanceof FormControl) {
            const value: string = control.value;
            const emailPattern = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
            if (value != null && value.trim() && emailPattern.test(value) === false) {
                return {
                    email: {
                        email: value
                    }
                };
            }
        }
        return null;
    }
}
