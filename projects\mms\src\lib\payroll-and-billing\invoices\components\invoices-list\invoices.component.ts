import { InvoiceReportService } from './../../invoice-report.service';
import { Router } from "@angular/router";
import { Filter, Invoice } from "./../../models/invoice.model";
import { Component, OnInit, ViewChild, OnDestroy } from "@angular/core";
import { Store } from "@ngrx/store";
import { Column } from "../../../../root-store/shared-store/material-grid-store/state";
import { INVOICES_GRID_ID } from "../../../../root-store/invoice-store/state";
import { Formats } from "../../../../core/formats";
import { InvoiceStatus } from "../../models/invoice-status.enum";
import { ProjectStoreActions, RouterStoreSelectors } from "../../../../root-store";
import { MatDialog } from "@angular/material/dialog";
import { Observable, Subscription } from "rxjs";
import { RouterReducerState } from "@ngrx/router-store";
import { RouterStateUrl } from "../../../../root-store/router-store/router-state.serializer";
import {
  InvoiceStoreActions,
  InvoiceStoreSelectors,
  InvoiceStoreState,
} from "../../../../root-store/invoice-store";
import { FormBuilder, FormGroup } from "@angular/forms";
import { MmsDateTimepickerType } from "../../../../shared/forms/controls/date-time-picker/date-time-picker.component";
import { distinctUntilChanged } from "rxjs/operators";
import { Page } from "../../../../core/page.service";
import { Bill } from '../../../billing/models/bill.model';
import { Title } from '@angular/platform-browser';
import { MatDrawer } from '@angular/material/sidenav';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MMSSelectMenuComponent } from '../../../../shared/forms/controls/mms-select-menu/mms-select-menu.component';
import { DateTimePickerComponent } from '../../../../shared/forms/controls/date-time-picker/date-time-picker.component';
import { DateTimePickerToggleComponent } from '../../../../shared/forms/controls/date-time-picker/date-time-picker-toggle.component';
import { ValidationMessageComponent } from '../../../../shared/forms/validation-message/validation-message.component';
import { BlockUIDirective } from '../../../../shared/block-ui/block-ui.directive';
import { GridComponent } from '../../../../shared/grid/grid.component';
import { GridColumnComponent } from '../../../../shared/grid/grid-column.component';

// Use global pdfMake variable to avoid require() and import issues
declare const pdfMake: any;
@Component({
  selector: "app-invoices",
  templateUrl: "./invoices.component.html",
  styleUrls: ["./invoices.component.scss"],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatCheckboxModule,
    MatSidenavModule,
    MatTooltipModule,
    MatSlideToggleModule,
    MMSSelectMenuComponent,
    DateTimePickerComponent,
    DateTimePickerToggleComponent,
    ValidationMessageComponent,
    BlockUIDirective,
    GridComponent,
    GridColumnComponent
  ]
})
export class InvoicesComponent implements OnInit, OnDestroy {
  columns: Column[] = [
    { name: "id", hidden: true },
    { name: "status", hidden: false },
    { name: "agency", hidden: false },
    { name: "project", hidden: false },
    { name: "date", hidden: false },
    { name: "invoiceNo", hidden: false },
    { name: "total", hidden: false },
    { name: "processedDate", hidden: false },
    { name: "actions", hidden: false },
  ];
  routerState$: Observable<RouterReducerState<RouterStateUrl>> = this.store$.select(RouterStoreSelectors.getRouterStoreState);
  isLoading$ = this.store$.select(InvoiceStoreSelectors.selectIsLoading);
  isGridPopulated$ = this.store$.select(
    InvoiceStoreSelectors.selectIsGridPopulated
  );
  initialLoad$ = this.store$.select(InvoiceStoreSelectors.selectInitialLoad);
  agencies$ = this.store$.select(InvoiceStoreSelectors.selectAllAgencies);
  loadedInvoices$ = this.store$.select(InvoiceStoreSelectors.getLoadedInvoices);
  loadedInvoices!: Array<Invoice>;

  gridStoreSelector = InvoiceStoreSelectors.selectGrid;
  id: string = INVOICES_GRID_ID;
  DateFormatEnum = Formats;
  invoiceStatusEnum = InvoiceStatus;
  subscriptions: Subscription[] = [];
  formGroup!: FormGroup;
  mmsDateTimepickerType = MmsDateTimepickerType;
  today = new Date();
  default = { id: "0", name: "All" };
  isInvoiceManager = true;
  dropzoneConfig: any;
   acceptedFiles=".png, .jpg, .pdf, .txt, .doc, .docx, .xlsx, .xlsm, .xlsb, .xltx, .xls";
  @ViewChild("sidenav", { static: true}) sideNav!: MatDrawer;

  constructor(
    private store$: Store<InvoiceStoreState.State>,
    private formBuilder: FormBuilder,
    private page: Page,
    private router: Router,
    private invoiceReportService: InvoiceReportService,
    public dialog: MatDialog,
    private title: Title
  ) { }

  ngOnInit() {
    this.store$.dispatch(new InvoiceStoreActions.FilterInvoices({}));
    this.subscriptions.push(
      this.routerState$.subscribe(
        (data: RouterReducerState<RouterStateUrl>) => {
          if (
            data.state.url.includes("add-invoice") ||
            data.state.url.includes("edit-invoice")
          ) {
            this.isInvoiceManager = false;
            this.sideNav.open();
          } else {
            this.isInvoiceManager = true;
            this.sideNav.close();
          }
        }
      )
    );
    this.subscriptions.push(
      this.loadedInvoices$.subscribe((invoices) => {
        this.loadedInvoices = invoices;
      })
    );

    this.formGroup = this.formBuilder.group({
      fromDate: [null],
      toDate: [null],
      agency: [null],
      showRevisedDeleted: [false],
      showProcessed: [false],
      showSuperseded: [false]
    });

    this.subscriptions.push(
      this.formGroup.valueChanges
        .pipe(distinctUntilChanged())
        .subscribe((value) => {
          var filter = { ...value } as Filter;
          filter.fromDate = filter.fromDate
            ? new Date(filter.fromDate).toISOString()
            : undefined;
          filter.toDate = filter.toDate
            ? new Date(filter.toDate).toISOString()
            : undefined;
          filter.agency = value.agency && value.agency.id;
          if (filter.showProcessed && !filter.fromDate && !filter.toDate) {
            this.formGroup.get("showProcessed")!.setValue(false);
            this.page.alert.warning(
              "The \"Include Processed\" option requires a Start and End Date range. "
            );
          } else {
            this.store$.dispatch(
              new InvoiceStoreActions.FilterInvoices({ filter })
            );
          }
        })
    );
    this.title.setTitle("Invoice Manager");
    this.store$.dispatch(new ProjectStoreActions.LoadAllProjects());
  }

  editInvoice(invoice: Invoice) {
    // Reset view only mode
    this.store$.dispatch(
      new InvoiceStoreActions.SetViewOnlyModeAction({viewOnlyMode: false})
    );
    const editStatuses = new Array<InvoiceStatus>(
      InvoiceStatus.Preparing,
      InvoiceStatus.Ready,
      InvoiceStatus.Processed
    );
    if (invoice.status && editStatuses.includes(invoice.status)) {
      this.store$.dispatch(new InvoiceStoreActions.SetSelectedInvoice(invoice));
      if(invoice.status == InvoiceStatus.Processed) {
        this.store$.dispatch(
          new InvoiceStoreActions.ConfirmViewOnlyModeAction(invoice.id!)
        );
      } else {
        this.store$.dispatch(
          new InvoiceStoreActions.ShowEditInvoiceAction(invoice.id!)
        );
      }
    
    } else if (invoice.status == InvoiceStatus.Superseded) {
      this.store$.dispatch(
        new InvoiceStoreActions.SetViewOnlyModeAction({viewOnlyMode: true})
      );
      this.store$.dispatch(
        new InvoiceStoreActions.ShowEditInvoiceAction(invoice.id!)
      );
    } else {
      this.page.alert.warning("Editing this invoice is not possible.");
    }
  }

  markReadyInvoice(invoice: Invoice) {
    if (invoice.status && ![InvoiceStatus.Ready,InvoiceStatus.Preparing].includes(invoice.status)) {
      this.page.alert.warning(
        "Invoice with current status cannot be marked as Ready or Preparing"
      );
    } else if (!invoice.includedBills || invoice.includedBills.length === 0) {
      this.page.alert.warning(
        "Invoice doesn't have included bills. Marking invoice as ready is not possible."
      );
    } else if (invoice.invoiceNo && invoice.date) {
      var status = invoice.status == InvoiceStatus.Ready ? InvoiceStatus.Preparing : InvoiceStatus.Ready
      invoice.status = status;
      this.store$.dispatch(
        new InvoiceStoreActions.UpdateRequestAction(invoice)
      );
    } else {
      this.page.alert.warning(
        "Invoice doesn't have number and date. Marking invoice as ready or preparing is not possible."
      );
    }
  }

  processInvoices(invoice?: Invoice) {
    var ids = [];
    if (invoice && invoice.status && [InvoiceStatus.Ready, InvoiceStatus.Processed].includes(invoice.status)) {
      let filesIncluded = this.filesIncluded(invoice.includedBills || []);
      if (filesIncluded) {
        this.store$.dispatch(new InvoiceStoreActions.GeneratePDFRequest({invoice}));
      } else {
        this.generatePdf(invoice);
      }
      ids = [invoice.id!];
      this.store$.dispatch(new InvoiceStoreActions.ProcessInvoicesRequest(ids));
    } else if (invoice) {
      this.page.alert.warning(
        "Invoice is not ready. Processing invoice is not possible."
      );
    } else {
      var invoiceIds = new Set(
        this.loadedInvoices
          .filter((x) => x.status == InvoiceStatus.Ready)
          .map((x) => x.id)
      );
      ids = Array.from(invoiceIds).filter((id): id is string => id !== undefined);
      if (ids.length) {
        this.store$.dispatch(
          new InvoiceStoreActions.ProcessInvoicesRequest(ids)
        );
      } else {
        this.page.alert.warning(
          "There are no ready invoices. Processing is not possible."
        );
      }
    }
  }

  filesIncluded(includedBills: Bill[]) {
    let filesIncluded = false;
    includedBills.forEach(bill => {
      bill.lineItems?.forEach((lineItem: any) => {
        if (lineItem.fileAttached == true) {
          filesIncluded = true; 
        }
      })
    })

    return filesIncluded;
  }

  deleteInvoice(id: string, invoice: Invoice) {
    if (invoice.status && [InvoiceStatus.Superseded, InvoiceStatus.Processed].includes(invoice.status)) {
      this.page.alert.warning(
        "Superseded or Processed invoice cannot be deleted."
      );
    } else {
      this.store$.dispatch(new InvoiceStoreActions.ConfirmDeleteInoviceAction({ id, invoice }));
    }
  }

  addInvoice() {
    this.store$.dispatch(new InvoiceStoreActions.SetViewOnlyModeAction({viewOnlyMode: false}));
    this.store$.dispatch(new InvoiceStoreActions.ShowCreateInvoiceAction());
  }

  valueMapper(option: any) {
    return option;
  }

  compareFn(c1: any, c2: any): boolean {
    return c1 && c2 ? c1.id === c2.id : c1 === c2;
  }

  closeSideNav() {
    this.router.navigate(["/payroll-and-billing/invoices"]);
  }

  ngOnDestroy() {
    if (this.subscriptions && this.subscriptions.length) {
      this.subscriptions.forEach((s) => s.unsubscribe());
    }
  }

  generatePdf(invoice: Invoice) {
    const documentDefinition = this.invoiceReportService.getDocumentDefinition(invoice);
     pdfMake.createPdf(documentDefinition).open();
  }

  previewInvoice(invoice: Invoice) {
    if (invoice.status != InvoiceStatus.Superseded && invoice.status != InvoiceStatus.Deleted) {
      if (!invoice.includedBills || invoice.includedBills.length == 0) {
        this.page.alert.warning(
          "Invoice without included bills cannot be previewed."
        );
      } else {
        let filesIncluded = this.filesIncluded(invoice.includedBills || []);
        if (filesIncluded) {
          this.store$.dispatch(new InvoiceStoreActions.GeneratePDFRequest({ invoice: invoice }));
        } else {
          this.generatePdf(invoice);
        }
      }
    } else {
      this.page.alert.warning(
        "Superseded or Deleted invoice cannot be previewed."
      );
    }
  }

  shouldDisableMarkAsReady(invoice: Invoice): boolean {
    if (invoice.status && ![InvoiceStatus.Ready, InvoiceStatus.Preparing].includes(invoice.status)) {
      return true;
    }
    return !(invoice.invoiceNo && invoice.date) || (!invoice.includedBills || invoice.includedBills.length === 0);
  }

}
export class FileAttachment {
  public ticketNo!: string;
  public files!: Array<string>;  
  public convertedImage: any;
}