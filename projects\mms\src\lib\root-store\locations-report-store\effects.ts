import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, Action, select } from '@ngrx/store';
import { Observable, of } from 'rxjs';
import {
  withLatestFrom,
  map,
  catchError,
  exhaustMap,
  switchMap,
  tap
} from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state';
import * as featureSelectors from './selectors';

import { ReportsService } from '../../reports/reports.service';
import { Router } from '@angular/router';
import { IGridStoreEffects } from '../shared-store/material-grid-store/effects';
import { GridStoreActions } from '../shared-store/material-grid-store';
import { LocationsDataModel } from '../../reports/locations/locations-report.model';
import { LocationInstancesService } from '../../configuration/location-instances/shared/location-instances.service';
import { AgencyStoreSelectors } from '../agency-store';

@Injectable()
export class LocationsReportStoreEffects implements IGridStoreEffects {
  constructor(
    private actions$: Actions,
    private reportsService: ReportsService,
    private locationInstancesService: LocationInstancesService,
    private store$: Store<featureState.State>,
    private router: Router
  ) { }

  gridLoadItems(actionType: string): Observable<Action> {
    return this.actions$.pipe(
      ofType<GridStoreActions.GridLoadItemsAction<LocationsDataModel>>(actionType),
      withLatestFrom(this.store$.select(featureSelectors.selectGridItems)),
      map(([action, items]) => {
        return new GridStoreActions.GridLoadItemsSuccessAction(featureState.LOCATIONS_REPORT_GRID_ID, { items: items });
      })
    );
  }

  gridLoadItemsSuccess(actionType: string): Observable<Action> {
    return this.actions$.pipe(
      ofType(actionType),
      map(action => new GridStoreActions.GridNoAction())
    );
  }

  gridLoadItemsFailed(actionType: string): Observable<Action> {
    return this.actions$.pipe(
      ofType(actionType),
      map(action => new GridStoreActions.GridNoAction())
    );
  }

  // initiate the fetch actions for each role
  // tslint:disable-next-line:member-ordering
  gridLoadItems$ = createEffect(() => this.gridLoadItems(
    GridStoreActions.ActionTypes.GRID_LOAD_ITEMS(
      featureState.LOCATIONS_REPORT_GRID_ID
    )
  ));

  // initiate the fetch actions for each role
  // tslint:disable-next-line:member-ordering
  gridLoadItemsSuccess$ = createEffect(() => this.gridLoadItemsSuccess(
    GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_SUCCESS(
      featureState.LOCATIONS_REPORT_GRID_ID
    )
  ));

  // initiate the fetch actions for each role
  // tslint:disable-next-line:member-ordering
  gridLoadItemsFailed$ = createEffect(() => this.gridLoadItemsFailed(
    GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_FAILED(
      featureState.LOCATIONS_REPORT_GRID_ID
    )
  ));

  searchRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SearchRequestAction>(
      featureActions.ActionTypes.SEARCH_REQUEST
    ),
    withLatestFrom(this.store$.select(AgencyStoreSelectors.selectDefaultAgency)),
    exhaustMap(([action, defaultAgency]) => {
      let agency = defaultAgency ? defaultAgency.id : '';
      return this.reportsService.searchLocationsReport({ locationIds: action.payload.locationIds, agencyId: agency })
        .pipe(
          map(
            (reportData: LocationsDataModel[]) => {
              return new featureActions.SearchSuccessAction(reportData);
            }
          ),
          catchError(error =>
            of(new featureActions.SearchFailureAction({ error }))
          )
        )
    })
  ));

  searchSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SearchSuccessAction>(
      featureActions.ActionTypes.SEARCH_SUCCESS
    ),
    map((action) => {
      this.router.navigate(['/reports/locations-report']);
      return new GridStoreActions.GridLoadItemsSuccessAction(featureState.LOCATIONS_REPORT_GRID_ID, { items: action.payload });
    })
  ));

  showFilter$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ShowFilterAction>(
      featureActions.ActionTypes.SHOW_FILTER
    ), 
    tap((action: featureActions.ShowFilterAction) => {
      this.router.navigate(['/reports/locations-filter']);
    })
  ), { dispatch: false });

  getIsLoading$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.CheckIfIsLoading>(
      featureActions.ActionTypes.IS_LOADING
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectLocationsIsLoading)),
    ),
    tap(([action, isLoading]: [featureActions.CheckIfIsLoading, boolean]) => {
      if (isLoading === null) {
        this.router.navigate(['/reports/locations-filter']);
      }
    })
  ), { dispatch: false });

  loadLocationInstancess$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadLocationsRequestAction>(
      featureActions.ActionTypes.LOAD_LOCATION_INSTANCES_REQUEST
    ),
    switchMap((action) =>
      this.locationInstancesService.getAllLocationsByAgency(action.payload.agencyId)
        .pipe(
          map(
            data => new featureActions.LoadLocationsSuccessAction({ locations: data })
          ),
          catchError(error =>
            of(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));
}
