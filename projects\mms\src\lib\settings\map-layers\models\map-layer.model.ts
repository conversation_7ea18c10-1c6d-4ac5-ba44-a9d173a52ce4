import { BubbleLayerOptions, data, HeatMapLayerOptions, ImageLayerOptions, LineLayerOptions, PolygonExtrusionLayerOptions, PolygonLayerOptions, SymbolLayerOptions, TileLayerOptions } from 'azure-maps-control'
import { OgcMapLayerOptions, OgcSublayer } from 'azure-maps-spatial-io'

// MapLayer
export class MapLayer {
    id?: string
    name?: string
    agencyId?: string
    group?: string
    icon?: string
    icons?: object
    layerType?: string
    minZoom?: number
    maxZoom?: number
    defaultZoom?: number
    opacity?: number
    zOrder?: number
    enabled?: boolean
    usePriorityPolling?: boolean
    userLayer?: boolean
    mapSelector?: string
    options?: BubbleLayerOptions
        | HeatMapLayerOptions
        | ImageLayerOptions
        | LineLayerOptions
        | OpenStreetMapLayerOptions
        | PolygonLayerOptions
        | PolygonExtrusionLayerOptions
        | SymbolLayerOptions
        | TileLayerOptions
        | OgcMapLayerOptions;
    url?: string;
    ogcLayers?: OgcSublayer[];
    popupTemplate?: string;
    onClick?: string;
}

export class ListSelection {
    isSelected?: boolean;
    id?: string;
    name?: string;
}
export class OpenStreetMapLayerOptions implements TileLayerOptions {
    /**
     * A bounding box that specifies where tiles are available.
     * When specified, no tiles outside of the bounding box will be requested.
     * Default `[-180, -85.0511, 180, 85.0511]`.
     * @default [-180, -85.0511, 180, 85.0511]
     */
    bounds?: data.BoundingBox;
    /**
     * An integer specifying the minimum zoom level in which tiles are available from the tile source.
     * Default `0`.
     * @default 0
     */
    minSourceZoom?: number;
    /**
     * An integer specifying the maximum zoom level in which tiles are available from the tile source.
     * Default `22`.
     * @default 22
     */
    maxSourceZoom?: number;
    /**
     * An integer value that specifies the width and height dimensions of the map tiles.
     * For a seamless experience, the tile size must be a multiplier of 2.
     * Default `512`.
     * @default 512
     */
    tileSize?: number;
    /**
     * Specifies if the tile systems coordinates uses the Tile Map Services specification,
     * which reverses the Y coordinate axis.
     * Default `false`.
     * @default false
     */
    isTMS?: boolean;
    /**
     * An array of subdomain values to apply to the tile URL.
     */
    subdomains?: string[];
    /**
     * A http/https URL to a TileJSON resource or a tile URL template that uses the following parameters:
     * <p>{x}: X position of the tile. Usually also needs {y} and {z}.</p>
     * <p>{y}: Y position of the tile. Usually also needs {x} and {z}.</p>
     * <p>{z}: Zoom level of the tile. Usually also needs {x} and {y}.</p>
     * <p>{quadkey}: Tile quadKey id based on the Bing Maps tile system naming convention.</p>
     * <p>{bbox-epsg-3857}: A bounding box string with the format {west},{south},{east},{north}
     * in the EPSG 4325 Spacial Reference System.</p>
     * <p>{subdomain}: A placeholder where the subdomain values if specified will be added.</p>
     */
    tileUrl?: string;

    constructor() {
        this.tileSize = 256;
        this.subdomains = ['a', 'b', 'c'];
        this.tileUrl = 'https://{subdomain}.tile.openstreetmap.org/{z}/{x}/{y}.png';
        this.maxSourceZoom = 19.0;
        this.minSourceZoom = 1.0;
    }
}

export const DefaultMapLayer: MapLayer = {
        'id': 'test1',
        'name': 'New Layer',
        'agencyId': 'none',
        'group': 'Background',
        'layerType': 'Tile',
        'icon': 'public',
        'minZoom': 1.0,
        'maxZoom': 20.0,
        'defaultZoom': 0,
        'opacity': 1.0,
        'zOrder': 20,
        'enabled': true,
        'usePriorityPolling': false,
        'userLayer': true,
        'options': {}
}

export const TestMapLayers: Array<MapLayer> = [
    {
        'id': 'test1',
        'name': 'Bing Intersection',
        'agencyId': 'none',
        'group': 'Background',
        'layerType': 'Tile',
        'icon': 'map',
        'minZoom': 19.0,
        'maxZoom': 20.0,
        'defaultZoom': 0,
        'opacity': 1.0,
        'zOrder': 20,
        'enabled': true,
        'usePriorityPolling': false,
        'userLayer': true,
        'options': {}
    },
    {
        'id': 'test2',
        'name': 'Open Street Map',
        'agencyId': 'none',
        'group': 'Background',
        'layerType': 'OpenStreetMap',
        'icon': 'public',
        'minZoom': 1.0,
        'maxZoom': 19.0,
        'defaultZoom': 0,
        'opacity': 1.0,
        'zOrder': 10,
        'enabled': true,
        'usePriorityPolling': false,
        'userLayer': true,
        'options': new OpenStreetMapLayerOptions()
    }
]
