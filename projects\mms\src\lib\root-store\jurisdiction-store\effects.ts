import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Page } from '../../core/page.service';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import { map, catchError, concatMap, switchMap, tap, withLatestFrom, exhaustMap } from 'rxjs/operators';
import * as featureActions from './actions';
import * as featureState from './state';
import { RouterStoreSelectors } from '../router-store';
import { RouterReducerState } from '@ngrx/router-store';
import { RouterStateUrl } from '../router-store/router-state.serializer';
import { FormResetAction } from '../../shared/forms/connect-form-ngrx';
import { JurisdictionService } from '../../configuration/jurisdiction/services/jurisdiction.service';
import { Jurisdiction } from '../../configuration/jurisdiction/models/jurisdiction.model';
import { AgencyStoreSelectors } from '../agency-store';
import { GridStoreActions } from '../shared-store/material-grid-store';
import * as featureSelectors from './selectors';

@Injectable()
export class JurisdictionsStoreEffects {

    loadJurisdictionsRequestEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.LoadJurisdictionsRequestAction>(
                featureActions.ActionTypes.LOAD_JURISDICTIONS_REQUEST
            ),
            withLatestFrom(
                this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
            ),
            switchMap(([_, agency]) => {
                if (!agency) {
                    return of(new GridStoreActions.GridNoAction());
                }

                return this.jurisdictionService.getJurisdictions(agency)
                .pipe(
                    map(jurisdictions => new featureActions.LoadJurisdictionsSuccessAction({
                        jurisdictions
                    })),
                    catchError(error => of(new featureActions.LoadFailureAction({ error })))
                )
            })
        )
    );

    addNewEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.AddNewAction>(
                featureActions.ActionTypes.ADD_NEW
            ),
            tap(() => {
                this.router.navigate(['/configuration/jurisdictions']);
            })
        ),
        { dispatch: false }
    );

    confirmDeleteJurisdictionEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.ConfirmDeleteJurisdictionAction>(
                featureActions.ActionTypes.CONFIRM_DELETE_JURISDICTION
            ),
            exhaustMap((_) =>
                this.page.confirm.show('Are you sure you want to delete this jurisdiction?', 'Are you sure?').pipe(
                    map(
                        (result) => {
                            if (result) {
                                return new featureActions.DeleteJurisdictionRequestAction();
                            }
                            return new featureActions.NoAction();
                        }
                    )
                )
            )
        )
    );

    deleteJurisdictionEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.DeleteJurisdictionRequestAction>(
                featureActions.ActionTypes.DELETE_JURISDICTION_REQUEST
            ),
            withLatestFrom(
                this.store$.select(RouterStoreSelectors.getRouterStoreState)
            ),
            switchMap(([_, routerState]: [featureActions.DeleteJurisdictionRequestAction, RouterReducerState<RouterStateUrl>]) =>
                this.jurisdictionService.deleteJurisdiction(routerState.state.params['id']).pipe(
                    map(
                        () => {
                            return new featureActions.DeleteJurisdictionSuccessAction({ id: routerState.state.params['id'] });
                        }
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
            )
        )
    );

    deleteJurisdictionSuccessEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.DeleteJurisdictionSuccessAction>(
                featureActions.ActionTypes.DELETE_JURISDICTION_SUCCESS
            ),
            concatMap((_) => {
                this.page.notification.show('Jurisdiction deleted');
                return [
                    new FormResetAction(),
                    new featureActions.AddNewAction()
                ];
            })
        )
    );

    saveRequestEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.SaveJurisdictionAction>(
                featureActions.ActionTypes.SAVE_JURISDICTION_REQUEST
            ),
            withLatestFrom(
                this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
            ),
            switchMap(([action, agency]) => {
                if (!agency) {
                    return of(new featureActions.LoadFailureAction({ error: 'No agency selected' }));
                }
                const item = {
                    ...action.payload.jurisdiction,
                    agency: agency
                };
                return this.jurisdictionService.createJurisdiction(item).pipe(
                    map((jurisdiction) => {
                        return new featureActions.SaveJurisdictionSuccessAction({
                            jurisdiction: jurisdiction!
                        });
                    }),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
            })
        )
    );

    saveRequestSuccessEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.SaveJurisdictionSuccessAction>(
                featureActions.ActionTypes.SAVE_JURISDICTION_SUCCESS
            ),
            map(() => {
                this.page.notification.show('Jurisdiction added');
                if(this.router.url.includes('edit-contract')){
                    return new featureActions.NoAction();
                }
                else{
                    return new FormResetAction();
                }
            })
        )
    );

    updateRequestEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.UpdateJurisdictionAction>(
                featureActions.ActionTypes.UPDATE_JURISDICTION_REQUEST
            ),
            withLatestFrom(
                this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
            ),
            switchMap(([action, agency]) => {
                if (!agency) {
                    return of(new featureActions.LoadFailureAction({ error: 'No agency selected' }));
                }
                const item = {
                    ...action.payload.jurisdiction,
                    agency: agency
                };
                return this.jurisdictionService.updateJurisdiction(item).pipe(
                    map(() =>
                        new featureActions.UpdateJurisdictionSuccessAction({ jurisdiction: { id: action.payload.jurisdiction.id!, changes: item } })
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
            })
        )
    );

    updateRequestSuccessEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.UpdateJurisdictionSuccessAction>(
                featureActions.ActionTypes.UPDATE_JURISDICTION_SUCCESS
            ),
            tap(() =>
                this.page.notification.show('Jurisdiction updated')
            )
        ),
        { dispatch: false }
    );

    canBeDeletedEffect$ = createEffect(() =>
        this.actions$.pipe(
            ofType<featureActions.CanBeDeletedAction>(
                featureActions.ActionTypes.CAN_BE_DELETED
            ),
            withLatestFrom(
                this.store$.select(RouterStoreSelectors.getRouterStoreState),
                this.store$.select(featureSelectors.getAllJurisdictions)
            ),
            exhaustMap(([_, routerState, jurisdictions]: [featureActions.CanBeDeletedAction, RouterReducerState<RouterStateUrl>, Jurisdiction[]]) => {
                const jurisdiction = jurisdictions.find(x => x.id === routerState.state.params['id']);
                if (!jurisdiction) {
                    return of(new featureActions.NoAction());
                }
                return this.jurisdictionService.canBeDeleted(jurisdiction.name!).pipe(
                    map((response) => {
                        if (!response) {
                            return new featureActions.ConfirmDeleteJurisdictionAction();
                        } else {
                            this.page.alert.error('In order to delete this jurisdiction, you need to remove all instances associated with it.', 'Warning');
                            return new featureActions.NoAction();
                        }
                    }),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
            })
        )
    );

    constructor(
        private actions$: Actions,
        private page: Page,
        private router: Router,
        private jurisdictionService: JurisdictionService,
        private store$: Store<featureState.State>) { }
}