import { MemoizedSelector, createFeatureSelector, createSelector } from '@ngrx/store';
import { State, MainState } from './state';
import { OutstandingPmDataModel } from '../../reports/outstanding-pm/outstanding-pm-report.model';

export const getError = (state: MainState): any => state.error;
export const getIsLoading = (state: MainState): any => state.isLoading;

export const selectOutstandingPmState: MemoizedSelector<object, State> = createFeatureSelector<State>('outstandingPmReportData');

export const selectOutstandingPmGrid = createSelector(
    selectOutstandingPmState,
    (state) => state.grid
);
export const selectGridItems: MemoizedSelector<object, OutstandingPmDataModel[]> = createSelector(
    selectOutstandingPmState,
    (state): OutstandingPmDataModel[] => state.grid.items
);

export const selectOutstandingPmIsLoading: MemoizedSelector<object, boolean> = createSelector(
    selectOutstandingPmState,
    (state: State): any => state.main.isLoading
);

export const selectIsLoading = createSelector(
    selectOutstandingPmState,
    (state) => state.main.isLoading
);

export const selectIsOutstandingPmGridLoading = createSelector(
    selectOutstandingPmState,
    (state): boolean => state.grid.isLoading
);

export const selectReportData = createSelector(
    selectOutstandingPmState,
    (state): any => {
        return state.main.reportData
    }
);