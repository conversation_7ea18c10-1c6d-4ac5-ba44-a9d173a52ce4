import { Component, ChangeDetectorRef, LOCALE_ID, Inject, Input, ElementRef } from '@angular/core';
import { CalendarWeekViewComponent, CalendarUtils, DateAdapter } from 'angular-calendar';
import { CalendarEvent } from '../../common/calendar-utils';
import { CalendarDayViewData } from '../day-view-data.model';
import { getReccurenceType, trackByEventId } from '../../common/util';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'mms-calendar-day-view',
    templateUrl: './mms-calendar-day-view.componet.html',
    standalone: true,
    imports: [CommonModule]
})
export class MMSCalendarDayViewComponent extends CalendarWeekViewComponent {
    private activeFilterGroup: string | null = null;
    private _calendarDayViewData!: CalendarDayViewData;
    getReccurenceType = getReccurenceType;
    trackByEventId = trackByEventId;
    eventDragEnter = 0;

    get calendarDayViewData() : CalendarDayViewData {
        return this._calendarDayViewData;
    }
    
    @Input()
    set calendarDayViewData(value: CalendarDayViewData) {
        if (value) {
            this._calendarDayViewData = value;
            this.activeFilterGroup = null;
        }
    };

    constructor(
        private cdref: ChangeDetectorRef,
        private util: CalendarUtils,
        @Inject(LOCALE_ID) loc: string,
        private adapter: DateAdapter,
        private elementRef: ElementRef
    ) {
        super(cdref, util, loc, adapter, elementRef);
        // Configure for single day view (day view is now week view with daysInWeek = 1)
        this.daysInWeek = 1;
    }

    dayEventFilterClicked(group: string) {
        if (!this.activeFilterGroup || this.activeFilterGroup !== group) {
            this.activeFilterGroup = group;
            const filteredEvents = this.events.filter(x => x.meta.cronType === group);
            this.refreshViewEvents(filteredEvents);
        } else {
            this.activeFilterGroup = null;
            this.refreshViewEvents(this.events);
        }
    }

    private refreshViewEvents(events: CalendarEvent[]) {
        this.view = this.util.getWeekView({
            events: events,
            viewDate: this.viewDate,
            weekStartsOn: this.weekStartsOn,
            excluded: this.excludeDays,
            precision: 'minutes',
            absolutePositionedEvents: true,
            hourSegments: this.hourSegments,
            dayStart: {
                hour: this.dayStartHour,
                minute: this.dayStartMinute
            },
            dayEnd: {
                hour: this.dayEndHour,
                minute: this.dayEndMinute
            },
            segmentHeight: this.hourSegmentHeight,
            weekendDays: this.weekendDays
        });
        this.emitBeforeViewRenderEvent();
    }

    private emitBeforeViewRenderEvent(): void {
        if (this.view) {
            this.beforeViewRender.emit({
                header: this.days,
                period: this.view.period,
                allDayEventRows: this.view.allDayEventRows || [],
                hourColumns: this.view.hourColumns || []
            });
        }
    }

    override trackByHour = (index: number, hour: any): string => {
        return `hour-${index}`;
    }

    override trackByHourSegment = (index: number, segment: any): string => {
        return `segment-${index}`;
    }

    override eventDropped(dropEvent: any): void {
        // Handle the event drop from mwlDroppable
        const event = dropEvent.dropData?.event;
        if (event) {
            // Extract the date from the drop event or use current viewDate as fallback
            const newStart = dropEvent.dropData?.newStart || this.viewDate;
            const allDay = dropEvent.dropData?.allDay || false;
            
            // Emit or handle the event drop as needed
            console.log('Event dropped:', { event, newStart, allDay });
        }
    }
}
