﻿
import { ValidatorFn, AbstractControl, AsyncValidatorFn } from '@angular/forms'
import { Observable } from 'rxjs';

import { RequiredValidator } from './required.validator'
import { MaxDateValidator } from './max-date.validator'
import { MinDateValidator } from './min-date.validator'
import { MinValidator } from './min.validator'
import { MaxValidator } from './max.validator'
import { EmailValidator } from './email.validator'
import { MaxLengthValidator } from './max-length.validator'
import { MinLengthValidator } from './min-length.validator'
import { NumberValidator } from './number.validator'
import { PatternValidator } from './pattern.validator';
import { RegularExpressionValidator } from './regular-expression.validator';
import { IntegerValidator } from './integer.validator';
import { GreaterThanValidator } from './value-greater-than.validator';
import { LessThanValidator } from './value-less-than.validator';
import { AsyncValidator } from './async.validator';
import { CustomValidator } from './custom.validator';
import { AllowedCharactersValidator } from './allowed-characters.validator';
import { SameAsPatternValidator } from './same-as-pattern.validator';
import { SameAsValidator } from './same-as.validator';
import { LessThanFieldValidator } from './less-than-field.validator';
import { GreaterThanFieldValidator } from './greater-than-field.validator';
import { EmojiValidator } from './emoji.validator';


// @dynamic
export class Validators {
    static number(): ValidatorFn {
        return (c: AbstractControl) =>  new NumberValidator().validate(c);
    }

    static integer(): ValidatorFn {
        return (c: AbstractControl) => new IntegerValidator().validate(c);
    }

    static required(): ValidatorFn {
        return (c: AbstractControl) => new RequiredValidator().validate(c);
    }

    static max(maxValue: number, ): ValidatorFn {
        return (c: AbstractControl) =>  new MaxValidator(maxValue).validate(c);
    }

    static min(minValue: number, ): ValidatorFn {
        return (c: AbstractControl) => new MinValidator(minValue).validate(c);
    }

    static maxLength(maxLength: number, ): ValidatorFn {
        return  (c: AbstractControl) => new MaxLengthValidator(maxLength).validate(c);
    }

    static minLength(minLength: number, ): ValidatorFn {
        return (c: AbstractControl) => new MinLengthValidator(minLength).validate(c);
    }

    static maxDate(maxDate: Date, ): ValidatorFn {
        return  (c: AbstractControl) => new MaxDateValidator(maxDate).validate(c);
    }

    static minDate(minDate: Date, ): ValidatorFn {
        return (c: AbstractControl) => new MinDateValidator(minDate).validate(c);
    }

    static email(): ValidatorFn {
        return (c: AbstractControl) => new EmailValidator().validate(c);
    }

    static emoji(): ValidatorFn {
        return (c: AbstractControl) => new EmojiValidator().validate(c);
    }

    static pattern(pattern: string, ): ValidatorFn {
        return  (c: AbstractControl) =>  new PatternValidator(pattern).validate(c);
    }

    static regularExpression(): ValidatorFn {
        return (c: AbstractControl) => new RegularExpressionValidator().validate(c);
    }

    static lessThan(controlToCompare: AbstractControl, controlToValidateName: string, controlToCompareName: string): ValidatorFn {
        return (controlToValidate: AbstractControl) =>
            new LessThanValidator(controlToCompare, controlToValidateName, controlToCompareName)
            .validate(controlToValidate);
    }

    static lessThanField(controlToCompare: string, controlToValidateName: string, controlToCompareName: string): ValidatorFn {
        return (controlToValidate: AbstractControl) =>
            new LessThanFieldValidator(controlToCompare, controlToValidateName, controlToCompareName)
            .validate(controlToValidate);
    }

    static greaterThan(controlToCompare: AbstractControl, controlToValidateName: string, controlToCompareName: string): ValidatorFn {
        return (controlToValidate: AbstractControl) =>
            new GreaterThanValidator(controlToCompare, controlToValidateName, controlToCompareName)
            .validate(controlToValidate);
    }
    static greaterThanField(controlToCompare: string, controlToValidateName: string, controlToCompareName: string): ValidatorFn {
        return (controlToValidate: AbstractControl) =>
            new GreaterThanFieldValidator(controlToCompare, controlToValidateName, controlToCompareName)
            .validate(controlToValidate);
    }

    static async(func: (value: string) => Observable<boolean>, message: string): AsyncValidatorFn {
        return (controlToValidate: AbstractControl) =>
            new AsyncValidator(func, message).validate(controlToValidate);
    }

    static custom(validatorFn: (control: AbstractControl) => boolean, message: string): ValidatorFn {
        return (c: AbstractControl) => new CustomValidator(validatorFn, message).validate(c);
    }

    static allowedCharacters(allowedCharacters: string[]): ValidatorFn {
        return (c: AbstractControl) => new AllowedCharactersValidator(allowedCharacters).validate(c);
    }

    static sameAsPattern(pattern: string): ValidatorFn {
        return (c: AbstractControl) => new SameAsPatternValidator(pattern).validate(c);
    }

    static sameAs(controlName: string): ValidatorFn {
        return (c: AbstractControl) => new SameAsValidator(controlName).validate(c);
    }
}
