import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Action, Store, select } from '@ngrx/store';
import { of as observableOf, pipe, of } from 'rxjs';
import { catchError, map, switchMap, tap, withLatestFrom, exhaustMap, concatMap } from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state'
import * as featureSelectors from './selectors'

import { Router } from '@angular/router';
import { Page } from '../../core/page.service';
import { FormResetAction } from '../../shared/forms/connect-form-ngrx';
import { AgenciesService } from '../../configuration/agencies/agencies.service';
import { Agency } from '../../configuration/agencies/agency.model';
import { displayMessages } from '../../core/resources/display-messages';
import { RouterStoreSelectors } from '../router-store';
import { RouterReducerState } from '@ngrx/router-store';
import { RouterStateUrl } from '../router-store/router-state.serializer';
import * as projectStoreActions from '../projects-store/actions';
import * as regionsStoreActions from '../region-store/actions';
import * as mastrInventoryStoreActions from '../master-inventory-store/actions';
import * as projectSchedulerStoreActions from '../projects-scheduler-store/actions';
import * as inventoryTypeActions from '../inventory-store/actions';
import * as EmployeeStoreActions from '../employee-store/actions';
import { GridLoadItemsAction, GridLoadItemsSuccessAction } from '../shared-store/material-grid-store/actions';
import { LOCATION_INSTANCES_GRID_ID } from '../location-instance-store/state';
import { MASTER_INVENTORY_GRID_ID } from '../master-inventory-store/state';
import { TicketStoreActions } from '../ticket-store';
import { User } from '../../configuration/shared/user-and-groups/user.model';
import { UserService } from '../../configuration/shared/user-and-groups/users.service';
import { UserIdentity } from '@econolite/identity-client';
import { MatDialog } from '@angular/material/dialog';
import { AgencyModalComponent } from '../../configuration/agencies/agency-modal/agency-modal.component';
import { JurisdictionsStoreActions } from '../jurisdiction-store';
import * as mapStoreActions from '../map-store/actions';
import { ProjectService } from '../../configuration/projects/services/projects.service';
import { PermissionsEnum } from '../../core/auth/permissions.enum';
import { LocationInstancesService } from '../../configuration/location-instances/shared/location-instances.service';
import { LocationAgencyAssignment } from '../../configuration/location-instances/shared/location-agency-assignment.model';
import { ASSIGNED_LOCATIONS_GRID_ID, ASSIGNED_USERS_GRID_ID } from './state';
import { ContractStoreActions } from '../contract-store';
import { PMChecklistStoreActions } from '../pm-checklist-store';
import { LocationTicketStoreActions } from '../location-ticket-store';
import { ProjectStoreActions } from '../projects-store';

@Injectable()
export class AgencyStoreEffects {
  constructor(
    private router: Router,
    private agencyService: AgenciesService,
    private projectService: ProjectService,
    private actions$: Actions,
    private store$: Store<featureState.State>,
    private page: Page,
    private userService: UserService,
    private userIdentity: UserIdentity,
    private locationInstanceService: LocationInstancesService,
    public dialog: MatDialog) { }

  loadRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(
      featureActions.ActionTypes.LOAD_REQUEST
    ),
    switchMap(action =>
      this.agencyService
        .getAgencies()
        .pipe(
          map(
            items =>
              new featureActions.LoadSuccessAction({
                items
              })
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  loadProjectsByAgency$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadProjectsByAgencyAction>(
      featureActions.ActionTypes.LOAD_PROJECTS_BY_AGENCY_REQUEST
    ),
    switchMap((action) => {
      if (action.payload.seeAllAgencies) {
        return this.projectService.getAllProjects()
          .pipe(
            map(items => new featureActions.LoadProjectsByAgencySuccessAction({ dispatchProjects: items })
            ),
            catchError(error =>
              of(new featureActions.LoadFailureAction({ error }))
            )
          )
      } else {
        const agencyId = action.payload.agencyId;
        if (!agencyId) {
          return of(new featureActions.LoadFailureAction({ error: 'Agency ID is required' }));
        }
        return this.projectService.getProjectsByAgency(agencyId)
          .pipe(
            map(items => new featureActions.LoadProjectsByAgencySuccessAction({ dispatchProjects: items })
            ),
            catchError(error =>
              of(new featureActions.LoadFailureAction({ error }))
            )
          )
      }
    })
  ));

  loadSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadSuccessAction>(
      featureActions.ActionTypes.LOAD_SUCCESS
    ),
    map(() => new featureActions.LoadDefaultAgencyRequestAction())
  ));

  saveRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveRequestAction>(
      featureActions.ActionTypes.SAVE_REQUEST
    ),
    exhaustMap((action) =>
      this.agencyService
        .addAgency(action.payload.item)
        .pipe(
          concatMap((agency) => {
            return [
              new featureActions.UpdateAgencyLocations({ 
                agencyId: agency?.id!,
                agencyName: agency?.name!
              }),
              new featureActions.AssignUsers({ agency }),
              new featureActions.SaveSuccessAction({
                item: agency
              }),
              new featureActions.LoadRequestAction()
            ]
          }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  updateRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateRequestAction>(
      featureActions.ActionTypes.UPDATE_REQUEST
    ),
    exhaustMap((action) =>
      this.agencyService
        .updateAgency(action.payload.item)
        .pipe(
          switchMap(
            () => [
              new featureActions.UpdateAgencyLocations({ 
                agencyId: action.payload.item.id!,
                agencyName: action.payload.item.name!,
              }),
              new featureActions.AssignUsers({ agency: action.payload.item }),
              new featureActions.UpdateSuccessAction({ item: { id: action.payload.item.id!, changes: action.payload.item } }),
              new featureActions.LoadRequestAction()]
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
    ));

  updateRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateSuccessAction>(
      featureActions.ActionTypes.UPDATE_SUCCESS
    ),
    tap(() => {
      this.page.notification.show('Agency updated');
    })
  ), { dispatch: false });

  saveRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveSuccessAction>(
      featureActions.ActionTypes.SAVE_SUCCESS
    ),
    map(() => {
      this.page.notification.show('Agency added');
      return new FormResetAction();
    }
    )
  ));

  confirmDeleteAgencyEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmDeleteAgencyAction>(
      featureActions.ActionTypes.CONFIRM_DELETE_AGENCY
    ),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectedAgency))),
    exhaustMap(([action, selectedAgency]: [featureActions.ConfirmDeleteAgencyAction, Agency]) =>
      this.page
        .confirm.show('Are you sure you want to delete this agency?', 'Are you sure?')
        .pipe(
          map(
            (result) => {
              if (result) {
                return new featureActions.DeleteRequestAction();
              }
              return new featureActions.NoAction();
            }
          )
        )
    )
  ));

  deleteRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteRequestAction>(
      featureActions.ActionTypes.DELETE_REQUEST
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedAgency)),
    ),
    switchMap(([action, selectedAgency]: [featureActions.DeleteRequestAction, Agency]) =>
      this.agencyService
        .deleteAgency(selectedAgency.id!)
        .pipe(
          map(
            () =>
              new featureActions.DeleteSuccessAction({
                itemId: selectedAgency.id!
              })
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  deleteSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteSuccessAction>(
      featureActions.ActionTypes.DELETE_SUCCESS
    ),
    pipe(
      map(() => {
        this.page.notification.show('Agency deleted');
      }),
      switchMap(res => [
          new FormResetAction(),
          new featureActions.AddNewAction()
      ])
    )
  ));

  addNewEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AddNewAction>(
      featureActions.ActionTypes.ADD_NEW
    ),
    tap(() => {
      this.router.navigate(['/settings/agencies']);
    })
  ));

  saveEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveAction>(
      featureActions.ActionTypes.SAVE
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedAgency)),
    ),
    map(([action, selectedAgency]: [featureActions.SaveAction, Agency]) => {
      if (selectedAgency == null) {
        return new featureActions.SaveRequestAction({ item: action.payload.item })
      } else {
        const agency = action.payload.item;
        agency.id = selectedAgency.id;
        agency.tenantId = selectedAgency.tenantId;
        return new featureActions.UpdateRequestAction({ item: agency })
      }
    })
    ));

  canBeDeletedEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.CanBeDeletedAction>(
      featureActions.ActionTypes.CAN_BE_DELETED
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedAgency)),
    ),
    switchMap(([_, selectedAgency]: [featureActions.CanBeDeletedAction, Agency]) =>
      this.agencyService.canBeDeleted(selectedAgency.id!).pipe(
        map((response) => {
          if (!response) {
            return new featureActions.ConfirmDeleteAgencyAction();
          } else {
            this.page.alert.warning(displayMessages.exceptions.itemIsAssociated('agency'));
            return new featureActions.CanceledDeleteAgencyAction();
          }
        }),
        catchError(error =>
          of(new featureActions.LoadFailureAction({ error }))
        )
      )
    )
  ));

  changeDefaultAgency$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ChangeDefaultAgencyAction>(
      featureActions.ActionTypes.CHANGE_DEFAULT_AGENCY
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    concatMap(([action, routerState]: [featureActions.ChangeDefaultAgencyAction, RouterReducerState<RouterStateUrl>]) => {
      const user = {
        userName: this.userIdentity.username,
        firstName: this.userIdentity.fullName,
        lastName: this.userIdentity.fullName,
        idMMS: this.userIdentity.id,
        id: this.userIdentity.id
      };

      return this.userService.updateDefaultAgency(user, action.payload.id!).pipe(
        concatMap((response: Agency) => {
          const actions = [];
          actions.push(new featureActions.ChangeDefaultAgencySuccessAction({ item: { id: response.id!, changes: response } }));
          if (routerState && routerState.state && routerState.state.url) {
            this.getUpdateActions(routerState.state.url, actions);
          }
          actions.push(new ProjectStoreActions.ClearGrid({isClear: true}));
          actions.push(new ProjectStoreActions.LoadProjectsRequestAction({}));
          actions.push(new ProjectStoreActions.LoadAllProjects());
          actions.push(new ProjectStoreActions.LoadProjectsAllStatus());
          actions.push(new ProjectStoreActions.ClearGrid({isClear: false}));
          actions.push(new EmployeeStoreActions.CreateMultipleEmployeeRequestAction());
          return actions;
        }),
        catchError(error =>
          of(new featureActions.LoadFailureAction({ error }))
        )
      )
    })
  ));

  assignUsers$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AssignUsers>(
      featureActions.ActionTypes.ASSIGN_USERS
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectUsers)),
    ),
    switchMap(([action, selectedUsers]: [featureActions.AssignUsers, User[]]) => {
      const agency = action.payload.agency;
      if (selectedUsers && selectedUsers.length) {
        selectedUsers.forEach(user => {
          user.assignedAgencies = agency ? [agency] : [];
        })
      }
      return this.userService.assignAgencies(selectedUsers).pipe(
        map((result) => {
          return new featureActions.LoadUsers({ agencyId: agency?.id! })
        }),
        catchError(error => {
          return of(new featureActions.LoadFailureAction({ error }))
        })
      )
    }),
    catchError(error => {
      return of(new featureActions.LoadFailureAction({ error }))
    })
  ));

  loadAssignedUsers$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadUsers>(
      featureActions.ActionTypes.LOAD_USERS
    ),
    switchMap((action: featureActions.LoadUsers) => {
      const agencyId = action.payload.agencyId;
      return this.userService.getUsersByAgency(agencyId).pipe(
        map(users => {
          return new featureActions.LoadUsersSuccess({ users })
        }),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      )
    }),
    catchError(error => of(new featureActions.LoadFailureAction({ error })))
  ));

  loadAssignedUsersEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadAssignedUsers>(
      featureActions.ActionTypes.LOAD_ASSIGNED_USERS
    ),
    exhaustMap(action =>
      this.userService.getUsersByAgency(action.payload.agencyId).pipe(
        map(users => new GridLoadItemsSuccessAction(
          ASSIGNED_USERS_GRID_ID, {
            items: [
              ...users.sort((a, b) => (a.firstname ?? '').localeCompare((b.firstname ?? '')))]
          })
        ),
        catchError(err => of(err))
      )
    )
  ));

  deleteAgencyFromUsers$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteAgencyFromUsers>(
      featureActions.ActionTypes.DELETE_AGENCY_FROM_USERS
    ),
    switchMap((action: featureActions.DeleteAgencyFromUsers) => {
      return this.userService.deleteAgency(action.payload.userIds, action.payload.agencyId).pipe(
        map((result) => {
          return new featureActions.NoAction()
        }),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      )
    }),
    catchError(error => of(new featureActions.LoadFailureAction({ error })))
  ));

  loadDefaultAgencyRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadDefaultAgencyRequestAction>(
      featureActions.ActionTypes.LOAD_DEFAULT_AGENCY_REQUEST
    ),
    withLatestFrom(
      this.store$.select(featureSelectors.selectedAgenciesByUser),
      this.store$.select(featureSelectors.isDefaultAgencyValid)
    ),
    switchMap(([_, agencies, isDefaultAgencyValid]) =>
      this.userService.getUserDefaultAgency(this.userIdentity.username).pipe(
        map(response => {
          const canSeeAllAgencies = this.userIdentity.hasPermission([PermissionsEnum.SeeAllAgencies]);
          const isSysAdmin = this.userIdentity.isSysAdmin;
          
          if (!response.defaultAgencyId || (!isSysAdmin && !canSeeAllAgencies && !isDefaultAgencyValid)) {
            const dialogRef = this.dialog.open(AgencyModalComponent, {
              minWidth: 560,
              maxWidth: 560,
              data: agencies,
              hasBackdrop: true,
              disableClose: true
            });

            return new featureActions.NoAction();
          }

          return new featureActions.LoadDefaultAgencySuccessAction({ agencyId: response.defaultAgencyId });
        }),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      )
    )
  ));

  loadDefaultAgencySuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadDefaultAgencySuccessAction>(
      featureActions.ActionTypes.LOAD_DEFAULT_AGENCY_SUCCESS
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    concatMap(([_, routerState]) => {
      const actions: Action[] = [];
      if (routerState && routerState.state && routerState.state.url) {
        this.getUpdateActions(routerState.state.url, actions);
      }

      return actions;
    })
  ));
  
  saveLoggedUser$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveLoggedUser>(featureActions.ActionTypes.SAVE_LOGGED_USER),
    switchMap( action => {
      return this.userService.getUserByIdMMS(action.payload.userId)
      .pipe(
        map(user => new featureActions.SaveLoggedUserSuccess({ user })),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      ) 
    }),
    catchError(error =>  of(new featureActions.LoadFailureAction({ error })))
  ));

  assignLocationsToAgency$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AssignLocationsToAgency>(
      featureActions.ActionTypes.ASSIGN_LOCATIONS_TO_AGENCY
    ),
    exhaustMap(action => {
      const locationAgencyAssignments: LocationAgencyAssignment[] =
        action.payload.locations.map(location => ({
          locationId: location.id,
          agencyId: action.payload.agencyId,
          agencyName: action.payload.agencyName
        }));

      return this.locationInstanceService
        .assignAgenciesToLocations(locationAgencyAssignments)
        .pipe(
          map(() => new featureActions.AssignLocationsToAgencySuccess()),
          catchError(err => of(err))
        );
    })
  ));

  deleteLocationsFromAgency$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteLocationsFromAgency>(
      featureActions.ActionTypes.DELETE_LOCATIONS_FROM_AGENCY
    ),
    exhaustMap(action =>
      this.locationInstanceService
        .deleteLocationsFromAgency(action.payload.agencyId, action.payload.locationsIds)
        .pipe(
          map(() => new featureActions.DeleteLocationsFromAgencySuccess()),
          catchError(err => of(err))
        )
    )
  ));

  loadLocationsForAgency$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadLocations>(
      featureActions.ActionTypes.LOAD_LOCATIONS
    ),
    exhaustMap(action =>
      this.locationInstanceService
        .getLocationsForAgency(action.payload.agencyId)
        .pipe(
          map(locations => new GridLoadItemsSuccessAction(
            ASSIGNED_LOCATIONS_GRID_ID, {
              items: [...locations.sort((a, b) => a.name.localeCompare(b.name))]
            })
          ),
          catchError(err => of(err))
        )
    )
  ));

  updateAgencyLocations$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateAgencyLocations>(
      featureActions.ActionTypes.UPDATE_AGENCY_LOCATIONS
    ),
    withLatestFrom(this.store$.select(featureSelectors.selectLocationsToAdd)),
    withLatestFrom(this.store$.select(featureSelectors.selectLocationsToRemove)),
    withLatestFrom(this.store$.select(featureSelectors.selectLocationsGridItems)),
    concatMap(([[[action, locationsToAdd], locationsToRemove], assignedLocations]) => {
      const actions = [];
      
      if (locationsToAdd.size > 0) {
        actions.push(new featureActions.AssignLocationsToAgency({
          locations: assignedLocations
            .filter(location => locationsToAdd.has(location.id)),
          agencyId: action.payload.agencyId,
          agencyName: action.payload.agencyName
        }));
      }

      if (locationsToRemove.size > 0) {
        actions.push(new featureActions.DeleteLocationsFromAgency({
          locationsIds: Array.from(locationsToRemove),
          agencyId: action.payload.agencyId
        }));
      }

      actions.push(new featureActions.ClearLocationsToAddAndRemove());

      return actions;
    })
  ));

  clearLocationsForAddAndRemove$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AssignLocationsToAgencySuccess
          | featureActions.DeleteLocationsFromAgencySuccess>(
      featureActions.ActionTypes.ASSIGN_LOCATIONS_TO_AGENCY_SUCCESS,
      featureActions.ActionTypes.DELETE_LOCATIONS_FROM_AGENCY_SUCCESS
    ),
    withLatestFrom(this.store$.select(featureSelectors.selectedAgency)),
    map(([_, selectedAgency]) => 
      selectedAgency ? 
        new featureActions.NoAction() :
        new GridLoadItemsSuccessAction(ASSIGNED_LOCATIONS_GRID_ID, { items: [] })
    )
  ));

  // #region Private Methods

  // Agency dependent data is present on multiple screen. If route matches any of those screens, the data should be updated.
  private getUpdateActions(url: string, actions: any[]) {
    if (url.match(/manage-projects|agencies/g)) {
      actions.push(new projectStoreActions.LoadProjectsRequestAction({ isFilter: true }));
    }
    else if (url.match(/tickets/g)) {
      actions.push(new TicketStoreActions.LoadRequestAction());
      actions.push(new projectStoreActions.LoadProjectsRequestAction({ isFilter: true }));
    }
    else if (url.match(/assets-inventories/g)) {
      actions.push(new GridLoadItemsAction(MASTER_INVENTORY_GRID_ID)); 
      actions.push(new mastrInventoryStoreActions.LoadRequestAction());
    }
    else if (url.match(/inventories/g)) {
      actions.push(new inventoryTypeActions.SetSelectedInevntoryListAction({ item: null }));
      actions.push(new inventoryTypeActions.InventoryListLoadRequestAction());
      actions.push(new inventoryTypeActions.SetSelectedInventoryCostListAction({ item: null }));
      actions.push(new inventoryTypeActions.InventoryCostListLoadRequestAction());
    }
    else if (url.match(/locations/g)) {
      if (url.match(/locations|map/g)) {
        actions.push(new LocationTicketStoreActions.LoadRequestAction());
      }
      actions.push(new GridLoadItemsAction(LOCATION_INSTANCES_GRID_ID));
    }
    else if (url.match(/maintenance|regions/g)) {
      actions.push(new regionsStoreActions.LoadRequestAction());
    }
    else if (url.match(/jurisdictions/g) || url.match(/contracts/g)) {
      actions.push(new JurisdictionsStoreActions.LoadJurisdictionsRequestAction());
      actions.push(new ContractStoreActions.LoadRequestAction());
    }
    else if (url.match(/schedules/g)) {
      actions.push(new projectSchedulerStoreActions.LoadRequestAction());
    }
    else if (url.match(/map/g) || url.match(/dashboard/g)) {
      actions.push(new mapStoreActions.LoadMapAction());
    }
    else if (url.match(/payroll-and-billing|contracts/g)) {
      actions.push(new ContractStoreActions.LoadRequestAction());
    }
    else if (url.match(/maintenance|checklists/g)) {
      actions.push(new PMChecklistStoreActions.LoadRequestAction());
    }
    else if (url.match(/performance-priority-filter/g)) {
      actions.push(new projectStoreActions.LoadProjectsRequestAction({ }));
    }
    else if (url.match(/project-budget-report-filter/g)) {
      actions.push(new projectStoreActions.LoadProjectsRequestAction({ }));
    }
  }
  // #endregion Private Methods
}
