import { <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Component, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatDrawer } from '@angular/material/sidenav';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatMenuModule } from '@angular/material/menu';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { ProjectStoreSelectors, ProjectStoreState, ProjectStoreActions, GridStoreActions } from '../../root-store';
import { Project } from './models/project.model';
import { PermissionsEnum } from '../../core/auth/permissions.enum';
import { Column } from '../../root-store/shared-store/material-grid-store/state';
import { PROJECTS_GRID_ID } from '../../root-store/projects-store/state';
import { Formats } from '../../core/formats';
import { SidebarType } from './enums/sidebar-type.enum';
import { NavigationEnd, Router, Event as NavigationEvent } from '@angular/router';
import { GridComponent } from '../../shared/grid/grid.component';
import { GridColumnComponent } from '../../shared/grid/grid-column.component';
import { GridColumnsFilterComponent } from '../../shared/grid/grid-columns-filter.component';
import { BlockUIDirective } from '../../shared/block-ui/block-ui.directive';
import { IdentityClientModule } from '@econolite/identity-client';
import { CategoryDisplayPipe } from './pipes/category-display.pipe';
import { StatusDisplayPipe } from './pipes/status-display.pipe';
import { TypeDisplayPipe } from './pipes/type-display.pipe';

@Component({
    selector: 'app-projects',
    templateUrl: './projects.component.html',
    styleUrls: ['./projects.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        RouterModule,
        MatIconModule,
        MatButtonModule,
        MatTooltipModule,
        MatSidenavModule,
        MatMenuModule,
        GridComponent,
        GridColumnComponent,
        GridColumnsFilterComponent,
        BlockUIDirective,
        // IdentityClientModule,
        CategoryDisplayPipe,
        StatusDisplayPipe,
        TypeDisplayPipe
    ]
})
export class ProjectsComponent implements OnInit, OnDestroy {

    @ViewChild('sidenav', { static: true }) sideNav!: MatDrawer;
    Permissions = PermissionsEnum;
    DateFormatEnum = Formats;
    SidebarTypes = SidebarType;
    initialLoad$ = this.store$.select(ProjectStoreSelectors.selectInitialLoad);
    isSidebarOpened$ = this.store$.select(ProjectStoreSelectors.getIsSidebarOpened);

    // #region Grid Properties
    gridStoreSelector = ProjectStoreSelectors.selectGrid;
    id: string = PROJECTS_GRID_ID;
    isGridPopulated$ = this.store$.select(ProjectStoreSelectors.selectIsGridPopulated);
    isLoading$ = this.store$.select(ProjectStoreSelectors.selectIsLoading);
    isGridLoading$ = this.store$.select(ProjectStoreSelectors.selectIsGridLoading);

    columns: Column[] = [
        { name: 'name', hidden: false },
        { name: 'description', hidden: false },
        { name: 'category', hidden: false },
        { name: 'type', hidden: false },
        { name: 'status', hidden: false },
        { name: 'startDate', hidden: false },
        { name: 'endDate', hidden: false },
        { name: 'defaultLocations', hidden: false },
        { name: 'defaultUsers', hidden: false },
        { name: 'tasks', hidden: false },
        { name: 'actions', canHide: false }
    ];
    sortByColumns: string[] = ['startDate'];
    // #endregion Grid Properties

    constructor(private title: Title,
        private store$: Store<ProjectStoreState.State>,
        private cdr: ChangeDetectorRef,
        private router: Router) {
        title.setTitle('Project Details');
    }

    ngOnInit() {

        this.store$.dispatch(new GridStoreActions.GridLoadItemsSuccessAction(ProjectStoreState.PROJECTS_GRID_ID, { items: [] }));
        this.store$.dispatch(new ProjectStoreActions.LoadProjectsRequestAction({}));

        this.store$.dispatch(
            new ProjectStoreActions.LoadProjectsRequestAction({})
        );
        this.customHandleCloseSideBar();
        this.store$.dispatch(new ProjectStoreActions.LoadProjectsAllStatus())
    }

    deleteProject(projectId: string) {
        this.store$.dispatch(new ProjectStoreActions.CanBeDeletedAction({ projectId }));
    }

    /**
     * Opens form in sidebar
     * */
    showSidebar(sidebarType: SidebarType, projectId?: string, hasOpenTicket: boolean = false) {
        this.store$.dispatch(new ProjectStoreActions.ShowSidebarAction({ urlSuffix: sidebarType.toString(), projectId, project: new Project(), hasOpenTicket }));
    }

    /**
     * Closes sidebar
     * */
    closeSidebar() {
        this.store$.dispatch(new ProjectStoreActions.BackToProjectsGridAction());
    }

    /**
     * Navigates to tickets screen with ticket details sidebar opened.
     * @param project Selected project
     */
    openTicket(project: Project) {
        if (project.hasOpenTickets) {
            this.store$.dispatch(new ProjectStoreActions.OpenTicketsAction({ projectId: project.id }));
        }
    }

    projectsGridSortingAccessor(data: Project, propertyName: string) {
        switch (propertyName) {
            case 'type':
                return data.type?.name ?? '';
            case 'category':
                return data.type?.category ?? '';
            default:
                if (typeof (data as any)[propertyName] === 'string') {
                    return (data as any)[propertyName].toLocaleLowerCase();
                }
                return (data as any)[propertyName];
        }
    }

    customHandleCloseSideBar() {
        let checkUrlManageProjects = '/projects/manage-projects';
        this.router.events
            .subscribe(
                (event: NavigationEvent) => {
                    if (event instanceof NavigationEnd) {
                        if (event.url.toString() === checkUrlManageProjects) {
                            this.store$.dispatch(new ProjectStoreActions.CloseSidebarAction());
                        }
                    }
                });
    }

    ngOnDestroy(): void {
        this.store$.dispatch(new ProjectStoreActions.SetInitialLoadAction({ initialLoad: true }));
    }

}
