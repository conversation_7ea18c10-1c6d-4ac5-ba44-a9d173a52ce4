import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { Action, Store } from "@ngrx/store";
import * as featureActions from './actions';
import * as featureState from './state'
import * as featureSelectors from './selectors'
import { withLatestFrom, switchMap, concatMap, catchError, map, tap, exhaustMap } from "rxjs/operators";
import { GridStoreActions } from '../shared-store/material-grid-store';
import { forkJoin, of } from "rxjs";
import { AgencyStoreSelectors } from '../agency-store';
import { Filter, Invoice } from '../../payroll-and-billing/invoices/models/invoice.model';
import { Page } from '../../core/page.service';
import { FormResetAction } from '../../shared/forms/connect-form-ngrx';
import { BillingStoreActions } from '../billing-store';
import * as uuid from 'uuid';
import { FileAttachment } from '../../payroll-and-billing/invoices/components/invoices-list/invoices.component';
import { InvoiceTotalType } from './../../payroll-and-billing/invoices/models/invoice-total-type.model';
import { InvoicesService } from './../../payroll-and-billing/invoices/invoices.service';
import { UserIdentity } from '@econolite/identity-client';
import { ContractService } from './../../payroll-and-billing/contracts/contract.service';
import { FileUploadService } from './../../shared/forms/controls/file-upload/file-upload.service';
import { InvoiceReportService } from './../../payroll-and-billing/invoices/invoice-report.service';
import { InvoiceStatus } from '../../payroll-and-billing/invoices/models/invoice-status.enum';
import { econoliteHeaderPicture } from '../../payroll-and-billing/invoices/components/invoice-report/header-picture';

@Injectable()
export class InvoicesStoreEffects {
  constructor(
    private actions$: Actions,
    private store$: Store<featureState.State>,
    public userIdentity: UserIdentity,
    private invoicesService: InvoicesService,
    private page: Page,
    private router: Router,
    private contractService: ContractService,
    private fileService: FileUploadService,
    private invoiceReportService: InvoiceReportService
  ) { }

  loadInvoices$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(featureActions.ActionTypes.LOAD_REQUEST),
    withLatestFrom(
      this.store$.select(featureSelectors.getNextPageToken),
      this.store$.select(featureSelectors.selectSelectedFilter),
    ),
    switchMap(([action, nextPageToken, selectedFilter]) => {
      let filter: Filter | undefined;
      if (action.payload?.filter) {
        filter = action.payload.filter;
      }
      else if (selectedFilter) {
        filter = selectedFilter;
      }

      return this.invoicesService.getInvoices(filter!).pipe(
        concatMap(
          (result) => {
            const actions: Action[] = [
              new featureActions.LoadSuccessAction({ items: result.items! }),
              new GridStoreActions.GridLoadItemsSuccessAction(
                featureState.INVOICES_GRID_ID,
                { items: result.items!, nextPageToken: result.nextPageToken, clearGrid: nextPageToken !== null }
              ),
              new featureActions.SetSelectedFilterAction({ filter })
            ];
            return actions;
          }
        ),
        catchError(error => of(new featureActions.LoadFailureAction({ error: error.toString() })))
      );
    })
  ));

  saveEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveAction>(featureActions.ActionTypes.SAVE),
    withLatestFrom(
      this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
      this.store$.select(featureSelectors.selectSelectedInvoice),
      this.store$.select(featureSelectors.selectIncludedBillsGrid),
      this.store$.select(featureSelectors.selectInvoiceTotalBillsGrid),
    ),
    map(([action, agency, selectedInvoice, includedBills, totals]) => {
      if (selectedInvoice) {
        let oldInvoice: Invoice = {
          ...selectedInvoice
        };
        let invoice: Invoice = {
          ...action.payload
        };
        invoice.id = selectedInvoice.id;
        invoice.tenantId = selectedInvoice.tenantId;
        invoice.includedBills = includedBills.items;
        invoice.totals = totals.items;
        invoice.total = totals.items.find(item => item.type == InvoiceTotalType.InvoiceTotal).total;
        if (selectedInvoice.status == InvoiceStatus.Processed) {
          return new featureActions.ManageProcessedInvoiceRequestAction({ invoice, oldInvoice });
        } else {
          return new featureActions.UpdateRequestAction(invoice);
        }
      } else {
        let invoice: Invoice = {
          ...action.payload
        };
        invoice.id = uuid.v4();
        invoice.date = Date.prototype.fromTimeZone(new Date(), agency?.timezone || '');
        invoice.includedBills = includedBills.items;
        invoice.totals = totals.items;
        invoice.total = totals.items.find(item => item.type == InvoiceTotalType.InvoiceTotal).total;
        return new featureActions.SaveRequestAction(invoice);
      }
    })
  ));

  saveRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveRequestAction>(featureActions.ActionTypes.SAVE_REQUEST),
    withLatestFrom(
      this.store$.select(featureSelectors.selectIncludedBillsGrid),
      this.store$.select(featureSelectors.selectAvailableBillsGrid),
    ),
    exhaustMap(([action, includedBills, availableBills]) =>
      this.invoicesService
        .createInvoice(action.payload)
        .pipe(
          concatMap(
            (invoice) => {
              var actions = [];
              actions.push(new featureActions.SaveSuccessAction(invoice));
              if (invoice.status != InvoiceStatus.Superseded) {
                let includedBillsItems = includedBills.items.map(item => {
                  return { ...item, invoiceId: invoice.id }
                });

                let availableBillsItems = availableBills.items.map(item => {
                  return { ...item, invoiceId: null }
                });
                var items = [...includedBillsItems, ...availableBillsItems];
                actions.push(new BillingStoreActions.UpdateMultipleBills({ items }));
              }

              return actions;
            }
          ),
          catchError(error =>
            of(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  saveRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveSuccessAction>(featureActions.ActionTypes.SAVE_SUCCESS),
    withLatestFrom(this.store$.select(featureSelectors.selectSelectedFilter)),
    map(([_, filter]) => {
      this.page.notification.show('Invoice added');
      return new featureActions.LoadRequestAction({ filter: filter! });
    })
  ));

  filterInvoices$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.FilterInvoices>(featureActions.ActionTypes.FILTER_INVOICES),
    concatMap((action) => {
      if (action.payload?.filter?.agency === "0") {
        action.payload.filter.agency = undefined;
      }
      return [
        new featureActions.LoadRequestAction({ filter: action.payload?.filter }),
        new featureActions.SetSelectedFilterAction({ filter: action.payload?.filter })
      ];
    })
  ));

  processInvoices$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ProcessInvoicesRequest>(featureActions.ActionTypes.PROCESS_INVOICES_REQUEST),
    exhaustMap((action) =>
      this.invoicesService.processInvoices(action.payload).pipe(
        concatMap(() => [
          new featureActions.ProcessInvoicesSuccess(),
          new featureActions.LoadRequestAction({})
        ]),
        catchError(error => of(new featureActions.LoadFailureAction({ error: error.toString() })))
      )
    )
  ));

  deleteRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteRequestAction>(featureActions.ActionTypes.DELETE_REQUEST),
    switchMap((action) =>
      this.invoicesService.deleteInvoice(action.payload.id || '').pipe(
        concatMap(() => {
          const includedBills = action.payload.invoice?.includedBills || [];
          includedBills.forEach(item => {
            item.invoiceId = undefined;
          });
          this.page.notification.show('Invoice deleted');
          return [
            new featureActions.LoadRequestAction({}),
            new BillingStoreActions.UpdateMultipleBills({ items: includedBills })
          ];
        }),
        catchError(error => of(new featureActions.LoadFailureAction({ error: error.toString() })))
      )
    )
  ));

  updateRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateRequestAction>(featureActions.ActionTypes.UPDATE_REQUEST),
    withLatestFrom(
      this.store$.select(featureSelectors.selectIncludedBillsGrid),
      this.store$.select(featureSelectors.selectAvailableBillsGrid),
    ),
    exhaustMap(([action, includedBills, availableBills]) =>
      this.invoicesService
        .updateInvoice(action.payload)
        .pipe(
          concatMap(
            (invoice) => {
              var actions = [];
              actions.push(new featureActions.UpdateSuccessAction(invoice));
              actions.push(new featureActions.LoadRequestAction({}));

              if (invoice.status != InvoiceStatus.Superseded) {
                includedBills.items.forEach(item => {
                  item.invoiceId = invoice.id;
                });
                availableBills.items.forEach(item => {
                  item.invoiceId = null;
                });
                var items = [...includedBills.items, ...availableBills.items];

                actions.push(new BillingStoreActions.UpdateMultipleBills({ items }));

              }

              this.page.notification.show('Invoice updated');
              return actions;
            }
          ),
          catchError(error =>
            of(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  showCreateInvoiceEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ShowCreateInvoiceAction>(featureActions.ActionTypes.SHOW_CREATE_INVOICE),
    tap(() => {
      new FormResetAction();
      this.router.navigate(['/payroll-and-billing/invoices/add-invoice/']);
    })
  ), { dispatch: false });

  showEditInvoiceDialogEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ShowEditInvoiceAction>(featureActions.ActionTypes.SHOW_EDIT_INVOICE),
    tap(action => {
      this.router.navigate([`/payroll-and-billing/invoices/edit-invoice/${action.payload}/`]);
    })
  ), { dispatch: false });

  loadContractEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadContractRequest>(featureActions.ActionTypes.LOAD_CONTRACT_REQUEST),
    switchMap((action) => {
      const contractId = action.payload.id;
      if (!contractId) {
        return of(new featureActions.LoadFailureAction({ error: 'Contract ID is required' }));
      }
      return this.contractService.getContractById(contractId).pipe(
        map(contract => new featureActions.LoadContractSuccess({ contract })),
        catchError(error => of(new featureActions.LoadFailureAction({ error: error.toString() })))
      );
    })
  ));

  generateInvoicePDFRequest$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.GeneratePDFRequest>(featureActions.ActionTypes.GENERATE_PDF_REQUEST),
    switchMap((action) => {
      const agencyId = action.payload.invoice.agency?.id;
      const files: FileAttachment[] = [];
      const includedBills = action.payload.invoice.includedBills || [];
      
      includedBills.forEach(bill => {
        const lineItems = bill.lineItems || [];
        lineItems.forEach(lineItem => {
          if (lineItem.fileAttached && lineItem.files?.length) {
            const fileAttachment: FileAttachment = {
              ticketNo: bill.ticketNo?.toString() || '',
              files: lineItem.files,
              convertedImage: null // Will be populated later
            };
            files.push(fileAttachment);
          }
        });
      });

      if (!agencyId) {
        return of(new featureActions.LoadFailureAction({ error: 'Agency ID is required' }));
      }

      const requests = files.map(file => this.fileService.getFile(agencyId, file.files[0]));

      return forkJoin(requests).pipe(
        map(data => {
          if (data) {
            for (let i = 0; i < data.length; i++) {
              files[i].convertedImage = data[i];
            }
            return new featureActions.GeneratePDFSuccess({ files, invoice: action.payload.invoice });
          }
          return new featureActions.NoAction();
        }),
        catchError(error => of(new featureActions.LoadFailureAction({ error: error.toString() })))
      );
    })
  ));

  generateInvoicePDFSuccess$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.GeneratePDFSuccess>(featureActions.ActionTypes.GENERATE_PDF_SUCCESS),
    map((action) => {
      let invoice = action.payload.invoice;
      let files = action.payload.files;
      const documentDefinition = this.getDocumentDefinition(invoice, files);
      // pdfMake.createPdf(documentDefinition).open(); // SHOULD FIX IT

      return new featureActions.NoAction();
    })
  ));

  confirmDeleteInvoiceEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmDeleteInoviceAction>(featureActions.ActionTypes.CONFIRM_DELETE_INVOICE),
    exhaustMap((action) =>
      this.page
        .confirm.show('Are you sure you want to delete this invoice?', 'Are you sure?')
        .pipe(
          map(
            (result) => {
              if (result) {
                return new featureActions.DeleteRequestAction({ id: action.payload.id, invoice: action.payload.invoice });
              }
              return new featureActions.NoAction();
            }
          )
        )
    )
  ));

  confirmViewOnlyModeEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmViewOnlyModeAction>(featureActions.ActionTypes.CONFIRM_VIEW_ONLY_MODE),
    exhaustMap((action) =>
      this.page.confirm.showWithCustomActions('Please select invoice view mode', 'Invoice View Mode', false, undefined, true, 'Edit', 'View-Only').pipe(
        concatMap((result) => {
          if (result) {
            return [
              new featureActions.SetViewOnlyModeAction({ viewOnlyMode: true }),
              new featureActions.ShowEditInvoiceAction(action.payload)
            ];
          }
          return [new featureActions.ShowEditInvoiceAction(action.payload)];
        })
      )
    )
  ));

  manageProcessedInvoiceRequest$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ManageProcessedInvoiceRequestAction>(featureActions.ActionTypes.MANAGE_PROCESSED_INVOICE_REQUEST),
    concatMap((action) => {
      const actions = [];
      const newInvoice = new Invoice();
      newInvoice.invoiceNo = "";
      newInvoice.status = InvoiceStatus.Preparing;
      newInvoice.includedBills = action.payload.invoice.includedBills ? [...action.payload.invoice.includedBills] : [];
      newInvoice.project = action.payload.invoice.project || undefined;
      newInvoice.projectNo = action.payload.invoice.projectNo;
      newInvoice.remitTo = action.payload.invoice.remitTo;
      newInvoice.status = action.payload.invoice.status;
      newInvoice.terms = action.payload.invoice.terms;
      newInvoice.total = action.payload.invoice.total;
      newInvoice.totals = action.payload.invoice.totals;
      newInvoice.agency = action.payload.invoice.agency;
      newInvoice.authorizedBy = action.payload.invoice.authorizedBy;
      newInvoice.billTo = action.payload.invoice.billTo;
      newInvoice.contractNo = action.payload.invoice.contractNo;
      newInvoice.date = action.payload.invoice.date;
      newInvoice.description = action.payload.invoice.description;
      newInvoice.endDate = action.payload.invoice.endDate;

      const editedInvoice = action.payload.oldInvoice;
      editedInvoice.status = InvoiceStatus.Superseded;
      editedInvoice.includedBills = [];

      actions.push(new featureActions.SaveRequestAction(newInvoice));
      actions.push(new featureActions.UpdateRequestAction(editedInvoice));

      return actions;
    })
  ));

  getDocumentDefinition(invoice: Invoice, files: Array<FileAttachment>) {
    return {
      content: [
        {
          columns: [
            [
              this.invoiceReportService.getHeaderPicObject()
            ],
            [
              {
                style: 'tableExample',
                color: '#444',
                table: {
                  widths: ['auto', 'auto'],
                  headerRows: 2,
                  body: [
                    [{ text: 'Inovice Number:', border: [false, false, false, false], fontSize: 14, bold: true }, { text: invoice.invoiceNo }],
                    [{ text: 'Invoice Date:', style: 'iHeader', border: [false, false, false, false], fontSize: 14, bold: true }, { text: this.invoiceReportService.getFormattedDate(invoice.date) }],
                    [{ text: 'Your Contract/Order #:', style: 'iHeader', border: [false, false, false, false], fontSize: 14, bold: true }, { text: invoice.contractNo }]
                  ]
                }
              }
            ]
          ],
        },
        {
          columns: [
            [
              {
                text: [
                  { text: 'Remit to: \n', style: "iheader" },
                  { text: invoice.remitTo },
                ], style: "headerMargins", margin: [0, 0, 20, 10]
              },
              {
                text: [
                  { text: 'Bill to: \n', style: "iheader" },
                  { text: invoice.billTo, width: 50 },
                ], style: "headerMargins", margin: [0, 0, 20, 10]
              },
              {
                text: [
                  { text: 'Terms: ', style: "iheader", margin: [0, 0, 10, 30] },
                  { text: invoice.terms },
                ], style: "headerMargins", margin: [0, 0, 20, 10]
              },
            ],
            [
              {
                text: [
                  { text: 'Customer:\t\t\t\t', style: "iheader" },
                  { text: invoice.agency?.name || '' },

                ], style: "headerMargins"
              },
              {
                text: [
                  { text: 'Authorized By:\t\t', style: "iheader" },
                  { text: invoice.authorizedBy },
                ], style: "headerMargins"
              },
              {
                text: [
                  { text: 'Our Project #:\t\t', style: "iheader" },
                  { text: invoice.project?.name || '' },
                ], style: "headerMargins"
              },
              {
                text: [
                  { text: 'Ending Date:\t\t\t', style: "iheader" },
                  { text: this.invoiceReportService.getFormattedDate(invoice.endDate) },
                ], style: "headerMargins"
              },
              {
                text: [
                  { text: 'Job Description: \n', style: "iheader" },
                  { text: invoice.description },
                ], style: "headerMargins", fontSize: 10
              },
            ]
          ],
        },
        {
          text: 'If you have any questions, Call (714) 630-3700, Fax (714) 630-7123, <NAME_EMAIL>',
          margin: [0, 10, 0, 10],
          fontSize: 10
        },
        {
          style: 'tableExample',
          color: '#444',
          table: {
            widths: ['auto', 'auto', 'auto', 'auto', 40, 60, 60],
            headerRows: 2,
            // keepWithHeaderRows: 1,
            body: this.invoiceReportService.getLineCountTableBody(invoice)
          }
        },
        {},
        {
          style: 'tableExample2',
          color: '#444',
          table: {
            widths: [200, 'auto', 'auto', 'auto', 'auto'],
            headerRows: 2,
            // keepWithHeaderRows: 1,
            body: this.invoiceReportService.getTotalInvoiceTableBody(invoice)
          }
        },
        {
          columns: [
            [
              {
                text: `Inovice Details`,
                bold: true, fontSize: 16
              },
              {
                text: `${invoice.billTo}`,
                style: 'iHeader'
              },
              {
                text: `Rolling Report ${this.invoiceReportService.getYearAndMontString(invoice.date)}`,
                style: 'iHeader',
                margin: [0, 0, 20, 30]
              }
            ],
            [
              {
                image: 'data:image/png;base64,' + econoliteHeaderPicture,
                width: 200,
                alignment: 'right',
                margin: [0, 0, 0, 0]
              }
            ]
          ],
          style: 'header',
          pageBreak: 'before',
          pageOrientation: 'landscape'
        },
        {
          style: 'tableExample',
          color: '#444',
          table: {
            widths: ['auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto'],
            headerRows: 2,
            // keepWithHeaderRows: 1,
            body: this.invoiceReportService.getInvoiceDetailsTableBody(invoice)
          }
        },
        {
          style: 'tableExample2',
          color: '#444',
          table: {
            widths: [200, 'auto', 'auto', 'auto', 'auto'],
            headerRows: 2,
            // keepWithHeaderRows: 1,
            body: this.invoiceReportService.getTotalInvoiceTableBody(invoice)
          }
        },
        {
          text: "",
          pageBreak: 'before',
          pageOrientation: 'portrait'
        },
        this.invoiceReportService.getImgObjects(files)
      ],
      styles: {
        tableExample: {
          margin: [0, 5, 0, 15],
        },
        tableExample2: {
          margin: [75, 5, 0, 15],
        },
        iheader: {
          fontSize: 10,
          bold: true,
          margin: [0, 0, 30, 10],
        },
        thedaer1: {
          bold: true,
          fillColor: '#d9d9d9',
        },
        thedaer2: {
          bold: true,
          alignment: 'right'
        },
        headerMargins: {
          margin: [0, 0, 0, 10],
          fontSize: 10
        },
      }
    };
  }

}


