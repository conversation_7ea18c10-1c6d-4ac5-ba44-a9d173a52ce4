import { Actions, ActionTypes } from './actions';
import { initialState, MainState, featureAdapter, TICKET_GRID_ID, TICKETS_LIST_DASHBOARD_GRID_ID, State } from './state';
import { combineReducers, ActionReducerMap } from '@ngrx/store';
import { createGridReducer } from '../shared-store/material-grid-store/reducer';


export function mainReducer(state = initialState, action: Actions): MainState {

    switch (action.type) {
        case ActionTypes.LOAD_REQUEST: {
            return {
                ...state,
                isLoading: true
            }
        }
        case ActionTypes.LOAD_SUCCESS: {
            return {
                ...state,
                isLoading: false,
                showAuxData: action.payload.mmsSettings.auxDataVisibility,
                showTag: action.payload.mmsSettings.tagsVisibility
            };
        }

        case ActionTypes.LOAD_FAILURE : {
            return {
                ...state,
                isLoading: false
            };
        }
        case ActionTypes.LOAD_SIDEBAR_REQUEST: {
            return {
                ...state,
                isSidebarLoading: true
            }
        }
        
        case ActionTypes.LOAD_SIDEBAR_SUCCESS: {
            return {
                ...state,
                users: [...action.payload.users],
                isSidebarLoading: false
            };
        }

        case ActionTypes.LOAD_SIDEBAR_FAILURE: {
            return {
                ...state,
                isSidebarLoading: false
            };
        }

        case ActionTypes.SHOW_CREATE_TICKET: {
            return {
                ...state,
                ticketFormData: null
            };
        }
        case ActionTypes.SHOW_CREATE_CALL_TICKET: {
            return {
                ...state,
                ticketFormData: null
            };
        }
        
        case ActionTypes.SHOW_EDIT_TICKET: {
            return {
                ...state,
                returnPath: '',
                ticketFormData: null
            };
        }
        case ActionTypes.SAVE_MULTIPLE_REQUEST: {
            return {
                ...state,
                isLoading: true,
                isSidebarLoading: true
            };
        }
        case ActionTypes.SAVE_REQUEST: {
            return {
                ...state,
                isLoading: true
            };
        }
        case ActionTypes.SAVE_SUCCESS: {
            return featureAdapter.addOne(action.payload, {
                ...state,
                isLoading: false
            });
        }
        case ActionTypes.SAVE_MULTIPLE_SUCCESS: {
            return featureAdapter.addMany(action.payload, {
                ...state,
                isLoading: false,
                isSidebarLoading: false
            });
        }
        case ActionTypes.UPDATE_REQUEST: {
            return {
                ...state,
                isLoading: true
            };
        }
        case ActionTypes.UPDATE_SUCCESS: {
            return featureAdapter.updateOne(action.payload, {
                ...state,
                isLoading: false,
                ticketFormData: null
            });
        }

        case ActionTypes.DELETE_REQUEST: {
            return {
                ...state,
                isLoading: true,
            };
        }
        case ActionTypes.DELETE_SUCCESS: {
            if (!action.payload.ticketId) {
                return state;
            }
            return featureAdapter.removeOne(action.payload.ticketId, {
                ...state,
                isLoading: false,
            });
        }
        case ActionTypes.ADD_NEW: {
            return {
                ...state,
                searchActive: false,
                searchText: null
            };
        }
        case ActionTypes.DISPATCH_TICKET_REQUEST: {
            return {
                ...state,
                isSidebarLoading: true
            }
        }
        case ActionTypes.DISPATCH_TICKET_SUCCESS: {
            return featureAdapter.updateOne(action.payload, {
                ...state,
                isSidebarLoading: false
            });
        }

        case ActionTypes.DISPATCH_TICKET_FAILURE : {
            return {
                ...state,
                isSidebarLoading: false
            };
        }
        case ActionTypes.SAVE_CALLER_SUCCESS: {
            var formData = state.ticketFormData;
            if(formData) formData.caller = action.payload.caller;
            return {
                ...state,
               ticketFormData: formData 
             }
        }
        case ActionTypes.ADD_NEW_CALLER: {
            return {
                ...state,
               ticketFormData: action.payload.ticketFormData
             }
        }
        case ActionTypes.APPLY_FILTER: {
            return {
                ...state,
                selectedFilter: action.payload
            };
        }
        case ActionTypes.RESET_FILTER: {
            return {
                ...state,
                selectedFilter: null
            };
        }
        case ActionTypes.SEARCH_REQUEST: {
            return {
                ...state,
                isSidebarLoading: true
            };
        }
        case ActionTypes.SEARCH_SUCCESS: {
            return featureAdapter.setAll(action.payload.tickets, {
                ...state,
                isSidebarLoading: false
            });
        }
        case ActionTypes.SEARCH_FAILURE: {
            return {
                ...state,
                isSidebarLoading: false
            };
        }
        case ActionTypes.GET_CALLER_DETAILS: {
            return {
                ...state,
                isSidebarLoading: false,
                ticketFormData: action.payload.ticketFormData
            };
        }
        case ActionTypes.SHOW_CALLER_DETAILS: {
            return  {
                ...state,
                isSidebarLoading: false,
                callerDetails: action.payload.callHistory
            };
            
        }
        case ActionTypes.CLOSE_SIDEBAR: {
            return {
                ...state,
                isLoading: false,
                isSidebarLoading: false,
                selectedTicketProject: null
            };
        }
        case ActionTypes.LOAD_TICKET_USER_DETAILS:
        case ActionTypes.LOAD_TICKET_DETAILS: {
            return {
                ...state,
                isSidebarLoading: true
            }
        }

        case ActionTypes.LOAD_TICKET_DETAILS_SUCCESS: {
            return  {
                ...state,
                isSidebarLoading: false
            };
        }       
       
        case ActionTypes.SAVE_PATH: {
            return {
                ...state,
                returnPath: action.payload.path,
            };
        }

        case ActionTypes.TICKET_COUNT_SUCCESS: {
            return {
                ...state,
                ticketNumber: action.payload.ticketCount
            };
        }

        case ActionTypes.NO_ACTION: {
            return {
                ...state,
                isSidebarLoading: false,
                isLoading: false
            };
        }

        case ActionTypes.TICEKT_CLEAR_CHANGES: {
            return {
                ...state,
                ticketFormData: null
            };
        }

        case ActionTypes.TICKETS_ON_LOCATION_SUCCESS: {
            return {
                ...state,
                isSidebarLoading: false,
                isLoading: false,
                locationTickets: action.payload.tickets
                
            };
        }
        case ActionTypes.GET_TICKET_DETAILS_SUCCESS: {
            return {
                ...state,
                locationTickets: [action.payload.ticket]
            };
        }
        case ActionTypes.SET_INITIAL_LOAD: {
            return {
                ...state,
               initialLoad: false
            };
        }
        case ActionTypes.SET_SELECTED_TICKET_PROJECT: {
            return {
                ...state,
               selectedTicketProject: action.payload.project
            };
        }
        case ActionTypes.SET_SELECTED_TICKET_PROJECT_SUCCESS: {
            return {
                ...state,
               filteredTypesByProject: action.payload.filteredTypes,
               selectedTicketProject: action.payload.project,
               selectedContract: action.payload.contract || null
            };
        }
        case ActionTypes.GET_LATEST_TASK_NUMBER_SUCCESS: {
            return {
                ...state,
                latestTicketNo: action.payload.ticketNo
            } 
        }
        case ActionTypes.ACTIVATE_SEARCH: {
            return {
                ...state,
                searchActive: true,
                searchText: ''
            };
        }
        case ActionTypes.DEACTIVATE_SEARCH: {
            return {
                ...state,
                searchActive: false,
                searchText: ''
            };
        }
        case ActionTypes.SEARCH_TEXT_CHANGED: {
            return {
                ...state,
                searchText: action.payload
            }
        }
        case ActionTypes.QUICK_FILTER_CHANGED: {
            return {
                ...state,
                quickFilter: action.payload.quickFilter
            }
        }
        case ActionTypes.LOAD_SUMMARY_STATISTIC_SUCCESS: {
            return {
                ...state,
                summaryStatistic: action.payload.summaryStatistic
            }
        }
        case ActionTypes.LOAD_AVERAGE_DAILY_RESPONSE_SUCCESS: {
            return {
                ...state,
                averageDailyResponseChartData: action.payload.averageResponseTimeChartData
            }
        }
        case ActionTypes.LOAD_AVERAGE_WEEKLY_RESPONSE_SUCCESS: {
            return {
                ...state,
                averageWeeklyResponseChartData: action.payload.averageResponseTimeChartData
            }
        }
        case ActionTypes.LOAD_AVERAGE_MONTHLY_RESPONSE_SUCCESS: {
            return {
                ...state,
                averageMonthlyResponseChartData: action.payload.averageResponseTimeChartData
            }
        }
        case ActionTypes.ACTIVATE_TICKET_DASHBOARD_SEARCH: {
            return {
                ...state,
                ticketDashboardSearchActive: true,
                ticketDashboardSearchText: ''
            };
        }
        case ActionTypes.DEACTIVATE_TICKET_DASHBOARD_SEARCH: {
            return {
                ...state,
                ticketDashboardSearchActive: false,
                ticketDashboardSearchText: ''
            };
        }
        case ActionTypes.TICKET_DASHBOARD_SEARCH_CHANGE: {
            return {
                ...state,
                ticketDashboardSearchText: action.payload
            }
        }
        case ActionTypes.LOAD_ALLTICKETS_REQUEST: {
            return {
                ...state,
                isLoading: true
            }
        }
        case ActionTypes.LOAD_ALLTICKETS_SUCCESS: {
            return {
                ...state,
                isLoading: false,
                allTickets: action.payload.tickets
            }
        }
        case ActionTypes.GET_TICKET: {
            return {
                ...state,
               isLoading: true,
             }
        }
        case ActionTypes.GET_TICKET_SUCCESS: {
            return {
                ...state,
               selectedOneTicket: action.payload.ticket,
               isLoading: false,
             }
        }
        default: {
            return state;
        }
    }
}

export const reducers: ActionReducerMap<State, Actions> = {
    main: mainReducer,
    grid: createGridReducer(TICKET_GRID_ID),
    ticketListDashboardGrid: createGridReducer(TICKETS_LIST_DASHBOARD_GRID_ID)
};

const metaReducer = combineReducers(reducers);

export function featureReducer(state: State | undefined, action: Actions): State {
    return metaReducer(state, action);
}
