﻿import { Validator, AbstractControl, ValidationErrors, FormControl } from '@angular/forms'

export class MinDateValidator implements Validator {

    constructor(private minDate: Date) {
    }

    validate(control: AbstractControl): ValidationErrors | null {
        if (control instanceof FormControl) {
            if (control.value == null) {
                return null;
            }
            const date = new Date(control.value);
            if (date >= this.minDate) {
                return null;
            }

            return {
                minDate: {
                    currentDate: control.value,
                    minDate: this.minDate
                }
            }
        }
        return null;
    }
}
