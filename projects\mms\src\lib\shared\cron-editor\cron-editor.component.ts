import { Component, Input, Output, OnInit, OnChanges, SimpleChanges, EventEmitter, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { CronOptions } from './CronOptions';
import { Days, MonthWeeks, Months } from './enums';
import { MatTabChangeEvent, MatTabGroup, MatTabsModule } from '@angular/material/tabs';
import { getCronType, parseCron } from './cron-editor.utill';
import { CronType } from './cron-type.enum';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TimePickerComponent } from './cron-time-picker.component';

@Component({
    selector: 'mms-cron-editor',
    templateUrl: './cron-editor.template.html',
    styleUrls: ['./cron-editor.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        MatTabsModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        TimePickerComponent
    ]
})
export class CronGenComponent implements OnInit, OnChanges {
    @Input() public disabled = false;
    @Input() public options: CronOptions = {
        formInputClass: '',
        formSelectClass: '',
        formRadioClass: '',
        formCheckboxClass: '',
        defaultTime: '00:00:00',
        hideMinutesTab: false,
        hideHourlyTab: false,
        hideDailyTab: false,
        hideWeeklyTab: false,
        hideMonthlyTab: false,
        hideYearlyTab: false,
        hideAdvancedTab: false,
        hideSpecificWeekDayTab: false,
        hideSpecificMonthWeekTab: false,
        use24HourTime: true,
        hideSeconds: false,
        cronFlavor: 'standard',
        hideTimeFields: false
    };

    private _startDate = '';
    @Input() get startDate(): string { return this._startDate; }
    set startDate(value: string) {
        this._startDate = value;
        if (value && this.activeTab) {
            this.regenerateCron();
        }
    }

    @ViewChild('matTabGroup', { static: false }) matTabGroup!: MatTabGroup;

    @Input() get cron(): string { return this.localCron; }
    set cron(value: string) {
        this.localCron = value;
        this.cronChange.emit(this.localCron);
    }

    @Output() cronChange = new EventEmitter();
    @Output() tabChange = new EventEmitter();

    private _activeTab = '';
    public set activeTab(value: string) {
        this._activeTab = value;
    }
    public get activeTab() {
        return this._activeTab;
    }

    public activeTabIndex = 0;
    public selectOptions = this.getSelectOptions();
    public state: any;

    private localCron = '';
    private isDirty = false;

    private cronEditorTabIndex: Record<string, number | null> = {
        'minutes': null,
        'hourly': null,
        'daily': 0,
        'weekly': 1,
        'monthly': 2,
        'yearly': 3,
        'advanced': null
    };

    get isCronFlavorQuartz() { return this.options.cronFlavor === 'quartz'; }
    get isCronFlavorStandard() { return this.options.cronFlavor === 'standard'; }

    get yearDefaultChar() { return this.options.cronFlavor === 'quartz' ? '*' : ''; }
    get weekDayDefaultChar() { return this.options.cronFlavor === 'quartz' ? '?' : '*'; }
    get monthDayDefaultChar() { return this.options.cronFlavor === 'quartz' ? '?' : '*'; }

    public async ngOnInit() {
        this.state = this.getDefaultState();

        this.handleModelChange(this.cron);
    }

    public async ngOnChanges(changes: SimpleChanges) {
        const newCron = changes['cron'];
        if (newCron && !newCron.firstChange) {
            this.handleModelChange(this.cron);
        }
    }

    public setActiveTab(matTabChangeEvent: MatTabChangeEvent) {
        if (!this.disabled) {
            this.activeTab = matTabChangeEvent.tab.textLabel.toLowerCase();
            this.regenerateCron();
            this.tabChange.emit(this.activeTab);
        }
    }

    public onClear(cron: string) {
        this.state = this.getDefaultState();
        if (cron) {
            this.cron = cron;
            this.handleModelChange(this.cron);
        }
    }

    public dayDisplay(day: keyof typeof Days): string {
        return Days[day];
    }

    public monthWeekDisplay(monthWeekNumber: keyof typeof MonthWeeks): string {
        return MonthWeeks[monthWeekNumber];
    }

    public monthDisplay(month: number): string {
        return Months[month];
    }

    public monthDayDisplay(month: string): string {
        if (month === 'L') {
            return 'Last Day';
        } else if (month === 'LW') {
            return 'Last Weekday';
        } else if (month === '1W') {
            return 'First Weekday';
        } else {
            return `${month}${this.getOrdinalSuffix(month)} day`;
        }
    }

    public regenerateCron() {
        this.isDirty = true;
        switch (this.activeTab) {
            case 'minutes':
                this.cron = `${this.isCronFlavorQuartz ? this.state.minutes.seconds : ''} 0/${this.state.minutes.minutes} * 1/1 * ${this.weekDayDefaultChar} ${this.yearDefaultChar}`.trim();
                break;
            case 'hourly':
                this.cron = `${this.isCronFlavorQuartz ? this.state.hourly.seconds : ''} ${this.state.hourly.minutes} 0/${this.state.hourly.hours} 1/1 * ${this.weekDayDefaultChar} ${this.yearDefaultChar}`.trim();
                break;
            case 'daily':
                switch (this.state.daily.subTab) {
                    case 'everyDays':
                        const day = this.startDate ? new Date(this.startDate).getDate() : '1';
                        // tslint:disable-next-line:max-line-length
                        setTimeout(() => {
                            this.cron = `${this.isCronFlavorQuartz ? this.state.daily.everyDays.seconds : ''} ${this.state.daily.everyDays.minutes} ${this.hourToCron(this.state.daily.everyDays.hours, this.state.daily.everyDays.hourType)} ${day}/${this.state.daily.everyDays.days} * ${this.weekDayDefaultChar} ${this.yearDefaultChar}`.trim();
                        });
                        break;
                    case 'everyWeekDay':
                        // tslint:disable-next-line:max-line-length
                        this.cron = `${this.isCronFlavorQuartz ? this.state.daily.everyWeekDay.seconds : ''} ${this.state.daily.everyWeekDay.minutes} ${this.hourToCron(this.state.daily.everyWeekDay.hours, this.state.daily.everyWeekDay.hourType)} ${this.monthDayDefaultChar} * MON-FRI ${this.yearDefaultChar}`.trim();
                        break;
                    default:
                        throw new Error('Invalid cron daily subtab selection');
                }
                break;
            case 'weekly':
                const days = this.selectOptions.days
                    .reduce((acc: string[], day: string) => this.state.weekly[day] ? [...acc, day] : acc, [] as string[])
                    .join(',');
                const numberOfWeeks = this.state.weekly.numberOfWeeks;
                // tslint:disable-next-line:max-line-length
                this.cron = this.isWeeklyValid() ? `${this.isCronFlavorQuartz ? this.state.weekly.seconds : ''} ${this.state.weekly.minutes} ${this.hourToCron(this.state.weekly.hours, this.state.weekly.hourType)} ${this.monthDayDefaultChar}${numberOfWeeks != 1 ? '/' + numberOfWeeks : ''} * ${days} ${this.yearDefaultChar}`.trim() : '';
                break;
            case 'monthly':
                const today = new Date();
                const startDate = this.startDate ? (new Date(this.startDate)) : null;
                let month = startDate ? startDate.getMonth() : 1;
                const selectedDate = this.state.monthly.specificDay.day ? new Date(today.getFullYear(), month, this.state.monthly.specificDay.day) : null;
                if (startDate && selectedDate && startDate > selectedDate) {
                    month = month + 1;
                }
                switch (this.state.monthly.subTab) {
                    case 'specificDay':
                        // tslint:disable-next-line:max-line-length
                        setTimeout(() => {
                            this.cron = `${this.isCronFlavorQuartz ? this.state.monthly.specificDay.seconds : ''} ${this.state.monthly.specificDay.minutes} ${this.hourToCron(this.state.monthly.specificDay.hours, this.state.monthly.specificDay.hourType)} ${this.state.monthly.specificDay.day} ${month + 1}/${this.state.monthly.specificDay.months} ${this.weekDayDefaultChar} ${this.yearDefaultChar}`.trim();
                        });
                        break;
                    case 'specificWeekDay':
                        // tslint:disable-next-line:max-line-length
                        setTimeout(() => {                                
                            this.cron = `${this.isCronFlavorQuartz ? this.state.monthly.specificWeekDay.seconds : ''} ${this.state.monthly.specificWeekDay.minutes} ${this.hourToCron(this.state.monthly.specificWeekDay.hours, this.state.monthly.specificWeekDay.hourType)} ${this.monthDayDefaultChar} ${month + 1}/${this.state.monthly.specificWeekDay.months} ${this.state.monthly.specificWeekDay.day}${this.state.monthly.specificWeekDay.monthWeek} ${this.yearDefaultChar}`.trim();
                        });
                        break;
                    default:
                        throw new Error('Invalid cron monthly subtab selection');
                }
                break;
            case 'yearly':
                switch (this.state.yearly.subTab) {
                    case 'specificMonthDay':
                        // tslint:disable-next-line:max-line-length
                        this.cron = `${this.isCronFlavorQuartz ? this.state.yearly.specificMonthDay.seconds : ''} ${this.state.yearly.specificMonthDay.minutes} ${this.hourToCron(this.state.yearly.specificMonthDay.hours, this.state.yearly.specificMonthDay.hourType)} ${this.state.yearly.specificMonthDay.day} ${this.state.yearly.specificMonthDay.month} ${this.weekDayDefaultChar} ${this.yearDefaultChar}`.trim();
                        break;
                    case 'specificMonthWeek':
                        // tslint:disable-next-line:max-line-length
                        this.cron = `${this.isCronFlavorQuartz ? this.state.yearly.specificMonthWeek.seconds : ''} ${this.state.yearly.specificMonthWeek.minutes} ${this.hourToCron(this.state.yearly.specificMonthWeek.hours, this.state.yearly.specificMonthWeek.hourType)} ${this.monthDayDefaultChar} ${this.state.yearly.specificMonthWeek.month} ${this.state.yearly.specificMonthWeek.day}${this.state.yearly.specificMonthWeek.monthWeek} ${this.yearDefaultChar}`.trim();
                        break;
                    default:
                        throw new Error('Invalid cron yearly subtab selection');
                }
                break;
            case 'advanced':
                this.cron = this.state.advanced.expression;
                break;
            default:
                throw new Error('Invalid cron active tab selection');
        }
    }

    private getAmPmHour(hour: number) {
        return this.options.use24HourTime ? hour : (hour + 11) % 12 + 1;
    }

    private getHourType(hour: number) {
        return this.options.use24HourTime ? undefined : (hour >= 12 ? 'PM' : 'AM');
    }

    private hourToCron(hour: number, hourType: string) {
        if (this.options.use24HourTime) {
            return hour;
        } else {
            return hourType === 'AM' ? (hour === 12 ? 0 : hour) : (hour === 12 ? 12 : hour + 12);
        }
    }

    private isCronValid(cron: string): boolean {
        if (cron) {
            const cronParts = cron.split(' ');
            return (this.isCronFlavorQuartz && (cronParts.length === 6 || cronParts.length === 7) || (this.isCronFlavorStandard && cronParts.length === 5));
        }

        return false;
    }

    private handleModelChange(cron: string) {
        if (this.isDirty) {
            this.isDirty = false;
            return;
        } else {
            this.isDirty = false;
        }

        if (!this.isCronValid(cron)) {
            if (this.isCronFlavorQuartz) {
                throw new Error('Invalid cron expression, there must be 6 or 7 segments');
            }

            if (this.isCronFlavorStandard) {
                throw new Error('Invalid cron expression, there must be 5 segments');
            }
        }

        const cronType = getCronType(cron, this.options.cronFlavor);
        const cronData = parseCron(cron, cronType, this.options.use24HourTime, this.options.cronFlavor);

        if (!cronData) {
            return;
        }

        if (cronType === CronType.Minutes && cronData.minutes) {
            this.activeTab = 'minutes';
            this.state.minutes.minutes = cronData.minutes.minutes;
            this.state.minutes.seconds = cronData.minutes.seconds;
        } else if (cronType === CronType.Hourly && cronData.hourly) {
            this.activeTab = 'hourly';
            this.state.hourly.hours = cronData.hourly.hours;
            this.state.hourly.minutes = cronData.hourly.minutes;
            this.state.hourly.seconds = cronData.hourly.seconds;
        } else if (cronType === CronType.Daily_EveryDays && cronData.daily?.everyDays) {
            this.activeTab = 'daily';
            this.state.daily.subTab = 'everyDays';
            this.state.daily.everyDays.days = cronData.daily.everyDays.days;
            this.state.daily.everyDays.hours = cronData.daily.everyDays.hours;
            this.state.daily.everyDays.hourType = cronData.daily.everyDays.hourType;
            this.state.daily.everyDays.minutes = cronData.daily.everyDays.minutes;
            this.state.daily.everyDays.seconds = cronData.daily.everyDays.seconds;
        } else if (cronType === CronType.Daily_EveryWeekDay && cronData.daily?.everyWeekDay) {
            this.activeTab = 'daily';
            this.state.daily.subTab = 'everyWeekDay';
            this.state.daily.everyWeekDay.hours = cronData.daily.everyWeekDay.hours;
            this.state.daily.everyWeekDay.hourType = cronData.daily.everyWeekDay.hourType;
            this.state.daily.everyWeekDay.minutes = cronData.daily.everyWeekDay.minutes;
            this.state.daily.everyWeekDay.seconds = cronData.daily.everyWeekDay.seconds;
        } else if (cronType === CronType.Weekly && cronData.weekly) {
            this.activeTab = 'weekly';
            this.selectOptions.days.forEach(weekDay => this.state.weekly[weekDay] = false);
            cronData.weekly.days.forEach(weekDay => this.state.weekly[weekDay] = true);
            this.state.weekly.numberOfWeeks = cronData.weekly.interval;
            this.state.weekly.hours = cronData.weekly.hours;
            this.state.weekly.hourType = cronData.weekly.hourType;
            this.state.weekly.minutes = cronData.weekly.minutes;
            this.state.weekly.seconds = cronData.weekly.seconds;
        } else if (cronType === CronType.Monthly_SpecificDay && cronData.monthly?.specificDay) {
            this.activeTab = 'monthly';
            this.state.monthly.subTab = 'specificDay';
            this.state.monthly.specificDay.day = cronData.monthly.specificDay.day;
            this.state.monthly.specificDay.months = cronData.monthly.specificDay.months;
            this.state.monthly.specificDay.hours = cronData.monthly.specificDay.hours;
            this.state.monthly.specificDay.hourType = cronData.monthly.specificDay.hourType;
            this.state.monthly.specificDay.minutes = cronData.monthly.specificDay.minutes;
            this.state.monthly.specificDay.seconds = cronData.monthly.specificDay.seconds;
        } else if (cronType === CronType.Monthly_SpecificWeekDay && cronData.monthly?.specificWeekDay) {
            this.activeTab = 'monthly';
            this.state.monthly.subTab = 'specificWeekDay';
            this.state.monthly.specificWeekDay.monthWeek = cronData.monthly.specificWeekDay.monthWeek;
            this.state.monthly.specificWeekDay.day = cronData.monthly.specificWeekDay.day;
            this.state.monthly.specificWeekDay.months = cronData.monthly.specificWeekDay.months;
            this.state.monthly.specificWeekDay.hours = cronData.monthly.specificWeekDay.hours;
            this.state.monthly.specificWeekDay.hourType = cronData.monthly.specificWeekDay.hourType;
            this.state.monthly.specificWeekDay.minutes = cronData.monthly.specificWeekDay.minutes;
            this.state.monthly.specificWeekDay.seconds = cronData.monthly.specificWeekDay.seconds;
        } else if (cronType === CronType.Yearly_SpecificMonthDay && cronData.yearly?.specificMonthDay) {
            this.activeTab = 'yearly';
            this.state.yearly.subTab = 'specificMonthDay';
            this.state.yearly.specificMonthDay.month = cronData.yearly.specificMonthDay.month;
            this.state.yearly.specificMonthDay.day = cronData.yearly.specificMonthDay.day;
            this.state.yearly.specificMonthDay.hours = cronData.yearly.specificMonthDay.hours;
            this.state.yearly.specificMonthDay.hourType = cronData.yearly.specificMonthDay.hourType;
            this.state.yearly.specificMonthDay.minutes = cronData.yearly.specificMonthDay.minutes;
            this.state.yearly.specificMonthDay.seconds = cronData.yearly.specificMonthDay.seconds;
        } else if (cronType === CronType.Yearly_SpecificMonthWeek && cronData.yearly?.specificMonthWeek) {
            this.activeTab = 'yearly';
            this.state.yearly.subTab = 'specificMonthWeek';
            this.state.yearly.specificMonthWeek.monthWeek = cronData.yearly.specificMonthWeek.monthWeek;
            this.state.yearly.specificMonthWeek.day = cronData.yearly.specificMonthWeek.day;
            this.state.yearly.specificMonthWeek.month = cronData.yearly.specificMonthWeek.month;
            this.state.yearly.specificMonthWeek.hours = cronData.yearly.specificMonthWeek.hours;
            this.state.yearly.specificMonthWeek.hourType = cronData.yearly.specificMonthWeek.hourType;
            this.state.yearly.specificMonthWeek.minutes = cronData.yearly.specificMonthWeek.minutes;
            this.state.yearly.specificMonthWeek.seconds = cronData.yearly.specificMonthWeek.seconds;
        } else if (cronData.advanced) {
            this.activeTab = 'advanced';
            this.state.advanced.expression = cronData.advanced.expression;
        }

        this.activeTabIndex = this.cronEditorTabIndex[this.activeTab] ?? 0;
    }

    private getDefaultState() {
        const [defaultHours, defaultMinutes, defaultSeconds] = this.options.defaultTime.split(':').map(Number);

        return {
            minutes: {
                minutes: 1,
                seconds: 0
            },
            hourly: {
                hours: 1,
                minutes: 0,
                seconds: 0
            },
            daily: {
                subTab: 'everyDays',
                everyDays: {
                    days: 1,
                    hours: this.getAmPmHour(defaultHours),
                    minutes: defaultMinutes,
                    seconds: defaultSeconds,
                    hourType: this.getHourType(defaultHours)
                },
                everyWeekDay: {
                    hours: this.getAmPmHour(defaultHours),
                    minutes: defaultMinutes,
                    seconds: defaultSeconds,
                    hourType: this.getHourType(defaultHours)
                }
            },
            weekly: {
                MON: true,
                TUE: false,
                WED: false,
                THU: false,
                FRI: false,
                SAT: false,
                SUN: false,
                hours: this.getAmPmHour(defaultHours),
                minutes: defaultMinutes,
                seconds: defaultSeconds,
                hourType: this.getHourType(defaultHours),
                numberOfWeeks: 1
            },
            monthly: {
                subTab: 'specificDay',
                specificDay: {
                    day: '1',
                    months: 1,
                    hours: this.getAmPmHour(defaultHours),
                    minutes: defaultMinutes,
                    seconds: defaultSeconds,
                    hourType: this.getHourType(defaultHours)
                },
                specificWeekDay: {
                    monthWeek: '#1',
                    day: 'MON',
                    months: 1,
                    hours: this.getAmPmHour(defaultHours),
                    minutes: defaultMinutes,
                    seconds: defaultSeconds,
                    hourType: this.getHourType(defaultHours)
                }
            },
            yearly: {
                subTab: 'specificMonthDay',
                specificMonthDay: {
                    month: 1,
                    day: '1',
                    hours: this.getAmPmHour(defaultHours),
                    minutes: defaultMinutes,
                    seconds: defaultSeconds,
                    hourType: this.getHourType(defaultHours)
                },
                specificMonthWeek: {
                    monthWeek: '#1',
                    day: 'MON',
                    month: 1,
                    hours: this.getAmPmHour(defaultHours),
                    minutes: defaultMinutes,
                    seconds: defaultSeconds,
                    hourType: this.getHourType(defaultHours)
                }
            },
            advanced: {
                expression: this.isCronFlavorQuartz ? '0 15 10 L-2 * ? *' : '15 10 2 * *'
            }
        };
    }

    private getOrdinalSuffix(value: string) {
        if (value.length > 1) {
            const secondToLastDigit = value.charAt(value.length - 2);
            if (secondToLastDigit === '1') {
                return 'th';
            }
        }

        const lastDigit = value.charAt(value.length - 1);
        switch (lastDigit) {
            case '1':
                return 'st';
            case '2':
                return 'nd';
            case '3':
                return 'rd';
            default:
                return 'th';
        }
    }

    private getSelectOptions() {
        return {
            months: this.getRange(1, 12),
            monthWeeks: ['#1', '#2', '#3', '#4', '#5', 'L'],
            days: ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'],
            minutes: this.getRange(0, 59),
            fullMinutes: this.getRange(0, 59),
            seconds: this.getRange(0, 59),
            hours: this.getRange(1, 23),
            monthDays: this.getRange(1, 31),
            monthDaysWithLasts: ['1W', ...[...this.getRange(1, 31).map(String)], 'LW', 'L'],
            monthDaysWithOutLasts: [...[...this.getRange(1, 31).map(String)]],
            hourTypes: ['AM', 'PM']
        };
    }

    private getRange(start: number, end: number): number[] {
        const length = end - start + 1;
        return Array.apply(null, Array(length)).map((_, i) => i + start);
    }

    simpleItemMapper(option: string) {
        return option;
    }

    monthDayDisplayTextMapper(option: string) {
        return this.monthDayDisplay(option);
    }

    monthWeekDisplayTextMapper(option: number) {
        return this.monthWeekDisplay(option.toString() as keyof typeof MonthWeeks);
    }

    dayDisplayTextMapper(option: string) {
        return this.dayDisplay(option as keyof typeof Days);
    }

    monthDisplayTextMapper(option: number) {
        return this.monthDisplay(option);
    }

    isWeeklyValid(): boolean {
        if (this.state.weekly.numberOfWeeks == null || this.state.weekly.numberOfWeeks <= 0) {
            return false;
        }
        return true;
    }
}
