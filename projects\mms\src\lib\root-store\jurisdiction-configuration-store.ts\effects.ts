import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { select, Store } from '@ngrx/store';
import { of as observableOf, pipe } from 'rxjs';
import { catchError, concatMap, exhaustMap, map, mergeMap, switchMap, withLatestFrom } from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureSelectors from './selectors'

import * as contractSelectors from '../contract-store/selectors'

import { MatDialog } from '@angular/material/dialog';
import { ContractService } from '../../payroll-and-billing/contracts/contract.service';
import { Contract } from '../../payroll-and-billing/contracts/models/contract.model';
import { AgencyStoreSelectors } from '../agency-store';
import { Page } from '../../core/page.service';
import { ContractStoreActions } from '../contract-store';
import { GridStoreActions } from '../shared-store/material-grid-store';
import { BILL_CODE_RATES_GRID_ID, JOB_CODE_RATES_GRID_ID, MARKUP_GRID_ID, TRIP_CHARGES_GRID_ID, VEHICLE_EQUIPMENT_RATES_GRID_ID } from './state';

@Injectable()
export class JurisdictionConfigurationStoreEffects {
  constructor(
    private contractService: ContractService,
    private store$: Store<any>,
    private actions$: Actions,
    public dialog: MatDialog,
    private page: Page) { }

  loadEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadAction>(
      featureActions.ActionTypes.LOAD_ACTION
    ),
    withLatestFrom(
      this.store$.pipe(select(contractSelectors.selectedContract))
    ),
    map(([action, selectedContract]: [featureActions.LoadAction, Contract]) => {
      if (selectedContract == null) {
        return new featureActions.LoadSuccessAction({ items: [] })
      } else {
        return new featureActions.LoadRequestAction({ contractId: selectedContract?.id || '' })
      }
    })
  ));

  loadRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(
      featureActions.ActionTypes.LOAD_REQUEST
    ),
    switchMap((action) =>
      this.contractService
        .getJurisdictionConfigurations({ contractId: action.payload.contractId })
        .pipe(
          map(
            items =>
              new featureActions.LoadSuccessAction({
                items
              })
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  loadJurisdictionsByAgencyEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(
      featureActions.ActionTypes.LOAD_ALL_JURISDICTION_CONFIGURATIONS_REQUEST
    ),
    withLatestFrom(this.store$.pipe(select(AgencyStoreSelectors.selectDefaultAgency))),
    switchMap(([_, agency]) =>
      this.contractService
        .getJurisdictionConfigurations({ agencyId: (agency && agency.id) ?? '' })
        .pipe(
          map(
            items =>
              new featureActions.LoadAllJurisdictionConfigurationsSuccessAction({
                items
              })
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  saveEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveAction>(
      featureActions.ActionTypes.SAVE_ACTION
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedJurisdictionConfiguration)),
      this.store$.pipe(select(contractSelectors.selectedContract)),
      this.store$.pipe(select(AgencyStoreSelectors.selectDefaultAgency)),
      this.store$.pipe(select(featureSelectors.selectJobCodeRatesGrid)),
      this.store$.pipe(select(featureSelectors.selectVehicleEquipmentRateGrid)),
      this.store$.pipe(select(featureSelectors.selectTripChargesGrid)),
      this.store$.pipe(select(featureSelectors.selectMarkupsGrid)),
      this.store$.pipe(select(featureSelectors.selectBillCodeRatesGrid)),
      this.store$.pipe(select(featureSelectors.selectVehicleEquipmentCategoryMappersGrid)),
      this.store$.pipe(select(featureSelectors.checkCreateNewConfig)),
    ),
    map(([action, selectedJurisdictionConf, selectedContract, agency,
      jobCodeRates, vehEquipRates, tripCharges, markups, billCodes, vehicleEquipmentCategoryMappers, createNewConfig]) => {
      if (selectedJurisdictionConf == null && createNewConfig == false) {
        const item = {
          ...action.payload.item,
          contract: { id: selectedContract?.id || '', name: selectedContract?.name || '' },
          agency: { id: agency?.id || '', name: agency?.name || '' },
          jobCodeRates: jobCodeRates.items,
          tripCharges: tripCharges.items,
          vehicleEquipmentRates: vehEquipRates.items,
          markups: markups.items,
          billCodes: billCodes.items,
          vehicleEquipmentCategoryMappers: vehicleEquipmentCategoryMappers.items
        }
        return new featureActions.SaveRequestAction({ item });
      } 
      else if(createNewConfig == true) {
        const item = {
          ...action.payload.item,
          contract: { id: selectedContract?.id || '', name: selectedContract?.name || '' },
          agency: { id: agency?.id || '', name: agency?.name || '' },
          jobCodeRates: jobCodeRates.items,
          tripCharges: tripCharges.items,
          vehicleEquipmentRates: vehEquipRates.items,
          markups: markups.items,
          billCodes: billCodes.items,
          vehicleEquipmentCategoryMappers: vehicleEquipmentCategoryMappers.items
        }
        
        return new featureActions.SaveRequestAction({ item });
      }
      else {
        const item = {
          ...action.payload.item,
          jobCodeRates: jobCodeRates.items,
          tripCharges: tripCharges.items,
          vehicleEquipmentRates: vehEquipRates.items,
          markups: markups.items,
          billCodes: billCodes.items,        
          vehicleEquipmentCategoryMappers: vehicleEquipmentCategoryMappers.items
        }
        return new featureActions.UpdateRequestAction({ item })
      }
    })
  ));

  saveRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveRequestAction>(
      featureActions.ActionTypes.SAVE_REQUEST
    ),
    withLatestFrom(
      this.store$.pipe(select(contractSelectors.selectedContract)),
      this.store$.pipe(select(featureSelectors.selectCopiedJobCodes))
    ),
    exhaustMap(([action, contract, copiedJobCodes]) =>
      this.contractService
        .createJurisdictionConfiguration(action.payload.item)
        .pipe(
          concatMap((jurisdictionConfiguration) => {
            let actions = [];
            actions.push(new featureActions.SaveSuccessAction({
              item: jurisdictionConfiguration!
            }));

            actions.push(new featureActions.LoadAction());

            if (copiedJobCodes && copiedJobCodes.length) {
              contract.jobCodes = copiedJobCodes;
              actions.push(new ContractStoreActions.SaveAction({ item: contract }));
            }
            return actions;
          }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  updateRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateRequestAction>(
      featureActions.ActionTypes.UPDATE_REQUEST
    ),
    withLatestFrom(
      this.store$.pipe(select(contractSelectors.selectedContract)),
      this.store$.pipe(select(featureSelectors.selectCopiedJobCodes))
    ),
    exhaustMap(([action, contract, copiedJobCodes]) =>
      this.contractService
        .updateJurisdictionConfiguration(action.payload.item)
        .pipe(
          switchMap(() => {
            let actions = [];
            actions.push(new featureActions.UpdateSuccessAction({ item: action.payload.item  }));
            actions.push(new featureActions.LoadAction());

            if (copiedJobCodes && copiedJobCodes.length) {
              contract.jobCodes = copiedJobCodes;
              actions.push(new ContractStoreActions.SaveAction({ item: contract }));
            }
            return actions;
          }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  confirmDeleteJurisdictionConfEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmDeleteJurisdictionConf>(
      featureActions.ActionTypes.CONFIRM_DELETE_JURISDICTION_CONF
    ),
    exhaustMap(() =>
      this.page
        .confirm.show('Are you sure you want to delete this Jurisdiction configuration?', 'Are you sure?')
        .pipe(
          map(
            (result) => {
              if (result) {
                return new featureActions.DeleteRequestAction();
              }
              return new featureActions.NoAction();
            }
          )
        )
    )
  ));

  deleteRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteRequestAction>(
      featureActions.ActionTypes.DELETE_REQUEST
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedJurisdictionConfiguration)),
    ),
    switchMap(([_, selectedJurisdictionConf]) =>
      this.contractService
        .deleteJurisdictionConfiguration(selectedJurisdictionConf?.id!)
        .pipe(
          switchMap(res => [
            new featureActions.DeleteSuccessAction({
              itemId: selectedJurisdictionConf?.id!
            }),
            new featureActions.LoadAction(),
          ]),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  deleteSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteSuccessAction>(
      featureActions.ActionTypes.DELETE_SUCCESS
    ),
    pipe(
      map(() => {
        this.page.notification.show('Jurisdiction Configuration deleted');
      }),
      map(res => new featureActions.JurisdictionConfigurationResetAction())
    )
  ));

  changeSelectedEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ChangeSelectedJurisdictionConf>(
      featureActions.ActionTypes.CHANGE_SELECTED_JURISDICTION_CONF
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectFormChanged))
    ),
    map(([action, formChanged]) => {
      if (formChanged) {
        return new featureActions.ConfirmChangeSelectedJurisdictionConf({ item: action.payload.item })
      } else {
        return new featureActions.ChangeSelectedJurisdictionConfSuccess({ item: action.payload.item })
      }
    })
  ));

  confirmChangeJurisdictionEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmChangeSelectedJurisdictionConf>(
      featureActions.ActionTypes.CONFIRM_CHANGE_SELECTED_JURISDICTION
    ),
    exhaustMap((action) =>
      this.page
        .confirm.show('You have unsaved changes, are you sure you want to leave this page?', 'Are you sure?')
        .pipe(
          map(
            (result) => {
              if (result) {
                return new featureActions.ChangeSelectedJurisdictionConfSuccess({ item: action.payload.item });
              }
              return new featureActions.NoAction();
            }
          )
        )
    )
  ));

  reverChangesActionEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.JurisdictionConfigurationRevertChangesAction>(
      featureActions.ActionTypes.JURISDICTION_CONF_REVERT_CHANGES_ACTION
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedJurisdictionConfiguration)),
      this.store$.pipe(select(featureSelectors.selectJurisdictionJobCodeRates))
    ),
    mergeMap(([_, selectedJurisdictionConf, jobCodeRates]) => {
      if (selectedJurisdictionConf == null) {
        return [
          new GridStoreActions.GridLoadItemsSuccessAction(TRIP_CHARGES_GRID_ID, { items: [] }),
          new GridStoreActions.GridLoadItemsSuccessAction(VEHICLE_EQUIPMENT_RATES_GRID_ID, { items: [] }),
          new GridStoreActions.GridLoadItemsSuccessAction(MARKUP_GRID_ID, { items: [] }),
          new GridStoreActions.GridLoadItemsSuccessAction(JOB_CODE_RATES_GRID_ID, { items: [] }),
          new GridStoreActions.GridLoadItemsSuccessAction(BILL_CODE_RATES_GRID_ID, { items: [] }),
        ]
      } else {
        return [
          new GridStoreActions.GridLoadItemsSuccessAction(TRIP_CHARGES_GRID_ID, { items: selectedJurisdictionConf.tripCharges! }),
          new GridStoreActions.GridLoadItemsSuccessAction(VEHICLE_EQUIPMENT_RATES_GRID_ID, { items: selectedJurisdictionConf.vehicleEquipmentRates! }),
          new GridStoreActions.GridLoadItemsSuccessAction(MARKUP_GRID_ID, { items: selectedJurisdictionConf.markups! }),
          new GridStoreActions.GridLoadItemsSuccessAction(JOB_CODE_RATES_GRID_ID, { items: jobCodeRates }),
          new GridStoreActions.GridLoadItemsSuccessAction(BILL_CODE_RATES_GRID_ID, { items: selectedJurisdictionConf.billCodes! }),
        ]
      }
    })
  ));

  contractHoursChangedEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ContractHoursChangedAction>(
      featureActions.ActionTypes.CONTRACT_HOURS_CHANGED
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedJurisdictionConfiguration))
    ),
    map(([action, selectedJurisdictionConf]) => {
      if (selectedJurisdictionConf && selectedJurisdictionConf?.contractHours?.id !== action.payload.contractHours.id) {
        this.page.alert.warning('Contract Hours should not be changed in the middle of a shift.')
      }
      return new featureActions.NoAction();
    })
  ));
}