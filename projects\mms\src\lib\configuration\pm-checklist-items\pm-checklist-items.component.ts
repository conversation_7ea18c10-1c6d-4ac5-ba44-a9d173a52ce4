import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { Observable } from 'rxjs';
import { filter } from 'rxjs/operators';

import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';

import { ChecklistItemGroup } from './shared/checklist-item-group.model';
import { PMChecklistItemStoreState, PMChecklistItemStoreSelectors, PMChecklistItemStoreActions } from '../../root-store';
import { PermissionsEnum } from '../../core/auth/permissions.enum';
import { BlockUIDirective } from '../../shared/block-ui/block-ui.directive';
import { IdentityClientModule } from '@econolite/identity-client';

@Component({
    templateUrl: './pm-checklist-items.component.html',
    styleUrls: ['./pm-checklist-items.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        RouterModule,
        MatIconModule,
        MatButtonModule,
        MatTooltipModule,
        MatFormFieldModule,
        MatInputModule,
        BlockUIDirective,
        // IdentityClientModule
    ]
})
export class PMChecklistItemsComponent implements OnInit {
    error$!: Observable<string>;
    isLoading$!: Observable<boolean>;
    isSearchActive$!: Observable<boolean>;
    checkListItemGroups$!: Observable<ChecklistItemGroup[]>;
    selectedCheckListItemGroup$!: Observable<ChecklistItemGroup>;
    Permissions = PermissionsEnum;
    @ViewChild('searchInput', { static: false }) searchInput!: ElementRef;

    constructor(
        private title: Title,
        private store$: Store<PMChecklistItemStoreState.State>
    ) { }

    ngOnInit() {
        this.checkListItemGroups$ = this.store$.select(
            PMChecklistItemStoreSelectors.selectFilteredChecklistItemGroups
        );

        this.error$ = this.store$.select(
            PMChecklistItemStoreSelectors.selectChecklistItemGroupError
        );

        this.isLoading$ = this.store$.select(
            PMChecklistItemStoreSelectors.selectChecklistItemGroupIsLoading
        );

        this.isSearchActive$ = this.store$.select(
            PMChecklistItemStoreSelectors.selectSearchActive
        );

        this.selectedCheckListItemGroup$ = this.store$
            .select(PMChecklistItemStoreSelectors.selectSelectedChecklistItemGroup)
            .pipe(
                filter((group): group is ChecklistItemGroup => group !== null)
            );

        this.store$.dispatch(
            new PMChecklistItemStoreActions.LoadRequestAction()
        );

        this.title.setTitle('Checklist Groups');
    }


    add(): void {
        this.store$.dispatch(new PMChecklistItemStoreActions.AddNewAction());
    }

    delete() {
        this.store$.dispatch(new PMChecklistItemStoreActions.ConfirmDeleteAction());
    }

    activateSearch() {
        this.store$.dispatch(new PMChecklistItemStoreActions.ActivateSearchAction());
        setTimeout(() => {
            this.searchInput.nativeElement.focus();
          }, 20);
    }

    cancelSearch() {
        this.store$.dispatch(new PMChecklistItemStoreActions.DeactivateSearchAction())
    }

    searchChage(value: string) {
        this.store$.dispatch(new PMChecklistItemStoreActions.SearchTextChangedAction(value))
    }
}
