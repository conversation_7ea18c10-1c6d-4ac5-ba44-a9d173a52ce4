import { Component, Inject } from '@angular/core';
import { FormG<PERSON>, FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatChipInputEvent, MatChipsModule } from '@angular/material/chips';
import { ENTER } from '@angular/cdk/keycodes';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';

import { Validators } from '../../validators/validators';
import { Field } from '../../../../configuration/shared/asset-field/field.model';
import { FieldTypeEnum } from '../../../../configuration/shared/asset-field/field-type.enum';
import { ValidationMessageComponent } from '../../validation-message/validation-message.component';
import { HintMessageComponent } from '../../hint-message/hint-message.component';
import { MmsSelectComponent } from '../mms-select/mms-select.component';

import { AssetStoreSelectors, AssetStoreState, LocationStoreSelectors } from '../../../../root-store';
import { Store } from '@ngrx/store';
import { map } from 'rxjs/operators';
import { of } from 'rxjs';

@Component({
  selector: 'mms-new-field-dialog-component',
  templateUrl: 'new-field-dialog.component.html',
  styleUrls: ['./new-field-dialog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatSelectModule,
    ValidationMessageComponent,
    HintMessageComponent,
    MmsSelectComponent
  ]
})
export class NewFieldDialogComponent {
  formGroup: FormGroup;
  showDropdownOptions = false;
  matChipsConfig = {
    visible: true,
    selectable: true,
    removable: true,
    addOnBlur: true,
    separatorKeysCodes: [ENTER]
  };

  types = [
    { id: 0, name: 'Check Box' },
    { id: 1, name: 'Currency' },
    { id: 2, name: 'Date' },
    { id: 3, name: 'Date and Time' },
    { id: 4, name: 'Decimal' },
    { id: 5, name: 'Dropdown Box' },
    { id: 6, name: 'File' },
    { id: 7, name: 'Integer' },
    { id: 8, name: 'Manufacturer' },
    { id: 9, name: 'Multi-line Text' },
    { id: 10, name: 'Text' },
    { id: 11, name: 'Time' },
    { id: 12, name: 'User or Group' }
  ]

  constructor(private fb: FormBuilder,
    public dialogRef: MatDialogRef<NewFieldDialogComponent>, 
    @Inject(MAT_DIALOG_DATA) public data: { 
      id: string; 
      name: string; 
      description: string; 
      type: number; 
      availableValues: string[];
      fromAssets?: boolean;
      fromLocations?: boolean;
    }, 
    private store$: Store<AssetStoreState.State>) {
    this.formGroup = fb.group({
      id: [data.id],
      name: [data.name, [Validators.required(), Validators.emoji()], [Validators.async(this.isNameValid.bind(this), '$value name already exists.')]],
      description: [data.description, [Validators.emoji()]],
      type: [data.type ? data.type.toString() : null, [Validators.required()]],
      availableValues: [data.availableValues]
    });
    if (data.type && data.type.toString() === FieldTypeEnum.Dropdown.toString()) {
      this.showDropdownOptions = true;
      this.formGroup.controls['availableValues'].setValidators([Validators.required()]);
    }
  }

  save() {
    if (this.formGroup.valid) {
      this.dialogRef.close(this.formGroup.value);
      this.formGroup.reset();
    }
  }

  close() {
    this.dialogRef.close();
  }

  fieldTypeChange(value: number) {
    if (value === FieldTypeEnum.Dropdown) {
      this.showDropdownOptions = true;
      this.formGroup.controls['availableValues'].setValidators([Validators.required()]);
    } else {
      this.showDropdownOptions = false;
      this.formGroup.controls['availableValues'].clearValidators();
      this.formGroup.updateValueAndValidity();
      this.formGroup.controls['availableValues'].setErrors(null);
    }
  }

  add(event: MatChipInputEvent): void {
    const value = event.value;
    const input = event.input;
    if ((event.value || '').trim()) {
      this.data.availableValues.push(value.trim());
      this.formGroup.patchValue({ availableValues: this.data.availableValues });
    }

    if (input) {
      input.value = '';
    }
  }

  remove(option: string) {
    this.data.availableValues = this.data.availableValues.filter((v: string) => v !== option);
    this.formGroup.patchValue({ availableValues: this.data.availableValues });
  }

  sortChipsAscending() {
    this.data.availableValues.sort(new Intl.Collator('en',{ numeric: true, sensitivity: 'accent' }).compare);
  }

  sortChipsDescending() {
    const sortedDataASC = this.data.availableValues.sort(new Intl.Collator('en',{ numeric: true, sensitivity: 'accent' }).compare);
    sortedDataASC.reverse();
  }

  getTypeDescription() {
    return FieldTypeEnum[this.data.type];
  }

  private isNameValid(name: string) {
    // since this component is used only in /assets and /locations we need to check only 2 bools
    if(this.data.fromAssets){
      return this.store$.select(AssetStoreSelectors.selectGridItems)
      .pipe(
        map((items : Field[]) => {
          return items.filter(x => (x.name != null) && x.id != this.data.id && x.name.toLocaleLowerCase() === name.toLocaleLowerCase()).length === 0;
        })
      );
    } else if(this.data.fromLocations){
      return this.store$.select(LocationStoreSelectors.selectGridItems)
      .pipe(
        map((items : Field[]) => {
          return items.filter(x => (x.name != null) && x.name.toLocaleLowerCase() === name.toLocaleLowerCase()).length === 0;
        })
      );
    }
    return of(true);
  }

}
