import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, LOCALE_ID, Inject, ChangeDetectorRef, ElementRef } from '@angular/core';
import { CalendarWeekViewComponent, getWeekViewPeriod, CalendarUtils, DateAdapter, CalendarEvent, CalendarWeekViewBeforeRenderEvent, CalendarModule } from 'angular-calendar';
import { WeekDay } from 'calendar-utils';
import { CalendarWeekViewData } from '../week-view-data.model';
import { getReccurenceType } from '../../common/util';
import { Subject } from 'rxjs';
import { CommonModule } from '@angular/common';
import { MMSCalendarWeekViewHeaderComponent } from '../calendar-week-view-header/calendar-week-view-header.component';
import { ResizableModule } from 'angular-resizable-element';

@Component({
    selector: 'mms-calendar-week-view',
    templateUrl: './mms-calendar-week-view.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    styleUrls: ['./mms-calendar-week-view.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        CalendarModule,
        MMSCalendarWeekViewHeaderComponent,
        ResizableModule
    ]
})
export class MMSCalendarWeekViewComponent extends CalendarWeekViewComponent {
  private filterEvent: { dayDate: Date, group: string } | null = null;
  private _data: CalendarWeekViewData = new CalendarWeekViewData(new Date(), [], [], []);
  private activeFilterGroup: string = '';
  getReccurenceType = getReccurenceType;
  override refresh: Subject<any> = new Subject();
  eventDragEnter = 0;
  
  @Input() override dayEndMinute = 59;
  @Input() override viewDate: Date = new Date();
  @Input() override weekStartsOn = 0;
  @Input() override excludeDays: number[] = [];
  @Input() override precision: 'minutes' | 'days' = 'minutes';
  @Input() override hourSegments = 2;
  @Input() override dayStartHour = 0;
  @Input() override dayStartMinute = 0;
  @Input() override dayEndHour = 23;
  @Input() override hourSegmentHeight = 30;
  @Input() override weekendDays: number[] = [0, 6];
  @Input() override daysInWeek = 7;
  
  override days: WeekDay[] = [];
  override view: any = {};
  @Output() override beforeViewRender = new EventEmitter<CalendarWeekViewBeforeRenderEvent>();
  
  get data(): CalendarWeekViewData {
    return this._data;
  }

  @Input() 
  set data(value: CalendarWeekViewData) {
    this._data = value;
  }

  constructor(
    public override cdr: ChangeDetectorRef,
    public override utils: CalendarUtils,
    @Inject(LOCALE_ID) public override locale: string,
    public override dateAdapter: DateAdapter,
    public override element: ElementRef
  ) {
    super(cdr, utils, locale, dateAdapter, element);
  }

  weekDayFilterEvents(event: { dayDate: Date, group: string }) {
    if (!this.filterEvent || this.getDateTime(this.filterEvent.dayDate) !== this.getDateTime(event.dayDate) || this.filterEvent.group !== event.group) {
      this.filterEvent = event;
      const filteredEvents = this.data.events.filter(x => this.getDateTime(x.start) !== this.getDateTime(event.dayDate) 
                                                || (x.meta.cronType && x.meta.cronType === event.group));
      this.refreshViewBody(filteredEvents);
    } else {
      this.filterEvent = null;
      this.refreshViewBody(this.data.events);
    }
  }

  private getDateTime(date: Date) {
    return new Date(date.getFullYear(), date.getMonth(), date.getDate()).getTime();
  }

  private refreshViewBody(events: CalendarEvent[]): void {
    const weekView = this.getWeekViewEvents(events);
    this.data.dayEventRows = weekView.allDayEventRows;
    this.emitBeforeViewRenderEvent();
  }

  private getWeekViewEvents(events: CalendarEvent[]) {
    return this.utils.getWeekView({
      events,
      viewDate: this.viewDate,
      weekStartsOn: this.weekStartsOn,
      excluded: this.excludeDays,
      precision: this.precision,
      absolutePositionedEvents: true,
      hourSegments: this.hourSegments,
      dayStart: {
        hour: this.dayStartHour,
        minute: this.dayStartMinute
      },
      dayEnd: {
        hour: this.dayEndHour,
        minute: this.dayEndMinute
      },
      segmentHeight: this.hourSegmentHeight,
      weekendDays: this.weekendDays,
      ...getWeekViewPeriod(
        this.dateAdapter,
        this.viewDate,
        this.weekStartsOn,
        this.excludeDays,
        this.daysInWeek
      )
    });
  }

  private emitBeforeViewRenderEvent(): void {
    if (this.days && this.view) {
      this.beforeViewRender.emit({
        header: this.days,
        ...this.view
      });
    }
  }

  override eventDropped(dropEvent: any): void {
    // Handle the event drop from mwlDroppable
    const event = dropEvent.dropData?.event;
    if (event) {
      // Extract the date from the drop event or use current viewDate as fallback
      const newStart = dropEvent.dropData?.newStart || this.viewDate;
      const allDay = dropEvent.dropData?.allDay || false;
      
      // Emit or handle the event drop as needed
      // You can add custom logic here to handle the event drop
      console.log('Event dropped:', { event, newStart, allDay });
    }
  }
}
