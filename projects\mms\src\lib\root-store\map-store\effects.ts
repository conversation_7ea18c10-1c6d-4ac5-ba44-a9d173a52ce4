import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Action, Store } from '@ngrx/store';
import { of as observableOf, of } from 'rxjs';
import { catchError, map, switchMap, withLatestFrom, concatMap } from 'rxjs/operators';

import * as projectStoreActions from '../projects-store/actions';
import * as jurisdictionsStoreActions from '../jurisdiction-store/actions';

import * as featureActions from './actions';
import * as featureState from './state'
import * as featureSelectors from './selectors'

import { AgencyStoreSelectors, AgencyStoreActions } from '../agency-store';
import { TicketStoreSelectors } from '../ticket-store';
import { UserIdentity } from '@econolite/identity-client';
import { localStorageCompositeKeys } from '../../core/resources/local-storage-composite-keys';
import { MmsMapService } from '../../mms-map/mms-map.service';
// import { ToLayerTypes } from '../../configuration/map-layers/models/map-layer.model';
import { LocationInstancesService } from '../../configuration/location-instances/shared/location-instances.service';
import { ToLocationInstanceGridModel, ToLocationInstances, ToPMFeatures, ToTicketFeatures, ToWorkTicketFeatures } from '../../mms-map/models/map.models';
import { LocationService } from '../../configuration/type-and-field-definitions/locations/locations.service';
import { TicketService } from '../../configuration/tickets/tickets.service';
import { Filter } from '../../configuration/tickets/filter/filter.model';
import { TicketTypeCategory } from '../../configuration/ticket-types/enums/ticket-category.enum';
import { LOCATION_INSTANCES_GRID_ID } from '../location-instance-store/state';
import { GridLoadItemsAction } from '../shared-store/material-grid-store/actions';
import { RegionStoreActions } from '../region-store';
import { TicketStatus } from '../../configuration/tickets/tickets-enum/ticket-status.enum';
import { Ticket } from '../../configuration/tickets/tickets.model';
import { MapLayer } from '../../settings/map-layers/models/map-layer.model';

@Injectable()
export class MapStoreEffects {
    constructor(
        private mmsMapService: MmsMapService,
        private locationService: LocationService,
        private locationInstanceService: LocationInstancesService,
        private ticketService: TicketService,
        private actions$: Actions,
        private store$: Store<featureState.State>,
        private userIdentity: UserIdentity) { }

    loadAllMapsRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadMapKeyAction>(
            featureActions.ActionTypes.LOAD_ALL_MAP_KEY_REQUEST
        ),
        switchMap(() =>
        this.mmsMapService.getMapKey()
        .pipe(
            map((mapKey) => new featureActions.LoadMapKeySuccessAction(mapKey)),
            catchError(error =>
                observableOf(new featureActions.LoadMapConfigFailureAction({ error }))
            )
        )
        )
    ));

    loadRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadMapConfigRequestAction>(
            featureActions.ActionTypes.LOAD_MAP_CONFIG_REQUEST
        ),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
        ),
        concatMap(([action, agency]) => {
            const actions: Action[] = [];
            const agencyId = agency ? agency.id : localStorage.getItem(localStorageCompositeKeys.defaultTenantAgency(this.userIdentity.tenantId));
            if (!agencyId) {
                actions.push(new AgencyStoreActions.LoadDefaultAgencyRequestAction())
                return actions
            }
            return this.mmsMapService
                .getMapConfigs(agencyId)
                .pipe(
                    concatMap((item) => {
                            if (item) {
                                actions.push(new featureActions.LoadMapConfigSuccessAction(item))
                            }
                            return actions;
                        }
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadMapConfigFailureAction({ error }))
                    )
                )
        })
    ));

    addMapConfigEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.AddMapConfigAction>(
            featureActions.ActionTypes.ADD_MAP_CONFIG
        ),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
        ),
        switchMap(([data, agency]) => {
            const actions = [];
            const agencyId = agency ? agency.id : localStorage.getItem(localStorageCompositeKeys.defaultTenantAgency(this.userIdentity.tenantId));
            if (!agencyId) {
                actions.push(new AgencyStoreActions.LoadDefaultAgencyRequestAction())
                return actions
            }
            data.payload.agencyId = agencyId;
            return this.mmsMapService
                .addMapConfig(data.payload)
                .pipe(
                    map(
                        item =>
                            new featureActions.LoadMapConfigRequestAction()
                    ),
                    catchError(error =>
                        observableOf(new featureActions.AddMapConfigFailureAction({ error }))
                    )
                )
        })
    ));

    editMapConfigEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.EditMapConfigAction>(
            featureActions.ActionTypes.EDIT_MAP_CONFIG
        ),
        switchMap((data) => {
            return this.mmsMapService
                .editMapConfig(data.payload)
                .pipe(
                    map(
                        item =>
                            new featureActions.LoadMapConfigRequestAction()
                    ),
                    catchError(error =>
                        observableOf(new featureActions.EditMapConfigFailureAction({ error }))
                    )
                )
        })
    ));

    deleteMapConfigEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteMapConfigAction>(
            featureActions.ActionTypes.DELETE_MAP_CONFIG
        ),
        switchMap((data) => {
            return this.mmsMapService
                .deleteMapConfig(data.payload)
                .pipe(
                    map(
                        item =>
                            new featureActions.LoadMapConfigRequestAction()
                    ),
                    catchError(error =>
                        observableOf(new featureActions.DeleteMapConfigFailureAction({ error }))
                    )
                )
        })
    ));

    loadLayerRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadMapLayerConfigRequestAction>(
            featureActions.ActionTypes.LOAD_MAP_LAYER_CONFIG_REQUEST
        ),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
        ),
        concatMap(([action, agency]) => {
            const actions: Action[] = [];
            const agencyId = agency ? agency.id : localStorage.getItem(localStorageCompositeKeys.defaultTenantAgency(this.userIdentity.tenantId));
            if (!agencyId) {
                actions.push(new AgencyStoreActions.LoadDefaultAgencyRequestAction())
                return actions
            }
            return this.mmsMapService
                .getLayers(agencyId)
                .pipe(
                    concatMap((item) => {
                            if (item) {
                                const layers = featureSelectors.ToLayerTypes(item);
                                actions.push(new featureActions.LoadMapLayerConfigSuccessAction(layers as MapLayer[]))
                            }
                            return actions;
                        }
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadMapConfigFailureAction({ error }))
                    )
                )
        })
    ));

    addMapLayerConfigEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.AddMapLayerConfigAction>(
            featureActions.ActionTypes.ADD_MAP_LAYER_CONFIG
        ),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
        ),
        switchMap(([data, agency]) => {
            const actions = [];
            const agencyId = agency ? agency.id : localStorage.getItem(localStorageCompositeKeys.defaultTenantAgency(this.userIdentity.tenantId));
            if (!agencyId) {
                actions.push(new AgencyStoreActions.LoadDefaultAgencyRequestAction())
                return actions
            }
            data.payload.agencyId = agencyId;
            return this.mmsMapService
                .addLayerConfig(data.payload)
                .pipe(
                    map(
                        item =>
                            new featureActions.LoadMapLayerConfigRequestAction()
                    ),
                    catchError(error =>
                        observableOf(new featureActions.AddMapLayerConfigFailureAction({ error }))
                    )
                )
        })
    ));

    editMapLayerConfigEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.EditMapLayerConfigAction>(
            featureActions.ActionTypes.EDIT_MAP_LAYER_CONFIG
        ),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
        ),
        switchMap(([data, agency]) => {
            const actions = [];
            const agencyId = agency ? agency.id : localStorage.getItem(localStorageCompositeKeys.defaultTenantAgency(this.userIdentity.tenantId));
            if (!agencyId) {
                actions.push(new AgencyStoreActions.LoadDefaultAgencyRequestAction())
                return actions
            }
            data.payload.agencyId = agencyId;
            return this.mmsMapService
                .editLayerConfig(data.payload)
                .pipe(
                    map(
                        item =>
                            new featureActions.LoadMapLayerConfigRequestAction()
                    ),
                    catchError(error =>
                        observableOf(new featureActions.EditMapLayerConfigFailureAction({ error }))
                    )
                )
        })
    ));

    deleteMapLayerConfigEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteMapLayerConfigAction>(
            featureActions.ActionTypes.DELETE_MAP_LAYER_CONFIG
        ),
        switchMap((data) => {
            return this.mmsMapService
                .deleteLayerConfig(data.payload)
                .pipe(
                    map(
                        item =>
                            new featureActions.LoadMapLayerConfigRequestAction()
                    ),
                    catchError(error =>
                        observableOf(new featureActions.DeleteMapLayerConfigFailureAction({ error }))
                    )
                )
        })
    ));

    loadMapEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadMapAction>(
            featureActions.ActionTypes.LOAD_MAP
        ),
        concatMap((data) => [
            new featureActions.LoadMapConfigRequestAction(),
            new featureActions.LoadMapLayerConfigRequestAction(),
            new featureActions.LoadAllMapDataAction()
        ])
    ));

    loadAllMapDataEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadAllMapDataAction>(
            featureActions.ActionTypes.LOAD_ALL_MAP_DATA
        ),
        concatMap((data) => [
            new RegionStoreActions.LoadRequestAction(),
            new projectStoreActions.LoadProjectsRequestAction({ isFilter: true }),
            new jurisdictionsStoreActions.LoadJurisdictionsRequestAction(),
            new GridLoadItemsAction(LOCATION_INSTANCES_GRID_ID),
            new featureActions.LoadLocationTypesAction()
        ])
    ));

    loadLocationInstancesEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadLocationsAction>(
            featureActions.ActionTypes.LOAD_LOCATIONS
        ),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
            this.store$.select(featureSelectors.selectMapFilterLocation)
        ),
        withLatestFrom(this.store$.select(featureSelectors.selectLocationTypes)),
        switchMap((data) => {
            if (!data[0][1]) {
              return [];
            }
            return this.locationInstanceService
                .getAllLocationsByAgency(data[0][1].id!)
                .pipe(
                    map(
                        items => {
                            const filter = data[0][2];
                            const mapLocation = items.map(v => ToLocationInstanceGridModel(v)).filter(v => {
                                return v.latitudeLongitude &&
                                v.latitudeLongitude.length > 0 &&
                                ((filter && filter.locationTypes) ?
                                (filter.locationTypes.find(l => l.id === v.locationTypeId)) :
                                true)});
                            return new featureActions.LoadLocationsSuccessAction(ToLocationInstances(data[1], mapLocation))
                        }
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadMapConfigFailureAction({ error }))
                    )
                )
        })
    ));

    loadLocationTypesEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadLocationTypesAction>(
            featureActions.ActionTypes.LOAD_LOCATION_TYPES
        ),
        switchMap((data) => {
            return this.locationService
                .getLocations()
                .pipe(
                    map(
                        items =>
                        new featureActions.LoadLocationTypesSuccessAction(items.locationTypeList)
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadMapConfigFailureAction({ error }))
                    )
                )
        })
    ));

    loadLocationTypesSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadLocationTypesSuccessAction>(
            featureActions.ActionTypes.LOAD_LOCATION_TYPES_SUCCESS
        ),
        switchMap((data) => {
            return of(new featureActions.LoadLocationsAction());
        })
    ));

    loadLocationsSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadLocationsSuccessAction>(
            featureActions.ActionTypes.LOAD_LOCATIONS_SUCCESS
        ),
        concatMap((data) => [
            new featureActions.LoadTicketAction(data.payload)
        ])
    ));

    loadTicketsEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadTicketAction>(
            featureActions.ActionTypes.LOAD_TICKETS
        ),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
            this.store$.select(TicketStoreSelectors.selectSelectedFilter),
            this.store$.select(TicketStoreSelectors.hideAvailableAssignedPMTaskDefault)
        ),
        switchMap((data) => {
            const filter = data[2] ? data[2] : new Filter();
            const agency = data[1];
            filter.agencyId = agency?.id || localStorage.getItem(localStorageCompositeKeys.defaultTenantAgency(this.userIdentity.tenantId)) || '';
            const current = new Date();
            filter.paging = false;

            if (data[3]) {
                filter.hideAvailableAssignedPMTask = true;
            }          

            return this.ticketService
                .getAllTicketList(filter)
                .pipe(
                    concatMap(
                        items => {                                     
                            const call = items.items?.filter(t => t.type && t.type.category === TicketTypeCategory.Call) ?? [];
                            const work = items.items?.filter(t => t.type && t.type.category === TicketTypeCategory.Work) ?? [];
                            const allPms = items.items?.filter(t => t.type && t.type.category === TicketTypeCategory.PM) ?? [];
                            let pms: Ticket[] = [];

                            if (filter.hideAvailableAssignedPMTask) {
                                pms = allPms.filter(pm => pm.status != TicketStatus.Assigned && pm.status != TicketStatus.Available)
                            }                            

                            return [
                              new featureActions.LoadCallTicketSuccessAction(
                                ToTicketFeatures(data[0].payload, call)
                              ),
                              new featureActions.LoadTicketSuccessAction(
                                ToWorkTicketFeatures(data[0].payload, work)
                              ),
                              new featureActions.LoadPMSSuccessAction({
                                pms: ToPMFeatures(data[0].payload, pms),
                                allPms: ToPMFeatures(data[0].payload, allPms)
                              }),
                            ];
                        }
                    ),
                    catchError(error =>
                        observableOf(new featureActions.ClearTicketsAction)
                    )
                )
        })
    ));

    setLocationFilterEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SetLocationFilterAction>(
            featureActions.ActionTypes.SET_LOCATION_FILTER
        ),
        concatMap((data) => [
            new featureActions.LoadLocationsAction
        ])
    ));

    loadLocationsByIdsEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SetSelectedLocationOnMapAction>(
            featureActions.ActionTypes.SET_SELECTED_LOCATION_ON_MAP
        ),
        switchMap((data) => {
            return this.locationService
                .getLocationsInstances(data.payload.location)
                .pipe(
                    map(
                        result =>{
                        let location = result && result.items && result.items.length > 0 ? result.items[0] : [];
                        return new featureActions.SetSelectedLocationOnMapActionSuccess({ location: location })
                        }
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadMapConfigFailureAction({ error }))
                    )
                )
        })
    ));
}
