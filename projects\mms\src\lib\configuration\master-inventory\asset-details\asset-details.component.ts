import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSidenavModule, MatDrawer } from '@angular/material/sidenav';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Store } from '@ngrx/store';
import { Observable, Subscription } from 'rxjs';
import { MasterInventoryItem } from '../master-inventory.model';
import { MasterInventoryStoreState, MasterInventoryStoreSelectors, MasterInventoryStoreActions, GridStoreActions } from '../../../root-store';
import { Field } from '../../shared/asset-field/field.model';
import { InventoryType } from '../inventory-type.model';
import { map, first, filter } from 'rxjs/operators';
import { FieldTypeEnum } from '../../shared/asset-field/field-type.enum';
import { Statistics } from './statistics';
import { Column } from '../../../root-store/shared-store/material-grid-store/state';
import { Formats } from '../../../core/formats';
import { GridModule } from '../../../shared/grid/grid.module';
import { BlockUIDirective } from '../../../shared/block-ui/block-ui.directive';
import { MapSidebarComponent } from '../../shared/map-sidebar/map-sidebar.component';
import { SecurePipe } from '../../../shared/utility/secure.pipe';
import { FieldValue } from '../../shared/asset-field/field-value.model';

@Component({
    selector: 'mms-asset-details',
    templateUrl: './asset-details.component.html',
    styleUrls: ['./asset-details.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        CommonModule,
        MatButtonModule,
        MatIconModule,
        MatSidenavModule,
        MatTabsModule,
        MatCheckboxModule,
        MatTooltipModule,
        GridModule,
        BlockUIDirective,
        MapSidebarComponent,
        SecurePipe,
    ]
})
export class AssetDetailsComponent implements OnInit, OnDestroy {
    isLoading$!: Observable<boolean>;
    selectedInventoryItem$!: Observable<MasterInventoryItem | undefined>;
    selectedInventoryType$!: Observable<InventoryType | undefined>;
    selectedInventoryLocation$!: Observable<MasterInventoryItem | undefined>;
    selectedInventorySub!: Subscription;
    goBackSubscription!: Subscription;
    selectedInventoryItem!: Subscription;
    selectedInventoryType!: Subscription;
    fields!: Field[];
    statistics!: Statistics;
    mapPoints$ = this.store$.select(MasterInventoryStoreSelectors.selectSelectedMapPoint);
    mapConfig$ = this.store$.select(MasterInventoryStoreSelectors.selectMapConfig);
    columns: Column[] = [
        { name: 'createdDateTime', hidden: false },
        { name: 'action', hidden: false },
        { name: 'reason', hidden: false },
    ];
    key = 'id';
    gridStoreSelector: any = MasterInventoryStoreSelectors.selectHistGrid;
    id = MasterInventoryStoreState.ASSET_HISTORY_GRID_ID;
    DateFormatEnum = Formats;

    constructor(
        private matDrawer: MatDrawer,
        private store$: Store<MasterInventoryStoreState.State>,
    ) {
    }

    ngOnInit() {
        this.fields = [];
        this.statistics = new Statistics();

        this.isLoading$ = this.store$.select(
            MasterInventoryStoreSelectors.selectInventoryItemLoading
        );

        this.selectedInventoryItem$ = this.store$.select(
            MasterInventoryStoreSelectors.selectSelectedInventoryItem
        );

        this.selectedInventoryType$ = this.store$.select(
            MasterInventoryStoreSelectors.selectedInventoryItemType
        );

        this.selectedInventoryLocation$ = this.store$.select(
            MasterInventoryStoreSelectors.selectedInventoryItemLocation
        );

        this.selectedInventorySub = this.selectedInventoryItem$.subscribe((inventoryItem) => {
            if (!inventoryItem) return; 
                
            inventoryItem.fieldValues && inventoryItem.fieldValues.forEach((fieldValue: FieldValue) => {
                const field = new Field();
                field.name = fieldValue.name;
                field.value = fieldValue.value;
                field.type = fieldValue.type;
                field.id = fieldValue.id;
                this.fields.push(field);
            });
            this.store$.dispatch(new GridStoreActions.GridLoadItemsSuccessAction(this.id, { items: inventoryItem.history ?? [] }));
            
        });

        this.calculateStatistics();
    }

    ngOnDestroy() {
        if (this.selectedInventorySub) {
            this.selectedInventorySub.unsubscribe();
        }
        if (this.goBackSubscription) {
            this.goBackSubscription.unsubscribe();
        }
        if (this.selectedInventoryItem) {
            this.selectedInventoryItem.unsubscribe();
        }
        if (this.selectedInventoryType) {
            this.selectedInventoryType.unsubscribe();
        }
    }

    close() {
        this.matDrawer.close();
    }

    goBack() {
        this.goBackSubscription = this.store$.select(MasterInventoryStoreSelectors.selectedInventoryItemLocation).pipe(map((location) => {
            if(!location) return;
            this.store$.dispatch(new MasterInventoryStoreActions.ShowLocationDetailsAction({ locationId: location.id! }))
        }, first())).subscribe();
    }

    isFileType(field: any) {
        return field.type === FieldTypeEnum.File;
    }

    private getContentType(fileName: string) {
        const types = this.getMimeTypes();
        const extension = '.' + fileName.substr(fileName.lastIndexOf('.') + 1);
        return (types as any)[extension];
    }

    private getMimeTypes() {
        return {
            '.txt': 'text/plain',
            '.pdf': 'application/pdf',
            '.doc': 'application/vnd.ms-word',
            '.docx': 'application/vnd.ms-word',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.csv': 'text/csv',
            '.zip': 'application/zip'
        };
    }

    getNoFilesFields() {
        return this.fields.filter(x => !this.isFileType(x))
    }

    getFilesFields() {
        return this.fields.filter(x => this.isFileType(x))
    }

    calculateStatistics() {
        this.selectedInventoryType = this.selectedInventoryType = this.selectedInventoryType$.pipe(
            filter((inventoryType): inventoryType is InventoryType => inventoryType !== undefined)
        ).subscribe((inventoryType) => {
            this.statistics.setInventoryTypeData(inventoryType);
        });

        this.selectedInventoryItem = this.selectedInventoryItem = this.selectedInventoryItem$.pipe(
            filter((inventoryItem): inventoryItem is MasterInventoryItem => inventoryItem !== null)
        ).subscribe((inventoryItem: MasterInventoryItem) => {
            this.statistics.setInventoryItemData(inventoryItem);
        });
    }
}
