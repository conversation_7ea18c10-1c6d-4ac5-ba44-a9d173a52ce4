import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, select } from '@ngrx/store';
import { of as observableOf, pipe } from 'rxjs';
import { catchError, map, switchMap, tap, withLatestFrom, exhaustMap } from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state'
import * as featureSelectors from './selectors'

import { Router } from '@angular/router';
import { ManufacturersService } from '../../configuration/manufacturers/manufacturers.service';
import { Page } from '../../core/page.service';
import { FormResetAction } from '../../shared/forms/connect-form-ngrx';
import { Manufacturer } from '../../configuration/manufacturers/manufacturer.model';

@Injectable()
export class ManufacturerStoreEffects {
  constructor(
    private router: Router,
    private manufacturerService: ManufacturersService,
    private actions$: Actions,
    private store$: Store<featureState.State>,
    private page: Page) { }

  loadRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(
      featureActions.ActionTypes.LOAD_REQUEST
    ),
    switchMap(action =>
      this.manufacturerService
        .getManufacturers()
        .pipe(
          map(
            items =>
              new featureActions.LoadSuccessAction({
                items
              })
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  saveRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveRequestAction>(
      featureActions.ActionTypes.SAVE_REQUEST
    ),
    exhaustMap((action) =>
      this.manufacturerService
        .addManufacturer(action.payload.item)
        .pipe(
          map(
            (manufacturer) => {
              return new featureActions.SaveSuccessAction({
                item: manufacturer!
              });
            }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  updateRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateRequestAction>(
      featureActions.ActionTypes.UPDATE_REQUEST
    ),
    exhaustMap((action) =>
      this.manufacturerService
        .updateManufacturer(action.payload.item)
        .pipe(
          map(
            () =>
              new featureActions.UpdateSuccessAction({ item: { id: action.payload.item.id!, changes: action.payload.item } })
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  updateRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateSuccessAction>(
      featureActions.ActionTypes.UPDATE_SUCCESS
    ),
    tap(() => {
      this.page.notification.show('Manufacturer updated');
    })
  ), { dispatch: false });

  saveRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveSuccessAction>(
      featureActions.ActionTypes.SAVE_SUCCESS
    ),
    map(() => {
      this.page.notification.show('Manufacturer added');
      return new FormResetAction();
    })
  ));

  confirmDeleteManufacturerEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmDeleteManufacturerAction>(
      featureActions.ActionTypes.CONFIRM_DELETE_MANUFACTURER
    ),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectedManufacturer))),
    exhaustMap(([action, selectedManufacturer]: [featureActions.ConfirmDeleteManufacturerAction, Manufacturer]) =>
      this.page
        .confirm.show('Are you sure you want to delete this manufacturer?', 'Are you sure?')
        .pipe(
          map(
            (result) => {
              if (result) {
                return new featureActions.DeleteRequestAction();
              }
              return new featureActions.NoAction();
            }
          )
        )
    )
  ));

  deleteRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteRequestAction>(
      featureActions.ActionTypes.DELETE_REQUEST
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedManufacturer)),
    ),
    switchMap(([action, selectedManufacturer]: [featureActions.DeleteRequestAction, Manufacturer]) =>
      this.manufacturerService
        .deleteManufacturer(selectedManufacturer.id!)
        .pipe(
          map(
            () =>
              new featureActions.DeleteSuccessAction({
                itemId: selectedManufacturer.id!
              })
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  deleteSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteSuccessAction>(
      featureActions.ActionTypes.DELETE_SUCCESS
    ),
    pipe(
      map(() => {
        this.page.notification.show('Manufacturer deleted');
      }),
      switchMap(res => [
          new FormResetAction(),
          new featureActions.AddNewAction()
      ])
    )
  ));

  addNewEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AddNewAction>(
      featureActions.ActionTypes.ADD_NEW
    ),
    tap(() => {
      this.router.navigate(['/configuration/manufacturers']);
    })
  ), { dispatch: false });

  saveEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveAction>(
      featureActions.ActionTypes.SAVE
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedManufacturer)),
    ),
    map(([action, selectedManufacturer]: [featureActions.SaveAction, Manufacturer]) => {
      if (selectedManufacturer == null) {
        return new featureActions.SaveRequestAction({ item: action.payload.item })
      } else {
        const manufacturer = action.payload.item;
        manufacturer.id = selectedManufacturer.id;
        return new featureActions.UpdateRequestAction({ item: manufacturer })
      }
    })
  ));
}
