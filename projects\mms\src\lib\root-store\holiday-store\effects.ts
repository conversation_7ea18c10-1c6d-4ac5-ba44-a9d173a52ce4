import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Action, Store } from '@ngrx/store';
import { of as observableOf, of } from 'rxjs';
import { catchError, map, switchMap, withLatestFrom, exhaustMap, concatMap } from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state'
import * as featureSelectors from './selectors'

import { Router } from '@angular/router';
import { Page } from '../../core/page.service';
import { HolidaysService } from '../../payroll-and-billing/holidays/services/holidays.service';
import { GridStoreActions } from '../shared-store/material-grid-store';

@Injectable()
export class HolidayStoreEffects {
    constructor(
        private router: Router,
        private holidayService: HolidaysService,
        private actions$: Actions,
        private store$: Store<featureState.State>,
        private page: Page) { }

    loadHolidaysRequest$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadRequestAction>(featureActions.ActionTypes.LOAD_REQUEST),
        switchMap((_: featureActions.LoadRequestAction) => {
            return this.holidayService.getHolidays().pipe(
                concatMap(
                    (items) => {
                        const actions: Action[] = [
                            new featureActions.LoadSuccessAction({ items }),
                            new GridStoreActions.GridLoadItemsSuccessAction(
                                featureState.HOLIDAYS_GRID_ID,
                                { items }
                            )
                        ];

                        return actions;
                    }
                ),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        })
    ));

    saveRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SaveRequestAction>(
            featureActions.ActionTypes.SAVE_REQUEST
        ),
        exhaustMap((action) =>
            this.holidayService
                .addHoliday(action.payload.item)
                .pipe(
                    concatMap((holiday): Action[] => {
                        return [
                            new featureActions.SaveSuccessAction({
                                item: holiday!
                            }),
                            new featureActions.LoadRequestAction()
                        ];

                    }
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    updateRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateRequestAction>(
            featureActions.ActionTypes.UPDATE_REQUEST
        ),
        exhaustMap((action) =>
            this.holidayService
                .updateHoliday(action.payload.item)
                .pipe(
                    switchMap(
                        (): Action[] => [
                            new featureActions.UpdateSuccessAction({ item: { id: action.payload.item.id!, changes: action.payload.item } }),
                            new featureActions.LoadRequestAction()]
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    confirmDeleteHolidayEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ConfirmDeleteAction>(
            featureActions.ActionTypes.CONFIRM_DELETE
        ),
        exhaustMap((action) =>
            this.page
                .confirm.show('Are you sure you want to delete this holiday?', 'Are you sure?')
                .pipe(
                    map(
                        (result): Action => {
                            if (result) {
                                return new featureActions.DeleteRequestAction({ itemId: action.payload.itemId });
                            }
                            return new featureActions.NoAction();
                        }
                    )
                )
        )
    ));

    deleteRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteRequestAction>(
            featureActions.ActionTypes.DELETE_REQUEST
        ),
        switchMap((action) =>
            this.holidayService
                .deleteHoliday(action.payload.itemId)
                .pipe(
                    map(
                        (): Action =>
                            new featureActions.DeleteSuccessAction({
                                itemId: action.payload.itemId
                            })
                    ),
                    catchError(error =>
                        observableOf(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    deleteSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteSuccessAction>(
            featureActions.ActionTypes.DELETE_SUCCESS
        ),
        map((action): Action => {
            this.page.notification.show('Holiday deleted');
            return new featureActions.LoadRequestAction();
        })
    ));

    searchTextChangedAction$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SearchTextChangedAction>(
            featureActions.ActionTypes.SEARCH_TEXT_CHANGED
        ),
        withLatestFrom(this.store$.select(featureSelectors.getHolidays)),
        map(([action, holidays]): Action => {
            const items = holidays.filter(x => !action.payload || x.name && x.name.toLocaleLowerCase().indexOf(action.payload.toLocaleLowerCase()) > -1);
            return new GridStoreActions.GridLoadItemsSuccessAction(
                featureState.HOLIDAYS_GRID_ID,
                { items }
            )
        }
        ),
        catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
        )
    ));
}
