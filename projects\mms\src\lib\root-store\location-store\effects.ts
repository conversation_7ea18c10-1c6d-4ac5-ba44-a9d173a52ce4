import { LocationTypeChecklist } from './../../configuration/shared/models/location-type-checklist.model';
import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, Action, select } from '@ngrx/store';
import { LocationData } from '../../configuration/tickets/location-data.model';
import { Checklist } from '../../configuration/pm-checklist/checklist.model';

import * as featureActions from './actions';
import * as featureState from './state';
import * as featureSelectors from './selectors';
import * as featureProjectsSchedulerSelectors from '../projects-scheduler-store/selectors';
import * as featureTicketsSelectors from '../ticket-store/selectors';

import { forkJoin, Observable, of, pipe } from 'rxjs';
import { switchMap, map, catchError, exhaustMap, withLatestFrom, tap } from 'rxjs/operators';
import { LocationService } from '../../configuration/type-and-field-definitions/locations/locations.service';
import { Page } from '../../core/page.service';
import { FormResetAction } from '../../shared/forms/connect-form-ngrx';
import { LocationType } from '../../configuration/type-and-field-definitions/locations/locations-manage/location-type.model';
import { Field } from '../../configuration/shared/asset-field/field.model';
import { IGridStoreEffects } from '../shared-store/material-grid-store/effects';
import { GridStoreActions, GridStoreState } from '../shared-store/material-grid-store';
import { Router } from '@angular/router';
import { GridLoadItemsAction } from '../shared-store/material-grid-store/actions';
import { RouterStoreSelectors } from '../router-store';
import { TicketStoreActions } from '../ticket-store';
import { ApprovalStatus } from '../../configuration/tickets/tickets-enum/ticket-status.enum';
import { Ticket } from '../../configuration/tickets/tickets.model';

@Injectable()
export class LocationStoreEffects implements IGridStoreEffects {
  constructor(
    private actions$: Actions,
    private locationService: LocationService,
    private store$: Store<featureState.State>,
    private page: Page,
    private router: Router,
  ) { }

  loadRequestEffect$ = createEffect(() => 
    this.actions$.pipe(
      ofType<featureActions.LoadRequestAction>(
        featureActions.ActionTypes.LOAD_REQUEST
      ),
      switchMap(action =>
        this.locationService.getLocations().pipe(
          map(items => {
            return new featureActions.LoadSuccessAction(items);
          }),
          catchError(error => of(new featureActions.LoadFailureAction({ error })))
        )
      )
    )
  );

  loadSuccessEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.LoadSuccessAction>(
        featureActions.ActionTypes.LOAD_SUCCESS
      ),
      map((action) => new GridLoadItemsAction(featureState.LOCATION_TYPE_GRID_ID))
    )
  );

  saveEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.SaveAction>(
        featureActions.ActionTypes.SAVE
      ),
      withLatestFrom(
        this.store$.pipe(select(featureSelectors.selectedLocation)),
        this.store$.select(featureSelectors.selectGrid)
      ),
      map(([action, selecteLocation, grid]: [featureActions.SaveAction, LocationType | undefined, GridStoreState.State<Field>]) => {
        const locationType = action.payload;
        locationType.fields = grid.items;

        if (selecteLocation == null) {
          return new featureActions.SaveRequestAction(locationType);
        } else {
          locationType.id = selecteLocation.id;
          return new featureActions.UpdateRequestAction(locationType)
        }
      })
    )
  );

  saveRequestEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.SaveRequestAction>(
        featureActions.ActionTypes.SAVE_REQUEST
      ),
      exhaustMap((action) =>
        this.locationService
          .addLocation(action.payload)
          .pipe(
            map(
              (location) => {
                if (!location) {
                  throw new Error('Failed to create location');
                }
                return new featureActions.SaveSuccessAction(location);
              }
            ),
            catchError(error =>
              of(new featureActions.LoadFailureAction({ error }))
            )
          )
      )
    )
  );

  updateRequestEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.UpdateRequestAction>(
        featureActions.ActionTypes.UPDATE_REQUEST
      ),
      exhaustMap((action) =>
        this.locationService
          .updateLocation(action.payload)
          .pipe(
            map(
              () =>
                new featureActions.UpdateSuccessAction({ id: action.payload.id, changes: action.payload })
            ),
            catchError(error =>
              of(new featureActions.LoadFailureAction({ error }))
            )
          )
      )
    )
  );

  updateRequestSuccessEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.UpdateSuccessAction>(
        featureActions.ActionTypes.UPDATE_SUCCESS
      ),
      tap(() => {
        this.page.notification.show('Location type updated');
      })
    ),
    { dispatch: false }
  );

  saveRequestSuccessEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.SaveSuccessAction>(
        featureActions.ActionTypes.SAVE_SUCCESS
      ),
      map(() => {
        this.page.notification.show('Location type added');
      }),
      switchMap(res => [
        new FormResetAction(),
        new GridStoreActions.GridLoadItemsAction(featureState.LOCATION_TYPE_GRID_ID)
      ])
    )
  );

  locationCanBeDeletedEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.CanBeDeletedAction>(
        featureActions.ActionTypes.CAN_BE_DELETED
      ),
      withLatestFrom(this.store$.pipe(select(featureSelectors.selectedLocation))),
      exhaustMap(([action, selectedLocation]: [featureActions.CanBeDeletedAction, LocationType | undefined]) => {
        if (!selectedLocation) {
          return of(new featureActions.LoadFailureAction({ error: 'No location selected' }));
        }
        return this.locationService
          .canLocationTypeBeDeleted(selectedLocation.id)
          .pipe(
            map(
              (response) => {
                if (response) {
                  return new featureActions.ConfirmDeleteAction();
                } else {
                  this.page.alert.error('In order to delete this location type, you need to remove all instances associated with it.', 'Warning');
                  return new featureActions.NoAction();
                }
              }
            ),
            catchError(error =>
              of(new featureActions.LoadFailureAction({ error }))
            )
          );
      })
    )
  );

  confirmDeleteLocationEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.ConfirmDeleteAction>(
        featureActions.ActionTypes.CONFIRM_DELETE
      ),
      withLatestFrom(this.store$.pipe(select(featureSelectors.selectedLocation))),
      exhaustMap(([action, selectedLocation]: [featureActions.ConfirmDeleteAction, LocationType | undefined]) => {
        if (!selectedLocation) {
          return of(new featureActions.LoadFailureAction({ error: 'No location selected' }));
        }
        return this.page
          .confirm.show('Are you sure you want to delete this location type?', 'Are you sure?')
          .pipe(
            map(
              (result) => {
                if (result) {
                  return new featureActions.DeleteRequestAction();
                }
                return new featureActions.NoAction();
              }
            )
          );
      })
    )
  );

  deleteRequestEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.DeleteRequestAction>(
        featureActions.ActionTypes.DELETE_REQUEST
      ),
      withLatestFrom(
        this.store$.pipe(select(featureSelectors.selectedLocation)),
      ),
      switchMap(([action, selectedLocation]: [featureActions.DeleteRequestAction, LocationType | undefined]) => {
        if (!selectedLocation) {
          return of(new featureActions.LoadFailureAction({ error: 'No location selected' }));
        }
        return this.locationService
          .deleteLocation(selectedLocation.id)
          .pipe(
            map(
              () =>
                new featureActions.DeleteSuccessAction(selectedLocation.id)
            )
          );
      })
    )
  );

  deleteSuccessEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.DeleteSuccessAction>(
        featureActions.ActionTypes.DELETE_SUCCESS
      ),
      pipe(
        map(() => {
          this.page.notification.show('Location type deleted');
        }),
        switchMap(res => [
          new FormResetAction(),
          new GridStoreActions.GridLoadItemsAction(featureState.LOCATION_TYPE_GRID_ID),
        ])
      )
    )
  );

  addNewEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.AddNewAction>(
        featureActions.ActionTypes.ADD_NEW
      ),
      tap(() => {
        this.router.navigate(['/configuration/types/locations']);
      })
    ),
    { dispatch: false }
  );

  gridLoadItems$ = createEffect(() =>
    this.gridLoadItems(GridStoreActions.ActionTypes.GRID_LOAD_ITEMS(featureState.LOCATION_TYPE_GRID_ID))
  );

  gridLoadItemsSuccess$ = createEffect(() =>
    this.gridLoadItemsSuccess(GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_SUCCESS(featureState.LOCATION_TYPE_GRID_ID))
  );

  gridLoadItemsFailed$ = createEffect(() =>
    this.gridLoadItemsFailed(GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_FAILED(featureState.LOCATION_TYPE_GRID_ID))
  );

  loadLocationTypesForPMTicketEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.LoadLocationTypesForPMTicket>(
        featureActions.ActionTypes.LOAD_LOCATION_TYPES_FOR_PM_TICKET
      ),
      withLatestFrom(this.store$.select(featureSelectors.getLocationTypesChecklist)),
      map(([action, locationTypesChecklists]) => {
        if (locationTypesChecklists && locationTypesChecklists.length) {
          return new GridStoreActions.GridLoadItemsSuccessAction(featureState.LOCATION_TYPE_CHECKLIST_GRID_ID, { items: locationTypesChecklists })
        } else {
          return new featureActions.LoadLocationTypesForPMTicketRequest({regions: action.payload.regions, locations: action.payload.locations})
        }
      })
    )
  );

  loadLocationTypesForPMTicketRequestEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.LoadLocationTypesForPMTicketRequest>(
        featureActions.ActionTypes.LOAD_LOCATION_TYPES_FOR_PM_TICKET_REQUEST
      ),
      switchMap(action =>
        this.locationService.getLocationTypesByLocationsAndRegions(action.payload.regions, action.payload.locations).pipe(
          map(items => {
            return new featureActions.LoadLocationTypesForPMTicketSuccess({ locationTypes: items });
          }),
          catchError(error => of(new featureActions.LoadFailureAction({ error })))
        )
      )
    )
  );

  loadLocationTypesForPMTicketSuccessEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.LoadLocationTypesForPMTicketSuccess>(
        featureActions.ActionTypes.LOAD_LOCATION_TYPES_FOR_PM_TICKET_SUCCESS
      ),
      withLatestFrom(
        this.store$.select(featureProjectsSchedulerSelectors.selectSelectedSchedule),
        this.store$.select(featureTicketsSelectors.selectSelectedTicket),
        this.store$.select(RouterStoreSelectors.getRouterStoreState),
      ),
      map(([action, selectedSchedule, selectedTicket, router]) => {
        const locationTypeChecklists: LocationTypeChecklist[] = [];

        if (router.state.url.includes('/edit-schedule')){
          action.payload.locationTypes.forEach(locationType => {
            const scheduleChecklist = selectedSchedule && selectedSchedule.locationTypesChecklists ? selectedSchedule.locationTypesChecklists.find(x => x.locationType?.id === locationType.id) : null;
            const locationTypeChecklist: LocationTypeChecklist = {
              locationType: locationType,
              checklist: scheduleChecklist ? scheduleChecklist.checklist : {} as Checklist
            };
            locationTypeChecklists.push(locationTypeChecklist);
          });
        }
        else if (router.state.url.includes('/edit-ticket'))
        {
          action.payload.locationTypes.forEach(locationType => {
            const locationTypeChecklist: LocationTypeChecklist = {
              locationType: locationType,
              checklist: selectedTicket.checklist || {} as Checklist
            };
            locationTypeChecklists.push(locationTypeChecklist);
          });
        }
        else 
        {
          action.payload.locationTypes.forEach(locationType => {
            const locationTypeChecklist: LocationTypeChecklist = {
              locationType,
              checklist: {} as Checklist
            };
            locationTypeChecklists.push(locationTypeChecklist);
          });
        }
        return new GridStoreActions.GridLoadItemsSuccessAction(featureState.LOCATION_TYPE_CHECKLIST_GRID_ID, { items: locationTypeChecklists });
      })
    )
  );

  loadLocationsofRegionsTicketRequestEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType<featureActions.LoadLocationsOfRegionsTicketRequest>(
        featureActions.ActionTypes.LOAD_LOCATIONS_OF_REGIONS_TICKET_REQUEST
      ),
      withLatestFrom(
        this.store$.select(RouterStoreSelectors.getRouterStoreState),
      ),
      switchMap(([action, router]) => {
        const regionIds1 = action.ticketModalSave.regions ? action.ticketModalSave.regions.map((x: any) => x.id) : [];
        const action1 = action;
        const regionsRequest = this.locationService.getLocationsOfRegions(regionIds1);
        return forkJoin(regionsRequest).pipe(
          map(locationsFromRegions => {
            const listLocationsOfRegions = locationsFromRegions[0];
            const regionIds = action1.ticketModalSave.regions ? action1.ticketModalSave.regions.map((x: any) => x.id) : [];
            const allLocationIds = listLocationsOfRegions ? listLocationsOfRegions.concat(action1.ticketModalSave.locations || []) : (action1.ticketModalSave.locations || []);

            if (listLocationsOfRegions && listLocationsOfRegions[0] && !allLocationIds.includes(listLocationsOfRegions[0])) {
              allLocationIds.push(listLocationsOfRegions[0]);
            }
            else if (action1.ticketModalSave.locations && action1.ticketModalSave.locations[0] && !allLocationIds.includes(action1.ticketModalSave.locations[0])) {
              if (listLocationsOfRegions && listLocationsOfRegions[0]) {
                allLocationIds.push(listLocationsOfRegions[0]);
              }
              allLocationIds.pop();
            }
            else if (listLocationsOfRegions && listLocationsOfRegions[0] && allLocationIds.includes(listLocationsOfRegions[0]) && locationsFromRegions && locationsFromRegions.length > 0 && action1.ticketModalSave.locations) {
              allLocationIds.pop();
            }
            else if (listLocationsOfRegions && listLocationsOfRegions[0]) {
              allLocationIds.splice(allLocationIds.indexOf(listLocationsOfRegions[0]), 1);
            }

            const sumNumberLocations = allLocationIds.length;
            const locationData = {} as LocationData;

            return action1.ticketModalSave.createMultipleTickets
              ? sumNumberLocations > 1 
                ? new TicketStoreActions.ConfirmSaveAction({
                    ticket: {
                      ...action1.ticketModalSave.ticket,
                      locationData,
                      approvalStatus: ApprovalStatus.Current
                    } as Ticket,
                    locations: action1.ticketModalSave.locations,
                    regionIds: regionIds,
                    locationTypesChecklists: [] as LocationTypeChecklist[],
                    sumNumberLocations
                  })
                : new TicketStoreActions.SaveMultipleRequestAction({
                    ticket: {
                      ...action1.ticketModalSave.ticket,
                      locationData,
                      approvalStatus: ApprovalStatus.Current
                    } as Ticket,
                    locations: action1.ticketModalSave.locations,
                    regionIds: regionIds,
                    locationTypesChecklists: [] as LocationTypeChecklist[],
                    sumNumberLocations
                  })
              : new TicketStoreActions.SaveAction({
                  ticket: action1.ticketModalSave.ticket || {} as Ticket,
                  locationTypes: action1.ticketModalSave.locationTypes || [],
                });
          })
        );
      })
    )
  );

  // Helper methods
  public gridLoadItems(actionType: string): Observable<Action> {
    return this.actions$.pipe(
      ofType<GridStoreActions.GridLoadItemsAction<Field>>(actionType),
      withLatestFrom(this.store$.select(featureSelectors.selectedLocation)),
      withLatestFrom(this.store$.select(featureSelectors.selectDefaultSharedFields)),
      map(([[action, location], defaultFields]) => {
        if (location) {
          const fields: Field[] = [];
          location.fields.forEach(function(value) {
            fields.push(Object.assign({}, value));
          });
          return new GridStoreActions.GridLoadItemsSuccessAction(featureState.LOCATION_TYPE_GRID_ID, { items: fields });
        } else {
          const fields: Field[] = [];
          if (defaultFields != undefined) {
            defaultFields.forEach(item => {
              if(item != null && item != undefined) {
                fields.push(item);
              }
            });
            return new GridStoreActions.GridLoadItemsSuccessAction(featureState.LOCATION_TYPE_GRID_ID, { items: fields });
          } else {
            return new GridStoreActions.GridLoadItemsSuccessAction(featureState.LOCATION_TYPE_GRID_ID, { items: defaultFields || [] });
          }
        }
      })
    );
  }

  public gridLoadItemsSuccess(actionType: string): Observable<GridStoreActions.Actions> {
    return this.actions$.pipe(
      ofType(actionType),
      map((action) => new GridStoreActions.GridNoAction)
    );
  }

  public gridLoadItemsFailed(actionType: string): Observable<GridStoreActions.Actions> {
    return this.actions$.pipe(
      ofType(actionType),
      map((action) => new GridStoreActions.GridNoAction)
    );
  }
}




