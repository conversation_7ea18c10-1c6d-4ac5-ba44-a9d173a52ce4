import { Validator, AbstractControl, ValidationErrors } from '@angular/forms'

export class GreaterThanFieldValidator implements Validator {

    constructor(
        private controlToCompare: string,
        private controlToValidateName: string,
        private controlToCompareName: string
    ) { }

    validate(controlToValidate: AbstractControl): ValidationErrors | null {
        const toValidate = controlToValidate.value;
        const toCompare = controlToValidate.root.get(this.controlToCompare);

        if (toValidate !== null && toCompare !== null && toValidate >= toCompare.value) {
          if (toCompare.invalid) {
                toCompare.updateValueAndValidity({ onlySelf: true, emitEvent: false });
          }

          return null;
        }
        if (toValidate === null || toCompare === null) {
          return null;
        }

        return {
            greaterThanField: {
                greaterThanField: true,
                controlToValidateName: this.controlToValidateName,
                controlToCompareName: this.controlToCompareName
            }
          };
    }
}
