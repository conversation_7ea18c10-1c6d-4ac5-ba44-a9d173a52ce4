import {
    createFeatureSelector, createSelector, MemoizedSelector
} from '@ngrx/store';
import { State, MainState } from './state';
import { RouterStoreSelectors } from '../router-store';
import { selectIdentityUsers } from '../user-store/selectors';


export const getError = (state: MainState): any => state.error;
export const getIsLoading = (state: MainState): boolean => !!state.isLoading;

export const selectEmployeeState: MemoizedSelector<object, State> = createFeatureSelector<State>('employee');
export const selectMainState: MemoizedSelector<object, MainState> = createSelector(
    selectEmployeeState,
    EmployeeState => EmployeeState.main
);

export const selectEmployeeIsLoading: MemoizedSelector<object, boolean> = createSelector(selectMainState, getIsLoading);

export const selectUsers = createSelector(
    selectMainState,
    (state) => state.users
);

export const selectedUser = createSelector(
    selectMainState,
    RouterStoreSelectors.getRouterStoreState,
    (state, routerState) => routerState && routerState.state.params['id'] && state.users.find( x => x.id === routerState.state.params['id'])
)

export const selectedEmployee = createSelector(
    selectMainState,
    (state) => state.selectedEmployee
)

export const selectSearchActive = createSelector(
    selectMainState,
    (state) => state.searchActive
);

export const selectSearchText = createSelector(
    selectMainState,
    (state) => state.searchText
);

export const selectGrid = createSelector(
    selectEmployeeState,
    (store) => store.grid
);

export const getTicketAssignTypes = createSelector(
    selectMainState,
    (store) => store.selectedEmployeeTicketAssignType
);

export const selectedFilteredEmployees = createSelector(
    selectSearchText,
    selectUsers,
    selectIdentityUsers,
    (searchText, employees, identityUsers ) => {
        var employee =  employees.filter(x => searchText == null || x.firstname!.toLocaleLowerCase().indexOf(searchText.toLocaleLowerCase()) > -1 ||  x.lastname!.toLocaleLowerCase().indexOf(searchText.toLocaleLowerCase()) > -1);
        var usersIsLockedOut = identityUsers.filter((x: any) => x.isLockedOut === false);
        var result = employee.filter(o => usersIsLockedOut.some((id: any) => o.id === id));
        return result;
    }
);

export const selectShiftGrid = createSelector(
    selectEmployeeState,
    (store) => store.shiftGrid
);

export const getEmployeesShifts = createSelector(
    selectMainState,
    selectIdentityUsers,
    (store, identityUsers) => {
        var usersIsLockedOut = identityUsers.filter((x: any) => x.isLockedOut === false);
        var result = store.employeeShifts.filter(o => usersIsLockedOut.some((id: any) => o.userId === id));
        return result;
    }
);

export const getEmployeesShiftsSearchText = createSelector(
    selectMainState,
    (store) => store.employeeShiftSearchText
);

export const getAssignedUsers = createSelector(
    selectMainState,
    (store) => store.assignedUsers
);



