import { Checklist } from '../../configuration/pm-checklist/checklist.model';
import { Region } from '../../configuration/regions/region.model';
import { Schedule } from '../../scheduler/schedule.model';
import { GridStoreState } from '../shared-store/material-grid-store';
import { Filter } from '../../scheduler/filter/filter.model';
import { CalendarView } from 'angular-calendar';

const calendarViewValue = localStorage.getItem('calendarView');

function getCalendarViewValue() : CalendarView {
        const key = (calendarViewValue!.charAt(0).toUpperCase() + calendarViewValue!.slice(1)) as keyof typeof CalendarView;
        return CalendarView[key];
}

export interface State {
    main: MainState;
    grid: GridStoreState.State<Schedule>;
}

export interface MainState {
    isLoading?: boolean;
    isSidebarLoading?: boolean;
    error?: any;
    showSidebar?: boolean;
    checklists: Checklist[],
    regions: Region[],
    schedules: Schedule[];
    allSchedules: Schedule[];
    selectedFilter?: Filter | null;
    createdEvent?: Schedule | null;
    calendarView: CalendarView;
}

export const initialState: MainState = {
        isLoading: false,
        error: null,
        showSidebar: false,
        checklists: [],
        regions: [],
        schedules: [],
        allSchedules: [],
        selectedFilter: null,
        createdEvent: null,
        calendarView: calendarViewValue ? getCalendarViewValue() : CalendarView.Month
};

export const GRID_ID = 'SCHEDULE_GRID';
