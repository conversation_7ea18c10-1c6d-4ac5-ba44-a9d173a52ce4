import { Action } from '@ngrx/store';
import { Region } from '../../configuration/regions/region.model';
import { MasterInventoryTypeModel } from '../../reports/assets-counts/master-inventory-type.model';
import { ProjectActivityFilterModel } from '../../reports/project-activity/project-activity-filter.model';
import { TicketsListData } from '../../configuration/tickets/tickets-list.model';


export enum ActionTypes {
    LOAD_TYPES_REQUEST = '[Project Activity Report] Load Types Request',
    LOAD_TYPES_FAILURE = '[Project Activity Report] Load Types Failure',
    LOAD_TYPES_SUCCESS = '[Project Activity Report] Load Types Success',
    LOAD_REGIONS_REQUEST = '[Project Activity Report] Load Regions Request',
    LOAD_REGIONS_FAILURE = '[Project Activity Report] Load Regions Failure',
    LOAD_REGIONS_SUCCESS = '[Project Activity Report] Load Regions Success',
    SHOW_FILTER = '[Project Activity Report] Show filter',
    RESET_FILTER = '[Project Activity Report] Reset Filter',
    SEARCH_REQUEST = '[Project Activity Report] Search Request',
    SEARCH_SUCCESS = '[Project Activity Report] Search Success',
    SEARCH_FAILURE = '[Project Activity Report] Search Failure',
    NO_ACTION = '[Project Activity Report] No Action',
    IS_LOADING = '[Project Activity Report] Is Loading',
}

export class LoadTypesRequestAction implements Action {
    readonly type = ActionTypes.LOAD_TYPES_REQUEST;
    constructor() {}
}

export class LoadTypesFailureAction implements Action {
    readonly type = ActionTypes.LOAD_TYPES_FAILURE;
    constructor(public payload: { error: string }) { }
}

export class LoadTypesSuccessAction implements Action {
    readonly type = ActionTypes.LOAD_TYPES_SUCCESS;
    constructor(public payload: { inventoryTypes: MasterInventoryTypeModel[] }) { }
}

export class LoadRegionsRequestAction implements Action {
    readonly type = ActionTypes.LOAD_REGIONS_REQUEST;
    constructor() {}
}

export class LoadRegionsFailureAction implements Action {
    readonly type = ActionTypes.LOAD_REGIONS_FAILURE;
    constructor(public payload: { error: string }) { }
}

export class LoadRegionsSuccessAction implements Action {
    readonly type = ActionTypes.LOAD_REGIONS_SUCCESS;
    constructor(public payload: { regions: Region[] }) { }
}

export class ShowFilterAction implements Action {
    readonly type = ActionTypes.SHOW_FILTER;
    constructor() {}
}

export class ResetFilterAction implements Action {
    readonly type = ActionTypes.RESET_FILTER;
    constructor() { }
}

export class SearchRequestAction implements Action {
    readonly type = ActionTypes.SEARCH_REQUEST;
    constructor(public payload: ProjectActivityFilterModel) { }
}

export class SearchSuccessAction implements Action {
    readonly type = ActionTypes.SEARCH_SUCCESS;
    constructor(public payload: TicketsListData) { }
}

export class SearchFailureAction implements Action {
    readonly type = ActionTypes.SEARCH_FAILURE;
    constructor(public payload: { error: string }) { }
}

export class CheckIfIsLoading implements Action {
    readonly type = ActionTypes.IS_LOADING;
    constructor() {};
}

export type Actions =
    LoadTypesRequestAction |
    LoadTypesFailureAction |
    LoadTypesSuccessAction |
    LoadRegionsRequestAction |
    LoadRegionsFailureAction |
    LoadRegionsSuccessAction |
    ResetFilterAction |
    SearchRequestAction |
    SearchSuccessAction |
    SearchFailureAction | 
    CheckIfIsLoading;
