import { Validator, AbstractControl, ValidationErrors } from '@angular/forms'

export class LessThanFieldValidator implements Validator {

    constructor(
        private controlToCompare: string,
        private controlToValidateName: string,
        private controlToCompareName: string
    ) { }

    validate(controlToValidate: AbstractControl): ValidationErrors | null {
        
        const toValidate = controlToValidate.value;
        const toCompare = controlToValidate.root.get(this.controlToCompare);

        if (toValidate !== null && toCompare !== null && toValidate <= toCompare.value) {
            if (toCompare.invalid) {
                toCompare.updateValueAndValidity({ onlySelf: true, emitEvent: false });
            }

            return null;
        }
        if (toValidate === null || toCompare?.value === null) {
            return null;
        }

        return {
            lessThanField: {
                lessThanField: true,
                controlToValidateName: this.controlToValidateName,
                controlToCompareName: this.controlToCompareName
            }
        };
    }
}
