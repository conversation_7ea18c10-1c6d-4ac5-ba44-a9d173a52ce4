import { createFeatureSelector, createSelector, MemoizedSelector } from '@ngrx/store';
import { featureAdapter, State } from './state';
import { UserAndGroup } from '../../configuration/shared/user-and-groups/user-and-group.model';
import { User } from '../../configuration/shared/user-and-groups/user.model';

// export const selectUserState: MemoizedSelector<object, State> = createFeatureSelector<State>('user');
// export const selectMainState: MemoizedSelector<object, State> = createSelector(
//     selectUserState,
//     State => State
// );
export const selectMainState: MemoizedSelector<object, State> = createFeatureSelector<State>('user');
export const selectAllUserAndGroupsItems: (state: object) => UserAndGroup[] = featureAdapter.getSelectors(selectMainState).selectAll;

export const selectUserById: MemoizedSelector<object, User> = createSelector(
    selectMainState,
    (store) => store.user!
)
export const getDefaultTaskUser: MemoizedSelector<object, User[]> = createSelector(
    selectMainState,
    (store) => store.dafaultTaskUser
)

export const selectUserWithRoles: MemoizedSelector<object, User> = createSelector(
    selectMainState,
    (store) => store.userWithRoles
)

export const getUserFromUsers = createSelector(
    selectAllUserAndGroupsItems,
    (users: UserAndGroup[], id: string) => {
        if (users && users.length) {
            if (id) {
                var user = users.find((user: UserAndGroup) => user.id === id);
                if (user) {
                    return user;
                }
            }
            return null;
        }
    }
)

export const selectIdentityUsers: MemoizedSelector<object, any> = createSelector(
    selectMainState,
    (store) => store.identityUsers
)