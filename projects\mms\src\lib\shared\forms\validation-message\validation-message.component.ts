import { Component, OnInit } from '@angular/core';
import { Ng<PERSON>ontrol } from '@angular/forms';
import { MatFormField } from '@angular/material/form-field';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'c-validation-message',
  templateUrl: './validation-message.component.html',
  standalone: true,
  imports: [CommonModule]
})
export class ValidationMessageComponent implements OnInit {
  formControl!: NgControl;

  constructor(private _matFormField: MatFormField) {
  }

  ngOnInit() {
    const control = this._matFormField._control?.ngControl;
    if (!control || !(control instanceof NgControl)) {
      return;
    }
    
    this.formControl = control;
  }
}
