import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>estroy, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';

import { Store } from '@ngrx/store';
import { Observable, Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';

import { Checklist } from './checklist.model';
import { PMChecklistStoreState, PMChecklistStoreSelectors, PMChecklistStoreActions } from '../../root-store/pm-checklist-store';
import { ChecklistItem } from '../pm-checklist-items/shared/checklist-item.model';
import { SchedulerStoreState, RouterStoreSelectors, AgencyStoreSelectors } from '../../root-store';
import { PermissionsEnum } from '../../core/auth/permissions.enum';
import { RouterReducerState } from '@ngrx/router-store';
import { RouterStateUrl } from '../../root-store/router-store/router-state.serializer';
import { Agency } from '../agencies/agency.model';
import { BlockUIDirective } from '../../shared/block-ui/block-ui.directive';
import { IdentityClientModule } from '@econolite/identity-client';

@Component({
  templateUrl: './pm-checklist.component.html',
  styleUrls: ['./pm-checklist.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatFormFieldModule,
    MatInputModule,
    BlockUIDirective,
    // IdentityClientModule
  ]
})
export class PmChecklistComponent implements OnInit, OnDestroy {
  isLoading$!: Observable<boolean>;
  isSearchActive$!: Observable<boolean>;
  checkLists$!: Observable<Checklist[]>;
  selectedCheckList$!: Observable<Checklist>;
  showCreateEventBtn$!: Observable<boolean>;
  showCreateEvent$!: Observable<boolean>;
  showChecklistItems$!: Observable<boolean>;
  Permissions = PermissionsEnum;
  routerState$: Observable<RouterReducerState<RouterStateUrl>> = this.store$.select(RouterStoreSelectors.getRouterStoreState);
  selectedAgency$ = this.store$.select(AgencyStoreSelectors.selectDefaultAgency);
  selectedAgency = new Agency();
  
  @ViewChild('searchInput', { static: false }) searchInput!: ElementRef;
  
  subscription: Subscription[] = [];


  constructor(
      private title: Title,
      private store$: Store<PMChecklistStoreState.State>,
      private router: Router,
      private schedulerStore$: Store<SchedulerStoreState.State>
  ) { }

  ngOnInit() {
    this.checkLists$ = this.store$.select(PMChecklistStoreSelectors.selectFilteredChecklists);
    this.isLoading$ = this.store$.select(PMChecklistStoreSelectors.selectChecklistIsLoading);
    this.isSearchActive$ = this.store$.select(PMChecklistStoreSelectors.selectSearchActive);
    this.selectedCheckList$ = this.store$.select(PMChecklistStoreSelectors.selectSelectedChecklist).pipe(filter((c): c is Checklist => c !== undefined));
    this.showCreateEvent$ = this.store$.select(PMChecklistStoreSelectors.selectShowSidebarCreateEvent);
    this.showChecklistItems$ = this.store$.select(PMChecklistStoreSelectors.selectShowSidebarItems);
    this.showCreateEventBtn$ = this.store$.select(PMChecklistStoreSelectors.selectShowSidebarCreateEvent);
    
    this.store$.dispatch(new PMChecklistStoreActions.LoadRequestAction());

    this.subscription.push(this.selectedCheckList$.subscribe((checklist: Checklist) => {
        let checklistItems: ChecklistItem [] = [];
        if (checklist) {
          checklistItems = checklist.checklistItems ?? [];
        }
        this.store$.dispatch(new PMChecklistStoreActions.SetSelectedChecklistItemsAction(checklistItems));
        this.store$.dispatch(new PMChecklistStoreActions.HideItemsAction)
    }))
    this.changeAgency();
    this.title.setTitle('Checklists');
  }



  ngOnDestroy() {
    this.subscription.forEach(x => x.unsubscribe());
  }

  add(): void {
      this.store$.dispatch(new PMChecklistStoreActions.AddNewAction());
  }

  delete() {
    this.store$.dispatch(new PMChecklistStoreActions.CanBeDeletedAction());
  }

  activateSearch() {
      this.store$.dispatch(new PMChecklistStoreActions.ActivateSearchAction());
      setTimeout(() => {
        this.searchInput.nativeElement.focus();
      }, 20);
  }

  cancelSearch() {
      this.store$.dispatch(new PMChecklistStoreActions.DeactivateSearchAction())
  }

  searchChage(value: string) {
      this.store$.dispatch(new PMChecklistStoreActions.SearchTextChangedAction(value))
  }

  createEvent() {
    this.store$.dispatch(new PMChecklistStoreActions.ShowSidebarCreateEventAction());
  }

  changeAgency() {
    this.selectedAgency$.subscribe((value) => {
      if (this.selectedAgency && this.selectedAgency.id && value && value.id && this.selectedAgency.id != value.id) {
        setTimeout(() => {
          this.store$.dispatch(new PMChecklistStoreActions.LoadRequestAction());
        }, 1600);
      }
      this.selectedAgency = value || new Agency();
    });
  }
}
