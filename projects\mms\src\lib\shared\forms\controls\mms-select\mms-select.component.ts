import { Component, OnInit, Output, EventEmitter, Input, OnDestroy, Optional, Self, ElementRef, HostBinding, OnChanges, DoCheck } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject } from 'rxjs';
import { FormControl, FormGroupDirective, NgControl, ControlValueAccessor, NgForm, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldControl, MatFormField, MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FocusMonitor } from '@angular/cdk/a11y';

export interface CanUpdateErrorState {
    errorState: boolean;
    errorStateMatcher: ErrorStateMatcher;
    updateErrorState(): void;
}

@Component({
    selector: 'mms-select',
    templateUrl: './mms-select.component.html',
    styleUrls: ['./mms-select.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        MatSelectModule,
        MatFormFieldModule,
        MatIconModule,
        MatButtonModule,
        MatTooltipModule
    ],
    providers: [
        {
            provide: MatFormFieldControl,
            useExisting: MmsSelectComponent
        }
    ]
})
export class MmsSelectComponent implements OnChanges, OnDestroy, DoCheck, ControlValueAccessor, MatFormFieldControl<string>, CanUpdateErrorState {
    // MatFormFieldControl
    static nextId = 0;
    private _uid = `mms-select-${MmsSelectComponent.nextId++}`;
    menuValue: string = '';
    stateChanges = new Subject<void>();
    controlType = 'mms-select';
    errorState = false;
    autofilled = false;
    focused = false;
    ngControl: NgControl;

    @HostBinding('attr.aria-describedby') describedBy = '';

    private _id: string = '';
    get id(): string {
        return this._id;
    }
    @Input()
    set id(value: string) {
        this._id = value || this._uid;
    }

    private _value: string = '';
    get value(): string {
        return this._value;
    }
    @Input()
    set value(value: string) {
        this._value = value;
        this.onChangeCallback(value);
        this.stateChanges.next();
    }

    @Input()
    get required(): boolean {
        return this._required;
    }
    set required(value: boolean) {
        this._required = !!value;
        this.stateChanges.next();
    }
    private _required = false;

    @Input()
    get disabled(): boolean {
        if (this.ngControl && this.ngControl.disabled !== null) {
            return this.ngControl.disabled;
        }
        return this._disabled;
    }
    set disabled(value: boolean) {
        this._disabled = !!value;

        // Browsers may not fire the blur event if the input is disabled too quickly.
        // Reset from here to ensure that the element doesn't become stuck.
        if (this.focused) {
            this.focused = false;
            this.stateChanges.next();
        }
    }
    private _disabled = false;

    @Input()
    get placeholder(): string {
        return this._placeholder;
    }
    set placeholder(value: string) {
        this._placeholder = value;
        this.stateChanges.next();
    }
    private _placeholder: string = '';

    @HostBinding('class.floating')
    get shouldLabelFloat(): boolean {
        return this.focused || !this.empty;
    }

    get empty(): boolean {
        return this.value == null;
    }
    // End MatFormFieldControl

    private _onDestroy = new Subject<void>();

    private _options: any[] = [];
    get options(): any[] {
        return this._options;
    }
    @Input() set options(options: any[]) {
        if (options) {
            this._options = options;
        }
    };

    @Input() errorStateMatcher: ErrorStateMatcher = new ErrorStateMatcher();
    @Input() disableClear = false;
    @Output() valueChange = new EventEmitter<string>();
    @Input() readonly = false;
    @Input() textMapper = (option: any) => {
        return option['name'];
    }
    @Input() valueMapper = (option: any) => {
        return option['id'];
    }
    @Input() shouldDisplayOption = (option: any) => {
        return true;
    }

    constructor(
        @Optional() @Self() ngControl: NgControl,
        @Optional() public parentForm: NgForm,
        @Optional() public parentFormGroup: FormGroupDirective,
        public defaultErrorStateMatcher: ErrorStateMatcher,
        private fm: FocusMonitor,
        private elRef: ElementRef<HTMLElement>,
        private matFormField: MatFormField
    ) {
        this.ngControl = ngControl;

        // Setting the value accessor directly (instead of using
        // the providers) to avoid running into a circular import.
        if (this.ngControl != null) { this.ngControl.valueAccessor = this; }

        // Force setter to be called in case id was not specified.
        this.id = this.id;

        // MatFormFieldControl
        fm.monitor(elRef.nativeElement, true).subscribe(origin => {
            this.focused = !!origin;
            this.stateChanges.next();
        });
        // end MatFormFieldControl
    }

     // MatFormFieldControl
     setDescribedByIds(ids: string[]) {
        this.describedBy = ids.join(' ');
    }

    onContainerClick(event: MouseEvent) {
        const eventTarget = (event.target as Element);
        const trigger = this.elRef.nativeElement.querySelector<HTMLElement>('mat-select > .mat-select-trigger');
        
        if (eventTarget.className.toLowerCase().includes('mat-form-field-infix') || eventTarget.tagName.toLowerCase() === 'mms-select') {
            trigger?.click();
        }
    }
    // end MatFormFieldControl

    // ControlValueAccessor
    onChangeCallback: (value: string | string[]) => void = () => { };
    onTouchedCallback: () => void = () => { };

    writeValue(val: string): void {
        if (val !== this._value) {
            this._value = val;
            this.stateChanges.next();
        }
    }

    registerOnChange(fn: any): void {
        this.onChangeCallback = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouchedCallback = fn;
    }
    // end ControlValueAccessor

    ngOnChanges() {
        this.stateChanges.next();
    }

    ngDoCheck() {
        if (this.ngControl) {
            this.updateErrorState();
        }
        this.readonly = (<HTMLElement>this.matFormField._elementRef.nativeElement).classList.contains('mat-form-field-disabled');
    }

    ngOnDestroy() {
        this._onDestroy.next();
        this._onDestroy.complete();
    }

    onOpenedChange(isOpen: boolean) {
        if (this.ngControl?.control) {
            this.ngControl.control.markAsTouched();
            this.updateErrorState();
        }
    }

    onSelectionChange(event: any) {
        this.value = event.value;
        this.valueChange.emit(this.value);
    }

    updateErrorState() {
        const oldState = this.errorState;
        const parent = this.parentFormGroup || this.parentForm;
        const matcher = this.errorStateMatcher || this.defaultErrorStateMatcher;
        const control = this.ngControl ? this.ngControl.control as FormControl : null;
        const newState = matcher.isErrorState(control, parent);

        if (newState !== oldState) {
            this.errorState = newState;
            this.stateChanges.next();
        }
    }

    clearValue() {
        this.value = '';
        this.focused = false;
    }
}
