import { createFeatureSelector, createSelector } from '@ngrx/store';
import { UserAndGroup } from '../../configuration/shared/user-and-groups/user-and-group.model';
import { featureAdapter, State } from './state';

export const selectOnCallScheduler = createFeatureSelector<State>('onCallScheduler');

export const selectMainState = createSelector(
    selectOnCallScheduler,
    state => state.main
);

export const allAreas = createSelector(
    selectMainState,
    (store) => featureAdapter.getSelectors().selectAll(store)
)

export const selectGrid = createSelector(
    selectOnCallScheduler,
    store => store.grid
);

export const selectIsGridPopulated = createSelector(
    selectGrid,
    grid => !!(grid && grid.items.length)
);

export const selectIsGridLoading = createSelector(
    selectGrid,
    (store) => store.isLoading
)

export const selectInitialLoad = createSelector(
    selectMainState,
    store => store.initialLoad
);

export const selectIsLoading = createSelector(
    selectMainState,
    store => store.isLoading
);

export const selectIsSidebarLoading = createSelector(
    selectMainState,
    (store) => store.isSidebarLoading
)

export const selectIsSidebarOpened = createSelector(
    selectMainState,
    (store) => store.isSidebarOpened
)

export const selectUsers = createSelector(
    selectMainState,
    (store) => store.users
)

export const selectAgencies = createSelector(
    selectMainState,
    (store) => store.agencies
)

export const selectRegions = createSelector(
    selectMainState,
    (store) => store.regions
)

export const selectAreas = createSelector(
    selectMainState,
    (store) => store.areas
)

export const selectAreaVisible = createSelector(
    selectMainState,
    (store) => store.areaVisible
)

export const selectOnCallTypes = createSelector(
    selectMainState,
    (store) => store.onCallTypes
)

export const selectedAgency = createSelector(
    selectMainState,
    (store) => store.formData.location.agency
)

export const selectedAreaType = createSelector(
    selectMainState,
    (store) => store.formData.location.areaType
)

export const selectedArea = createSelector(
    selectMainState,
    (store) => store.formData.location.areaSelection
)

export const selectOnCallTypeList = createSelector(
    selectMainState,
    (store) => store.onCallTypes
)

export const selectedPeriodIndex = createSelector(
    selectMainState,
    (store) => store.selectedPeriodIndex
)

export const selectedPeriod = createSelector(
    selectMainState,
    (store) => store.formData && store.formData.periods ? store.formData.periods[store.selectedPeriodIndex] : null
)

export const selectedLastPeriod = createSelector(
    selectMainState,
    (store) => {
        const entities = featureAdapter.getSelectors().selectAll(store)
        return entities && entities.length > 0 && entities[0].periods ? entities[0].periods[store.numberOfPeriods - 1] : null
    }
)

export const numberOfPeriods = createSelector(
    selectMainState,
    (store) => store.numberOfPeriods
)

export const periodRange = createSelector(
    selectMainState,
    (store) => store.periodRange
)

export const selectedPeriodUsers = createSelector(
    selectedPeriod,
    (store) => {
        const users = new Array<UserAndGroup>()

        if (store && store.onCallPeople) {
            store.onCallPeople.forEach(person => {
                if (person.user && person.user.id) {
                    const configUser: UserAndGroup = {
                        id: person.user.id,
                        type: person.user.type || 0,
                        name: person.user.name || '',
                        email: person.user.email || ''
                    };
                    users.push(configUser);
                }
            });
        }
        return users
    }
)

export const selectedForm = createSelector(
    selectMainState,
    (store) => store.formData
)
