import { Action } from '@ngrx/store';
import { Region } from '../../configuration/regions/region.model';
import { Checklist } from '../../configuration/pm-checklist/checklist.model';
import { LocationInstanceGridModel } from '../../configuration/location-instances/shared/location-instance-grid-element';
import { Schedule } from '../../scheduler/schedule.model';
import { Filter } from '../../scheduler/filter/filter.model';
import { CalendarView } from 'angular-calendar';

export enum ActionTypes {
    CLOSE_SIDEBAR = '[Scheduler] Close Sidebar',
    LOAD_REQUEST = '[Scheduler] Load Request',
    LOAD_FAILURE = '[Scheduler] Load Failure',
    LOAD_SUCCESS = '[Scheduler] Load Success',
    SHOW_SIDEBAR = '[Scheduler] Show Sidebar',
    HIDE_SIDEBAR = '[Scheduler] Hide Sidebar',
    SHOW_ADD_CHEKLIST_TO_SCHEDULER = '[Scheduler] Show Add Checklist To Scheduler',
    NAVIGATE = '[Scheduler] Navigate',
    SAVE = '[Scheduler] Save',
    SAVE_SUCCESS = '[Scheduler] Save Success',
    SAVE_REQUEST = '[Scheduler] Save Request',
    UPDATE_REQUEST = '[Scheduler] Update Request',
    UPDATE_SUCCESS = '[Scheduler] Update Success',
    DELETE = '[Scheduler] Delete',
    DELETE_REQUEST = '[Scheduler] Delete Request',
    DELETE_SUCCESS = '[Scheduler] Delete Success',
    NO_ACTION = '[Scheduler] No Action',
    SHOW_EDIT_SCHEDULE = '[Scheduler] Show Edit Schedule',
    SHOW_FILTER = '[Scheduler] Show Filter',
    APPLY_FILTER = '[Scheduler] Apply Filter',
    SEARCH_REQUEST = '[Scheduler] Search Request',
    SEARCH_SUCCESS = '[Scheduler] Search Success',
    SEARCH_FAILURE = '[Scheduler] Search Failure',
    LOAD_DATA = '[Scheduler] Load Data',
    CALENDAR_VIEW_CHANGED = '[Scheduler] Calendar View Changed',
    LOAD_SIDEBAR_DATA_REQUEST = '[Scheduler] Load Sidebar Data Request',
    LOAD_SIDEBAR_DATA_SUCCESS = '[Scheduler] Load Sidebar Data Success',
    TOGGLE_SCHEDULE_ACTIVE_STATE_REQUEST = '[Scheduler] Toggle Schedule Active State Request',
    SCHEDULER_FILTER_LOCATION = '[Scheduler] Filter Location',
}

export class CloseSidebarAction implements Action {
    readonly type = ActionTypes.CLOSE_SIDEBAR;
}

export class LoadRequestAction implements Action {
    readonly type = ActionTypes.LOAD_REQUEST;
}

export class LoadFailureAction implements Action {
    readonly type = ActionTypes.LOAD_FAILURE;
    constructor(public payload: { error: string }) { }
}

export class LoadSuccessAction implements Action {
    readonly type = ActionTypes.LOAD_SUCCESS;
    constructor(public payload: { schedules: Schedule[] }) { }
}

export class ShowSidebarAction implements Action {
    readonly type = ActionTypes.SHOW_SIDEBAR;
}

export class HideSidebarAction implements Action {
    readonly type = ActionTypes.HIDE_SIDEBAR;
}

export class ShowAddChecklistToSchedulerAction implements Action {
    readonly type = ActionTypes.SHOW_ADD_CHEKLIST_TO_SCHEDULER;
    constructor(public payload: { view: string }) { }
}

export class NavigateAction implements Action {
    readonly type = ActionTypes.NAVIGATE;
    constructor(public payload: { navigateTo: string }) { }
}

export class SaveAction implements Action {
    readonly type = ActionTypes.SAVE;
    constructor(public payload: Schedule) { }
}

export class SaveRequestAction implements Action {
    readonly type = ActionTypes.SAVE_REQUEST;
    constructor(public payload: Schedule) { }
}

export class SaveSuccessAction implements Action {
    readonly type = ActionTypes.SAVE_SUCCESS;
    constructor(public payload: Schedule) { }
}

export class UpdateRequestAction implements Action {
    readonly type = ActionTypes.UPDATE_REQUEST;
    constructor(public payload: Schedule) { }
}

export class UpdateSuccessAction implements Action {
    readonly type = ActionTypes.UPDATE_SUCCESS;
    constructor(public payload: Schedule) { }
}

export class DeleteAction implements Action {
    readonly type = ActionTypes.DELETE;
    constructor(public payload: { scheduleId: string }) { }
}

export class DeleteRequestAction implements Action {
    readonly type = ActionTypes.DELETE_REQUEST;
    constructor(public payload: { scheduleId: string }) { }
}

export class DeleteSuccessAction implements Action {
    readonly type = ActionTypes.DELETE_SUCCESS;
    constructor(public payload: { scheduleId: string }) { }
}

export class ShowEditScheduleAction implements Action {
    readonly type = ActionTypes.SHOW_EDIT_SCHEDULE;
    constructor(public payload: { view: string, scheduleId: string }) { }
}

export class ShowFilterAction implements Action {
    readonly type = ActionTypes.SHOW_FILTER;
    constructor(public payload: { view: string }) { }
}

export class ApplyFilterAction implements Action {
    readonly type = ActionTypes.APPLY_FILTER;
    constructor(public payload: Filter) { }
}

export class SearchRequestAction implements Action {
    readonly type = ActionTypes.SEARCH_REQUEST;
    constructor(public payload: Filter) { }
}

export class SearchSuccessAction implements Action {
    readonly type = ActionTypes.SEARCH_SUCCESS;
    constructor(public payload: Schedule[]) { }
}

export class SearchFailureAction implements Action {
    readonly type = ActionTypes.SEARCH_FAILURE;
    constructor(public payload: { error: string }) { }
}

export class LoadDataAction implements Action {
    readonly type = ActionTypes.LOAD_DATA;
    constructor() { }
}

export class CalendarViewChanged implements Action {
    readonly type = ActionTypes.CALENDAR_VIEW_CHANGED;
    constructor(public payload: CalendarView) { }
}

export class NoAction implements Action {
    readonly type = ActionTypes.NO_ACTION;
}

export class LoadSidebarDataRequestAction implements Action {
    readonly type = ActionTypes.LOAD_SIDEBAR_DATA_REQUEST;
    constructor() { }
}

export class LoadSidebarDataSuccessAction implements Action {
    readonly type = ActionTypes.LOAD_SIDEBAR_DATA_SUCCESS;
    constructor(public payload: { checklists: Checklist[], regions: Region[], locations: LocationInstanceGridModel[] }) { }
}

export class ToggleScheduleActiveStateRequestAction implements Action {
    readonly type = ActionTypes.TOGGLE_SCHEDULE_ACTIVE_STATE_REQUEST;
    constructor(public payload: Schedule) { }
}

export class ScheduleFilterLocationAction implements Action {
    readonly type = ActionTypes.SCHEDULER_FILTER_LOCATION;
    constructor() { }
}

export type Actions =
    CloseSidebarAction |
    LoadRequestAction |
    LoadFailureAction |
    LoadSuccessAction |
    ShowSidebarAction |
    HideSidebarAction |
    ShowAddChecklistToSchedulerAction |
    NavigateAction |
    SaveAction |
    SaveRequestAction |
    SaveSuccessAction |
    UpdateRequestAction |
    UpdateSuccessAction |
    DeleteAction |
    DeleteRequestAction |
    DeleteSuccessAction |
    ShowEditScheduleAction |
    ShowFilterAction |
    ApplyFilterAction |
    SearchRequestAction |
    SearchSuccessAction |
    SearchFailureAction |
    LoadDataAction |
    CalendarViewChanged |
    LoadSidebarDataRequestAction |
    LoadSidebarDataSuccessAction |
    ToggleScheduleActiveStateRequestAction |
    ScheduleFilterLocationAction |
    NoAction;
