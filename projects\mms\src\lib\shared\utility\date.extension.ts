import { timezones } from "../../configuration/agencies/timezones";

export {}

declare global {
  interface Date {
    toUtc(): Date;
    fromTimeZone(currentDate: Date, timezone: String): Date;
    processDaylightSaving(date: Date, timezoneId: String): Date;
  }
}

Date.prototype.toUtc = function(): Date {
    const param = new Date();
    const utcDate = Date.UTC(param.getUTCFullYear(), param.getUTCMonth(), param.getUTCDate(),
    param.getUTCHours(), param.getUTCMinutes(), param.getUTCSeconds());;
    return new Date(utcDate);
};

Date.prototype.fromTimeZone = function(currentDate: Date, timezoneId: String): Date{
  var timezoneObj = timezones.find(x=>x.id === timezoneId);
  if (!timezoneObj)
      return currentDate;
  var timezone = timezoneObj.BaseUtcOffset;
  const utcDate = Date.UTC(currentDate.getUTCFullYear(), currentDate.getUTCMonth(), currentDate.getUTCDate(),
  currentDate.getUTCHours(), currentDate.getUTCMinutes(), currentDate.getUTCSeconds());
  const operator = timezone.includes('-')?'-':'';
  timezone = timezone.replace("-","");
  const utcOffset = (parseInt(timezone.split(':')[0]) * 3600) + (parseInt(timezone.split(':')[1]) * 60) + parseInt(timezone.split(':')[2]);
  const result = operator == '-'?utcDate - (utcOffset * 1000):utcDate + (utcOffset * 1000);
  return Date.prototype.processDaylightSaving(new Date(result),timezoneId);
}

Date.prototype.processDaylightSaving = function(date: Date, timezoneId: String): Date {
  let timezone = timezones.find(x=>x.id === timezoneId);
  if (!timezone || !timezone.SupportsDaylightSavingTime || !timezone.AdjustmentRules || timezone.AdjustmentRules.length ==0)
      return date;
   let currentAdjustment = timezone.AdjustmentRules[timezone.AdjustmentRules.length -1];
   let daylightOffset = timezone.AdjustmentRules[timezone.AdjustmentRules.length -1].DaylightDelta;
   let containsMinusSign = daylightOffset.includes("-");
   daylightOffset = daylightOffset.replace("-","");
   let deltaOffset = ((parseInt(daylightOffset.split(':')[0]) * 3600) + (parseInt(daylightOffset.split(':')[1]) * 60) + parseInt(daylightOffset.split(':')[2])) * 1000;
   const daylightStartDate = getDayFromWeek(new Date().getFullYear(), currentAdjustment.DaylightTransitionStart.Month, currentAdjustment.DaylightTransitionStart.Week, currentAdjustment.DaylightTransitionStart.DayOfWeek);
   const daylightEndDate = getDayFromWeek(new Date().getFullYear(), currentAdjustment.DaylightTransitionEnd.Month, currentAdjustment.DaylightTransitionEnd.Week, currentAdjustment.DaylightTransitionEnd.DayOfWeek);
   if (date > daylightStartDate && date < daylightEndDate)
   {
      deltaOffset =  containsMinusSign?0-deltaOffset:deltaOffset
      date.setTime(date.getTime() + deltaOffset);
   }
   return date;
}

function getDayFromWeek(year: number, month: number, weekNumber: number, weekDay: number): Date {
  var date = new Date(year,month,0,12);
  weekNumber = weekCount(year,month) - weekNumber;
  date.setDate(date.getDate() - weekNumber * 7 + 1);
  let currentWeekDay = date.getDay();
  let dayDiff = currentWeekDay>weekDay ? (weekDay + 7) - currentWeekDay: weekDay - currentWeekDay;
  date.setDate(date.getDate() + dayDiff);
  if (date.getMonth() + 1 > month)
        date.setDate(date.getDate() - 7);
  return date;
}

function weekCount(year: number, month_number: number): number {
  var firstOfMonth = new Date(year, month_number-1, 1);
  var lastOfMonth = new Date(year, month_number, 0);
  var used = firstOfMonth.getDay() + lastOfMonth.getDate();
  return Math.ceil( used / 7);
}

