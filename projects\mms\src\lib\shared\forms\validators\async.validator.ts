import { AsyncValidator as AngularAsyncValidator, ValidationErrors, AbstractControl } from '@angular/forms'

import { Observable, Observer, timer } from 'rxjs';
import { map, switchMap, take } from 'rxjs/operators';

export class AsyncValidator implements AngularAsyncValidator {

    constructor(private func: (value: string) => Observable<boolean>, private message: string) {
    }

    validate(c: AbstractControl): | Observable<ValidationErrors | null> {
        if (c.value) {
            return timer(500).pipe(switchMap(() => {
                return this.func.bind(this)(c.value).pipe(map((result: boolean) => {
                    if (result) {
                        return null;
                    } else {
                        return {
                            async: {
                                message: this.message.replace('$value', c.value)
                            }
                        }
                    }
                }))
            }), take(1))
        } else {
            return Observable.create((observer: Observer<any>) => {
                observer.next(null);
                observer.complete();
            })
        }
    }
}
