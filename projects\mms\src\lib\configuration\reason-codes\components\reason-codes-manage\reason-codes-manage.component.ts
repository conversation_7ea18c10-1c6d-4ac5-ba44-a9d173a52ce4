import { Component, OnInit, HostListener, Inject } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup } from '@angular/forms';
import { ReasonCodesStoreState, ReasonCodesStoreSelectors, ReasonCodesStoreActions } from "../../../../root-store";
import { Store } from '@ngrx/store';
import { UserIdentity } from '@econolite/identity-client';
import { PermissionsEnum } from '../../../../core/auth/permissions.enum';
import { Validators } from '../../../../shared/forms/validators/validators';
import { withLatestFrom, map, take } from 'rxjs/operators';
import { ReasonCode } from '../../models/reason-code.model';
import { FormRevertChangesAction, FormResetAction } from '../../../../shared/forms/connect-form-ngrx';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { ValidationMessageComponent } from '../../../../shared/forms/validation-message/validation-message.component';
import { HintMessageComponent } from '../../../../shared/forms/hint-message/hint-message.component';
import { ConnectFormDirective } from '../../../../shared/forms/connect-form-ngrx';
import { IdentityClientModule } from '@econolite/identity-client';
import { VarDirective } from '../../../../shared/forms/variable.directive';

@Component({
  selector: 'app-reason-codes-manage',
  templateUrl: './reason-codes-manage.component.html',
  styleUrls: ['./reason-codes-manage.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    ValidationMessageComponent,
    HintMessageComponent,
    ConnectFormDirective,
    VarDirective,
    // IdentityClientModule
  ]
})
export class ReasonCodesManageComponent implements OnInit {
  
  Permissions = PermissionsEnum;
  reasonCodeFormGroup!: FormGroup;

  hasCreatePermission = this.userIdentity.hasPermission([this.Permissions.CreateReasonCode]);
  hasEditPermission = this.userIdentity.hasPermission([this.Permissions.EditReasonCode]);
  
  selectedReasonCode$ = this.store$.select(ReasonCodesStoreSelectors.getSelectedReasonCode);

  constructor(private formBuilder: FormBuilder, private store$: Store<ReasonCodesStoreState.State>, @Inject(UserIdentity) public userIdentity: UserIdentity) { }

  ngOnInit() {
    this.setForm();
  }

  cancelEdit() {
    this.store$.dispatch(new FormRevertChangesAction());
  }

  clearFormFields() {
    this.store$.dispatch(new FormResetAction());
  }

  save() {
    if (this.reasonCodeFormGroup.dirty && this.reasonCodeFormGroup.valid) {
        const id = this.reasonCodeFormGroup.controls['id'].value;
        const reasonCode = this.reasonCodeFormGroup.getRawValue();
        this.store$.dispatch(id ? new ReasonCodesStoreActions.UpdateReasonCodeAction({ reasonCode })
                                : new ReasonCodesStoreActions.SaveReasonCodeAction({ reasonCode }));
    }
  }

  /**
  * Field should be readonly if user does not have create or edit permissions but is trying to do either.
  * */
  isReadonly() {
   const id = this.reasonCodeFormGroup.controls['id'].value;
   return (id && !this.hasEditPermission) || (!id && !this.hasCreatePermission);
  }

  canDeactivate() {
    return this.selectedReasonCode$.pipe(
        map((reasonCode) => {
            if (reasonCode != null) {
                return Object.equal(this.reasonCodeFormGroup.getRawValue(), reasonCode, ['id','tenantId'])
            } else {
                return Object.equal(this.reasonCodeFormGroup.getRawValue(), new ReasonCode(), ['id', 'tenantId']);
            }
        }),
        take(1)
    );
  };

  /**
   * Triggered on any unload browser event (refresh, back, exit, tenant change, sign out) if there are any unsaved changes on form
   */
  @HostListener('window:beforeunload')
  canUnloadPage(): boolean {
    var returnValue = false;
    this.canDeactivate().subscribe(x => returnValue = x);
    return returnValue;
  }

  // #region Private Methods
  private setForm() {
    this.reasonCodeFormGroup = this.formBuilder.group({
        id: null,
        tenantId: null,
        name: [null, [Validators.required(), Validators.emoji()], [Validators.async(this.isNameValid.bind(this), '$value Reason Code already exists.')]],
    });
  }

  private isNameValid(name: string) {
    return this.store$.select(ReasonCodesStoreSelectors.getAllReasonCodes).pipe(
        withLatestFrom(this.store$.select(
            ReasonCodesStoreSelectors.getSelectedReasonCode
        )),
        map(([items, reasonCode]: [Array<ReasonCode>, ReasonCode]) => {
            return !items.some(x => (reasonCode === undefined || x.id !== reasonCode.id) && x.name && x.name.toLocaleLowerCase() === name.toLocaleLowerCase());
        })
    );
  }
}
