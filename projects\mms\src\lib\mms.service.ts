import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http'

import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class MmsService {

  constructor(private httpClient: HttpClient) { }

  getAssetTypes(): Observable<any> {
    return this.httpClient.get('inventory/v1/assetType');
  }

  checkInitialSharedFields(): Observable<any> {
    return this.httpClient.get('inventory/v1/sharedField/initial-data-check');
  }

  checkInitialLocationTypes(filedIds: string[]): Observable<any> {
    return this.httpClient.post('location/v1/locationType/initial-data-check', filedIds);
  }


  checkIfLocationNameExist(name: string, locationId: string, agencyId: string): Observable<boolean> {
    const params = new HttpParams()
    .set('name', name)
    .set('locationId', locationId)
    .set('agencyId', agencyId);
    return this.httpClient.get<boolean>('location/v1/locations/check-name', {params: params});
  }

  checkIfAssetsNameExist(name: string, assetId: string, agencyId: string): Observable<boolean> {
    var params = new HttpParams()
    .set('name', name)
    .set('id', assetId)
    .set('agencyId', agencyId);
    return this.httpClient.get<boolean>('inventory/v1/masterInventory/check-name', {params: params});
  }

  checkIfAssetsUniqueIdExist(uniqueId: string, assetId: string): Observable<boolean> {
    var params = new HttpParams()
    .set('uniqueId', uniqueId)
    .set('id', assetId)
    return this.httpClient.get<boolean>('inventory/v1/masterInventory/check-uniqueId', {params: params});
  }

  checkIfTicketNoExist(ticketNo: string): Observable<boolean> {
    var params = new HttpParams()
    .set('ticketNo', ticketNo);
    return this.httpClient.get<boolean>('ticket/v1/ticket/check-ticketNo', {params: params});
  }
}
