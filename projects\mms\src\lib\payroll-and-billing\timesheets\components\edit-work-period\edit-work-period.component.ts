import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { FormGroup } from '@angular/forms';

@Component({
  selector: 'app-edit-work-period',
  templateUrl: 'edit-work-period.component.html',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule
  ]
})
export class EditWorkPeriodComponent implements OnInit {

  formGroup?: FormGroup;
  cancelAction = 'Cancel';
  yesAction = 'OK';
  constructor(@Inject(MAT_DIALOG_DATA) public data: any, private dialogRef: MatDialogRef<MatDialog>) {
  }

  ngOnInit() {

  }

  yes() {
    this.dialogRef.close(true);
  }

  cancel() {
    this.dialogRef.close(null);
  }
}