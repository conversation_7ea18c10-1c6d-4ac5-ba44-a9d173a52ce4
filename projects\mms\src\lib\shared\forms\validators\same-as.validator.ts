import { Validator, AbstractControl, ValidationErrors } from '@angular/forms';

export class SameAsValidator implements Validator {
    constructor(private controlName: string) {
    }

    validate(control: AbstractControl): ValidationErrors | null {
        const sameAsControl = control.root.get(this.controlName);

        if (sameAsControl && sameAsControl.value !== control.value) {
            return {
                sameAs: {
                    message: 'Passwords are not matching.'
                }
            };
        }
        return null;
    }
}
