import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Action, Store, select } from '@ngrx/store';
import { of as observableOf, of } from 'rxjs';
import { catchError, map, switchMap, tap, withLatestFrom, exhaustMap, concatMap } from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state'
import * as featureSelectors from './selectors'

import { Router } from '@angular/router';
import { Page } from '../../core/page.service';
import { displayMessages } from '../../core/resources/display-messages';
import { UserService } from '../../configuration/shared/user-and-groups/users.service';
import { UserIdentity } from '@econolite/identity-client';
import { MatDialog } from '@angular/material/dialog';
import { ContractService } from '../../payroll-and-billing/contracts/contract.service';

import { GridStoreActions } from '../shared-store/material-grid-store';
import { Contract } from '../../payroll-and-billing/contracts/models/contract.model';
import * as AgencyStoreSelectors from '../agency-store/selectors';
import { CONTRACTS_GRID_ID } from './state';

@Injectable()
export class ContractStoreEffects {
  constructor(
    private router: Router,
    private contractService: ContractService,
    private actions$: Actions,
    private store$: Store<featureState.State>,
    private page: Page,
    private userService: UserService,
    private userIdentity: UserIdentity,
    public dialog: MatDialog) { }

  loadContractsRequest$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(featureActions.ActionTypes.LOAD_REQUEST),
    withLatestFrom(
      this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
      this.store$.select(featureSelectors.getNextPageToken),
    ),
    switchMap(([action, agency, nextPageToken]) => {
      if (!agency) {
        return of(new featureActions.LoadFailureAction({ error: 'Agency must be selected' }));
      }
      const filter = {
        agencyId: agency.id,
        nextPageToken
      };
      return this.contractService.getContracts(filter, nextPageToken).pipe(
        concatMap(
          (result) => {
            const actions: Action[] = [
              new featureActions.LoadSuccessAction({ items: result.items! }),
              new GridStoreActions.GridLoadItemsSuccessAction(
                featureState.CONTRACTS_GRID_ID,
                { items: result.items!, nextPageToken: result.nextPageToken, clearGrid: nextPageToken !== null }
              )
            ];
            return actions;
          }
        ),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      )
    })
  ));

  saveEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveAction>(featureActions.ActionTypes.SAVE),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectedContract))),
    map(([action, selectedContract]: [featureActions.SaveAction, Contract]) => {
      if (selectedContract == null) {
        return new featureActions.SaveRequestAction({ item: action.payload.item })
      } else {
        const contract = {
          ...action.payload.item,
          billingHolidays: selectedContract.billingHolidays,
          contractPrices: selectedContract.contractPrices
        };
        return new featureActions.UpdateRequestAction({ item: contract })
      }
    })
  ));

  saveContractEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveRequestAction>(featureActions.ActionTypes.SAVE_REQUEST),
    withLatestFrom(this.store$.select(AgencyStoreSelectors.selectDefaultAgency)),
    exhaustMap(([action, agency]) => {
      if (!agency) {
        return of(new featureActions.LoadFailureAction({ error: 'Agency must be selected' }));
      }
      if (!agency.id) {
        return of(new featureActions.LoadFailureAction({ error: 'Agency must have a valid ID' }));
      }
      const contract = {
        ...action.payload.item,
        agency: { id: agency.id, name: agency.name || '' }
      };
      return this.contractService.createContract(contract).pipe(
        concatMap(contract => [
          new featureActions.LoadRequestAction(),
          new featureActions.SaveSuccessAction({ item: contract! })
        ]),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      )
    })
  ));

  saveContractSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveSuccessAction>(featureActions.ActionTypes.SAVE_SUCCESS),
    map((action) => {
      this.page.notification.show('Contract added');
      this.router.navigate(['/payroll-and-billing/contracts/edit-contract/', action.payload.item.id], { state: { shouldDeactivate: true } });
      return new featureActions.NoAction;
    })
  ));

  updateContractEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateRequestAction>(featureActions.ActionTypes.UPDATE_REQUEST),
    withLatestFrom(this.store$.select(AgencyStoreSelectors.selectDefaultAgency)),
    exhaustMap(([action, agency]) => {
      if (!agency) {
        return of(new featureActions.LoadFailureAction({ error: 'Agency must be selected' }));
      }
      if (!agency.id) {
        return of(new featureActions.LoadFailureAction({ error: 'Agency must have a valid ID' }));
      }
      if (!action.payload.item.id) {
        return of(new featureActions.LoadFailureAction({ error: 'Contract ID is required for update' }));
      }
      const contract = {
        ...action.payload.item,
        agency: { id: agency.id, name: agency.name || '' }
      };
      return this.contractService.updateContract(contract).pipe(
        concatMap(() => [
          new featureActions.UpdateSuccessAction({ item: { id: action.payload.item.id!, changes: contract } }),
          new featureActions.LoadRequestAction(),
          new GridStoreActions.GridItemUpdatedAction(CONTRACTS_GRID_ID, { itemId: contract.id!, item: contract })
        ]),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      )
    })
  ));

  updateContractSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateSuccessAction>(featureActions.ActionTypes.UPDATE_SUCCESS),
    map(() => {
      this.page.notification.show('Contract updated');
    })
  ), { dispatch: false });

  canBeDeletedEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.CanBeDeletedAction>(featureActions.ActionTypes.CAN_BE_DELETED),
    exhaustMap((action) =>
      this.contractService.canContractBeDeleted(action.payload.contractId).pipe(
        map((response) => {
          if (!response) {
            return new featureActions.ConfirmDeleteContractAction({ contractId: action.payload.contractId });
          } else {
            this.page.alert.warning(displayMessages.exceptions.itemIsAssociated('contract'));
            return new featureActions.NoAction();
          }
        }),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      )
    )
  ));

  confirmDeleteContractEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmDeleteContractAction>(featureActions.ActionTypes.CONFIRM_DELETE_CONTRACT),
    exhaustMap((action) =>
      this.page.confirm.show('Are you sure you want to delete this contract?', 'Are you sure?').pipe(
        map((result) => {
          if (result) {
            return new featureActions.DeleteRequestAction({ contractId: action.payload.contractId });
          }
          return new featureActions.NoAction();
        })
      )
    )
  ));

  deleteContractEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteRequestAction>(featureActions.ActionTypes.DELETE_REQUEST),
    switchMap((action) =>
      this.contractService.deleteContract(action.payload.contractId).pipe(
        switchMap(() => [
          new featureActions.DeleteSuccessAction({ itemId: action.payload.contractId }),
          new GridStoreActions.GridItemRemovedAction(CONTRACTS_GRID_ID, { itemId: action.payload.contractId })
        ]),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      )
    )
  ));

  saveContractHolidaysEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveContractHolidaysAction>(featureActions.ActionTypes.SAVE_CONTRACT_HOLIDAYS),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectedContract))),
    map(([action, selectedContract]: [featureActions.SaveContractHolidaysAction, Contract]) => {
      selectedContract.billingHolidays = [...action.payload.holidays]
      return new featureActions.UpdateRequestAction({ item: selectedContract })
    })
  ));

  saveContractPricesEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveContractPricesAction>(featureActions.ActionTypes.SAVE_CONTRACT_PRICES),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectedContract))),
    map(([action, selectedContract]: [featureActions.SaveContractPricesAction, Contract]) => {
      selectedContract.contractPrices = [...action.payload.prices];
      return new featureActions.UpdateRequestAction({ item: selectedContract })
    })
  ));

  searchTextChangedAction$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SearchTextChangedAction>(featureActions.ActionTypes.SEARCH_TEXT_CHANGED),
    withLatestFrom(this.store$.select(featureSelectors.selectContracts)),
    map(([action, contracts]) => {
      const items = contracts.filter(x => !action.payload || x.name.toLocaleLowerCase().indexOf(action.payload.toLocaleLowerCase()) > -1);
      return new GridStoreActions.GridLoadItemsSuccessAction(
        featureState.CONTRACTS_GRID_ID,
        { items }
      )
    }),
    catchError(error => observableOf(new featureActions.LoadFailureAction({ error })))
  ));

  copyContractEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.CopyContractAction>(featureActions.ActionTypes.COPY_CONTRACT),
    withLatestFrom(this.store$.select(AgencyStoreSelectors.selectDefaultAgency)),
    exhaustMap(([action, agency]) => {
      if (!agency) {
        return of(new featureActions.LoadFailureAction({ error: 'Agency must be selected' }));
      }
      if (!agency.id) {
        return of(new featureActions.LoadFailureAction({ error: 'Agency must have a valid ID' }));
      }
      const contract = {
        ...action.payload.item,
        agency: { id: agency.id, name: agency.name || '' }
      };
      return this.contractService.createContract(contract).pipe(
        concatMap((contract) => {
          if (!contract?.id || !contract.name) {
            return of(new featureActions.LoadFailureAction({ error: 'Contract ID and name are required' }));
          }
          return [
            new featureActions.CopyJurisdictionConfFromContractAction({
              destinationContract: {id: contract.id!, name: contract.name!},
              sourceContractId: action.payload.sourceContractId
            })
          ];
        }),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      )
    })
  ));

  copyJurisdictionsFromContractEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.CopyJurisdictionConfFromContractAction>(featureActions.ActionTypes.COPY_JURISDICTIONS_CONF_FROM_CONTRACT),
    exhaustMap((action) =>
      this.contractService.copyJurisdictionConfigurationFromContract(action.payload).pipe(
        map(() => new featureActions.LoadRequestAction()),
        catchError(error => observableOf(new featureActions.LoadFailureAction({ error })))
      )
    )
  ));

  openProjects$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.OpenProjectsAction>(featureActions.ActionTypes.OPEN_PROJECTS),
    tap((action) => {
      this.router.navigate([`/projects/manage-projects/${action.payload.contractId}`]);
    })
  ), { dispatch: false });
}
