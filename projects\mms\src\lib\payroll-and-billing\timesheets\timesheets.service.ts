import { Ticket } from './../../configuration/tickets/tickets.model';
import { TaskHistory } from './models/task-history.model';
import { ManageTypeEnum, WorkPeriodManage } from './models/work-period-manage.model';
import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { TimesheetFilter } from "./models/filter.model";
import { Observable } from "rxjs";
import { Timesheet } from "./models/timesheet.model";
import { map } from "rxjs/operators";
import { PagedResults } from "./models/paged-results.model";
import { EmployeeWeekTimesheet } from "./models/employee-week-timesheet.model";
import { WorkPeriodGridModel } from "./models/work-period-grid-model.model";
import { ApproveTasksRequest } from "./models/approve-tasks-request.model";
import { StopActiveWPRequest } from './models/stop-active-wp-request.model';
import { PayrollOutputRow } from './models/payroll-output-row';
import { Page } from '../../core/page.service';
import { ApprovalStatus } from '../../configuration/tickets/tickets-enum/ticket-status.enum';
import { BillingEnum } from '../../configuration/tickets/tickets-enum/billing.enum';

@Injectable({
    providedIn: 'root'
})

export class TimesheetsService {

    constructor(private httpClient: HttpClient) {
    }

    getTimesheets(filter: TimesheetFilter | null = null): Observable<PagedResults<Timesheet>> {
        let params = new HttpParams();

        if (filter) {
            if (filter.fromDate) {
                const fromDate = new Date(filter.fromDate);                
                const decreasedFromDate = (new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate() , 0, 0, -1));
                const fromDateModified = new Date(decreasedFromDate.getTime() - (decreasedFromDate.getTimezoneOffset() * 60000)).toISOString();
               
                params = params.set('fromDate', fromDateModified.toString());
            }
            if (filter.toDate) {
                const toDate = new Date(filter.toDate);
                const increasedDate = (new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate() + 1, 0, 0, -1));
                const increasedDateModified = new Date(increasedDate.getTime() - (increasedDate.getTimezoneOffset() * 60000)).toISOString();
                params = params.set('toDate', increasedDateModified);
            }
            if (filter.users && filter.users.length) {
                filter.users.forEach((userId, index) => {
                    params = params.set(`users[${index}]`, userId);
                });
            }
            if (filter.agencies && filter.agencies.length) {
                filter.agencies.forEach((agencyId, index) => {
                    params = params.set(`agencies[${index}]`, agencyId);
                });
            }
            if (filter.hideApproved !== undefined && filter.hideApproved !== null) {
                params = params.set('hideApproved', filter.hideApproved.toString());
            }
            if (filter.date !== undefined && filter.date !== null) {
                params = params.set('date', filter.date.toString());
            }
            if (filter.userId !== undefined && filter.userId !== null) {
                params = params.set('userId', filter.userId.toString());
            }
            if (filter.nextPageToken) {
                params = params.set('nextPageToken', filter.nextPageToken);
            }
        }
        
        return this.httpClient.get<PagedResults<Timesheet>>('aggregator/v1/timesheet', { params });
    }

    getWeekTimesheeets(endDate: Date): Observable<Array<EmployeeWeekTimesheet>> {
        if (!endDate) {
            throw new Error('End date is required');
        }
        const params = new HttpParams()
            .set('endDate', endDate.toLocaleString());
        return this.httpClient.get<Array<EmployeeWeekTimesheet>>('aggregator/v1/timesheet/payrollCenter', { params });
    }

    getPayrollOutputData(endDate: Date): Observable<Array<PayrollOutputRow>> {
        if (!endDate) {
            throw new Error('End date is required');
        }
        const params = new HttpParams()
            .set('endDate', endDate.toLocaleString());
        return this.httpClient.get<Array<PayrollOutputRow>>('aggregator/v1/timesheet/payrollOutput', { params });
    }

    updateMultipleTimesheets(timesheets: Array<Timesheet>): Observable<Array<Timesheet>> {
        return this.httpClient.put<Array<Timesheet>>('payrollandbilling/v1/timesheet/update-multiple', timesheets);
    }

    updateTimesheet(timesheet: Timesheet): Observable<boolean> {
        return this.httpClient.put('payrollandbilling/v1/timesheet', timesheet).pipe(map(
            () => {
              return true;
            }
          ));
    }

    getWorkPeriodGridModel(timesheet: Timesheet, showDeleted?: boolean): Observable<Array<WorkPeriodGridModel>> {
        if (!timesheet.id) {
            throw new Error('Timesheet ID is required');
        }
        let params = new HttpParams();
        params = params.set('id', timesheet.id);
        if (showDeleted !== null && showDeleted !== undefined) {
            params = params.set('showDeleted', showDeleted.toString());
        }

        return this.httpClient.get<Array<WorkPeriodGridModel>>('aggregator/v1/timesheet/work-periods', { params });
    }

    approveTasks(model: ApproveTasksRequest): Observable<Array<Ticket>> {
        return this.httpClient.put<Array<Ticket>>('ticket/v1/ticket/approve-tasks', model);
    }

    manageWorkPeriod(request: WorkPeriodManage): Observable<Timesheet> {
        return this.httpClient.put<Timesheet>('aggregator/v1/timesheet/update-timesheet-work-periods', request);     
    }

    getTaskHistory(ticketNo: string): Observable<TaskHistory> {
        if (!ticketNo) {
            throw new Error('Ticket number is required');
        }
        let params = new HttpParams();
        params = params.set('ticketNo', ticketNo);

        return this.httpClient.get<TaskHistory>('aggregator/v1/timesheet/task-history', { params });
    }

    stopActiveWPTimesheet(request: StopActiveWPRequest): Observable<Timesheet> {
        return this.httpClient.put<Timesheet>('aggregator/v1/timesheet/stop-timesheet-active-work-period', request);     
    }
    
    getTimesheetById(id: string): Observable<Timesheet> {
        if (!id) {
            throw new Error('ID is required');
        }
        return this.httpClient.get<Timesheet>('payrollandbilling/v1/timesheet/getById/' + id);
    }
    
    addNewWorkPeriod(ticket: Ticket, timesheetId: string, date: any, employee: any, userName: any): Observable<Timesheet> {
        if (!ticket || !ticket.type || !ticket.project) {
            throw new Error('Valid ticket with type and project is required');
        }
        
        let category = false;
        if (ticket.type.category === 1) {
            category = true;
        }
        const request = {
            TimesheetId: timesheetId,
            ManageType: ManageTypeEnum.Add,
            UserName: userName,
            NewWorkPeriod: {
                Actions: ticket.actions || null,
                Employee: employee,
                TicketId: ticket.id,
                TicketNo: ticket.ticketNo,
                Location: ticket.locationData || null,
                Project: ticket.project,
                EnRouteTime: null,
                OnSiteTime: null,
                isDeleted: false,
                End: null,
                Start: date,
                TotalLunchBreakHours: 0,
                TotalHours: 0,
                FoundOnSiteComments: null,
                WorkPerformedNotesComments: null,
                WhyStoppedComments: null,
                AgencyId: ticket.project.agencyId,
                ProjectType: ticket.type.name,
                IsPM: category,
                JobCode: null, 
                BillCode: null,
                ContractId: null,
                IsShared: false,
                TimesheetId: timesheetId,
            },
        };

        return this.httpClient.put<Timesheet>('aggregator/v1/timesheet/update-timesheet-work-periods', request);
    }

    isReadyForApproval(period: WorkPeriodGridModel, timesheet: Timesheet, page: Page): boolean | undefined {
        if (!period || !timesheet || !timesheet.workRecords) {
            return undefined;
        }
        
        if (period.id) {
          const workPeriod = timesheet.workRecords.find(wp => wp.id === period.id);
    
          if (workPeriod) {
    
            if (period.taskApproval === ApprovalStatus.Approved) {
              page.alert.warning('Task is already approved.');
              return false;
            } else if (period.taskApproval === ApprovalStatus.Authorized) {
              page.alert.warning('Task is authorized. Task approval is not possible.');
              return false;
            } else if (!period.isTaskFinalized) {
              page.alert.warning('Task is not finalized. Task approval is not possible.');
              return false;
            } 

            if (!period.jobCode && (period.isBillable && period.billing === BillingEnum.Charge && !period.billCode)) {
              page.alert.warning('Timesheet cannot be approved unless all Job Codes and Bill Codes are set.');
              return false;
            }
    
            if (!period.jobCode) {
              page.alert.warning('Timesheet cannot be approved unless all Job Codes and Bill Codes are set.');
              return false;
            }
            
            if (period.isBillable && period.billing === BillingEnum.Charge && !period.billCode) {
              page.alert.warning('Timesheet cannot be approved unless all Job Codes and Bill Codes are set.');
              return false;
            }
            return true;
          }
        }
        return undefined;
    }
    
    getTasksToBeApproved(timesheet: Timesheet, wpGridItems: WorkPeriodGridModel[], page: Page, period?: WorkPeriodGridModel): { ids: any[]; isNotForApprove: boolean } | null {
        if (!timesheet) {
            return null;
        }
        
        let ids: any[] = [];
        let isNotForApprove = false;

        if (period) {
            const isValid = this.isReadyForApproval(period, timesheet, page);
            if (isValid) {
                ids = [period.ticketId];
            }
        }
        else if (timesheet.workRecords && timesheet.workRecords.length) {
            const workRecords = timesheet.workRecords.filter(x => x.isDeleted !== true);
        
            if (wpGridItems) {
                for (const item of wpGridItems) {
                    if (!item.jobCode && item.isBillable && item.billing === BillingEnum.Charge && item.taskApproval !== null && !item.billCode) {
                        page.alert.warning('Timesheet cannot be approved unless all Job Codes and Bill Codes are set.');
                        isNotForApprove = true;
                        return null;
                    }
            
                    if (!item.jobCode && item.taskApproval !== null) {
                        page.alert.warning('Timesheet cannot be approved unless all Job Codes and Bill Codes are set.');
                        isNotForApprove = true;
                        return null;
                    }
                    
                    if (item.isBillable && item.billing === BillingEnum.Charge && item.taskApproval !== null && !item.billCode) {
                        page.alert.warning('Timesheet cannot be approved unless all Job Codes and Bill Codes are set.');
                        isNotForApprove = true;
                        return null;
                    }
                    if ((item.taskApproval !== null && item.taskApproval === ApprovalStatus.Current) || (item.isTaskFinalized !== null && !item.isTaskFinalized)) {
                        page.alert.warning('Task is not finalized. Task approval is not possible.');
                        isNotForApprove = true;
                        return null;
                    }
                }
            }

            const ticketIds = new Set(workRecords.map(period => period.ticketId));
            ids = Array.from(ticketIds);
        }

        if (ids.length && !isNotForApprove) {
            return { ids: ids, isNotForApprove: isNotForApprove };
        }

        return null;
    }
    
    getTicketByTaskNumber(taskNumber: string): Observable<Ticket> {
        if (!taskNumber) {
            throw new Error('Task number is required');
        }
        let params = new HttpParams();
        params = params.set('ticketNo', taskNumber);
        return this.httpClient.get<Ticket>('aggregator/v1/timesheet/ticket-by-task-number', { params });
    }
}