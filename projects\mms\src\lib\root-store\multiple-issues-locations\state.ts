import { EntityAdapter, createEntityAdapter } from '@ngrx/entity';
import { GridStoreState } from '..';
import { MultipleIssuesLocationsReportModel } from '../../reports/multiple-issues-locations/multiple-issues-locations-report.model';


export const featureAdapter: EntityAdapter<MultipleIssuesLocationsReportModel> = createEntityAdapter<MultipleIssuesLocationsReportModel>( {
    selectId: model => model.id!,
});


export interface State {
    main: MainState;
    grid: GridStoreState.State<MultipleIssuesLocationsReportModel>;
}

export interface MainState {
    isLoading?: boolean;
    error?: any;
    selectedFilter: any;
    
}

export const initialState: MainState = featureAdapter.getInitialState(
    {
    isLoading: false,
    error: null,
    selectedFilter: null,
});

export const MULTIPLE_ISSUES_LOCATIONS_REPORT_GRID_ID = 'MULTIPLE_ISSUES_LOCATIONS_REPORT';
