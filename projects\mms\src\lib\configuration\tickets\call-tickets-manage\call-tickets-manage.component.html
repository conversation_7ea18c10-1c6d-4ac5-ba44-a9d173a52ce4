<div #ticket="value" [value]="selectedCallTicket$ | async"></div>
<div [mmsBlockUI]="isLoading$ || locationsLoading$ | async">
  <div class="card-header px-0 pt-5" class="fs-12">
    <h5 *ngIf="!(isLoading$ || locationsLoading$ | async)" class="card-title mt-2 pt-1 d-inline-block">
      {{ticket.value && ticket.value.id !== null ? 'Edit ' : 'Dispatch '}} Call Ticket
    </h5>
    <button class="mt-1 mb-2 float-right nav" matTooltip="Close" [matTooltipPosition]="'above'" mat-icon-button
      (click)="close()">
      <mat-icon>close</mat-icon>
    </button>
  </div>
  <div class="row w-100">
    <div id="dispatchRow" class="col-8">
      <form [formGroup]="formGroup"
        [mmsConnectForm]="(selectedTicketFormData$ | async) == null ? ticket.value : (selectedTicketFormData$ | async)">
        <input type="hidden" formControlName="id">
        <div class="row w-100">
          <div class="col-6 centered">
            <mat-form-field class="placeholder">
              <mat-label>Call Ticket#</mat-label>
              <input matInput type="text" formControlName="ticketNo" class="pt-5" [readonly]="true">
            </mat-form-field>
          </div>
        </div>
        <div class="row pt-5">
          <!-- <div class="col-4">
          <mat-form-field>
            <mms-select #area formControlName="area" [options]="areas" [placeholder]="'Select Area'" [value]="'0'">
            </mms-select>
          </mat-form-field>
        </div> -->
          <div class="col-6">
            <mat-form-field>
              <mms-select-menu #agency formControlName="agency" [options]="agencies$ | async"
                (selectionChange)="agencyChange($event)" [value]="defaultAgency$ | async " [valueMapper]="valueMapper"
                [compareFn]="compareFn" [placeholder]="'Select Agency'" [enableClearSearchInput]="true"
                [disableClearValue]="true">
              </mms-select-menu>
            </mat-form-field>
          </div>
        </div>
        <div class="row mt--5">
          <div class="col-8">
            <mat-form-field>
              <mms-select-location  formControlName="locationData" [required]="true" placeholder="Location"
                (valueChange)="locationChange($event)" [agencyName]="agencyName" [enableClearSearchInput]="true"
                [showConcreteLocationsOnly]="true" [showLocationsOnly]="true">
              </mms-select-location>
              <mat-error>
                <c-validation-message></c-validation-message>
              </mat-error>
            </mat-form-field>
          </div>
          <div class="col-2 mt--10">
            <button mat-raised-button class="btn btn-primary" type="button" [disabled]="disablebutton$"
              (click)=openHistory()> History
            </button>
          </div>
        </div>
        <div class="row mt--5">
          <div class="col-6">
            <mat-form-field>
              <mms-select-menu #callers formControlName="caller" [options]="(callerList$ | async)" placeholder="Caller"
                (loadMoreData)="loadMoreCallersData($event)" [textMapper]="callerTextMapper"
                [valueMapper]="callerValueMapper" [compareFn]="callerCompareFn" [headerTemplate]="addCallerTemplate"
                (clearValueEmitter)="loadFirst50Callers()" (click)="clickEventCallers()"
                (onOpenedChangeEmitter)="clickEventCallers()" [required]="true" [readonly]="isCallerReadonly">
              </mms-select-menu>
              <mat-error>
                <c-validation-message></c-validation-message>
              </mat-error>
            </mat-form-field>
            <ng-template #addCallerTemplate let-data="">
              <mat-option (click)="addNewCaller()" *iHasPermission="Permissions.CreateCaller">
                <div class="d-flex align-items-center">
                  <mat-icon class="mr-1">add</mat-icon>
                  <span>Add New Caller</span>
                </div>
              </mat-option>
            </ng-template>
          </div>
          <div class="col-6">
            <mat-form-field>
              <mms-date-time-picker [pickerType]="mmsDateTimepickerType.Both" placeholder="Call Date"
                formControlName="callDateTime" #callDatePicker
                [value]="ticket.value?.callDateTime ? ticket.value.callDateTime : defaultDate">
              </mms-date-time-picker>
              <mms-date-time-picker-toggle matSuffix [for]="callDatePicker"></mms-date-time-picker-toggle>
              <mat-error>
                <c-validation-message></c-validation-message>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        <div class="row mt--10">
          <div class="col-12">
            <mat-form-field>
              <textarea matInput placeholder="Description" rows="1" formControlName="description"></textarea>
              <mat-error>
                <c-validation-message></c-validation-message>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        <div class="row">
          <div class="col-6">
            <mat-form-field> 
              <mms-select-user #user formControlName="assignedUsers" placeholder="Assign To" [showUsersOnly]="true"
                [isMultiple]="false" [required]="true" [ticketDropdown]="true">
              </mms-select-user>
              <mat-error>
                <c-validation-message></c-validation-message>
              </mat-error>
            </mat-form-field>
          </div>
          <div class="col-6">
            <mat-form-field>
              <mms-date-time-picker [pickerType]="mmsDateTimepickerType.Both" placeholder="Dispatch Date/Time"
                formControlName="dispatchDateTime" #dispatchDateTimePicker
                [value]="ticket.value?.dispatchDateTime ? ticket.value.dispatchDateTime : defaultDate">
              </mms-date-time-picker>
              <mms-date-time-picker-toggle matSuffix [for]="dispatchDateTimePicker"></mms-date-time-picker-toggle>
              <mat-error>
                <c-validation-message></c-validation-message>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        <div class="row mt--5">
          <div class="col-4">
            <mat-form-field>
              <mms-select-menu formControlName="priorityItem" [options]="priorities$ | async" placeholder="Priority"
                [enableClearSearchInput]="true" [valueMapper]="ticketItemValueMapper" [compareFn]="ticketItemCompareFn"
                [value]="ticket.value ? ticket.value.priorityItem : (defaultPriority$ | async)">
              </mms-select-menu>
              <mat-error>
                <c-validation-message></c-validation-message>
              </mat-error>
            </mat-form-field>
          </div>
          <div class="col-4">
            <mat-form-field>
              <mms-select-menu formControlName="corner" [options]="cornerItems" placeholder="Corner"
                [enableClearSearchInput]="true">
              </mms-select-menu>
              <mat-error>
                <c-validation-message></c-validation-message>
              </mat-error>
            </mat-form-field>
          </div>
          <div class="col-4">
            <mat-form-field>
              <mms-select-menu formControlName="direction" [options]="directionItems" placeholder="Direction"
                [enableClearSearchInput]="true">
              </mms-select-menu>
              <mat-error>
                <c-validation-message></c-validation-message>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        <div class="row mt--5">
          <div class="col-12 mt--10">
            <mat-form-field class="mini">
              <mms-file-upload matInput placeholder="Files" [fileContainer]="fileContainer" formControlName="files"
                class="mini" acceptedFiles=".png, .jpg, .pdf, .txt, .doc, .docx, .xlsx, .xlsm, .xlsb, .xltx, .xls">
              </mms-file-upload>
              <mat-error>
                <c-validation-message></c-validation-message>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        <div class="row col-md-12 justify-content-between m-0 mt--10">
          <button mat-raised-button class="ml--5" type="button" class="btn btn-default btn-link" (click)="close()"
            aria-label="Close">
            Cancel
          </button>
          <div>
            <button mat-raised-button type="button" class="btn btn-default btn-link" (click)="clear()"
              aria-label="Clear" *ngIf="ticket.value; else clearNew">
              Clear
            </button>
            <ng-template #clearNew>
              <button mat-raised-button type="button" class="btn btn-default btn-link" (click)="reset()"
                aria-label="Clear">
                Clear
              </button>
            </ng-template>
            <button mat-raised-button class="btn btn-primary" *ngIf="!(isLoading$ | async)" (click)="saveTicket()">
              Dispatch
            </button>
          </div>
        </div>
      </form>
    </div>
    <div class="col-7 border-left" *ngIf="showHistoryTable">
      <div class="card-header px-0 pb-2 border-bottom history-display">
        <h4 class="card-title mt-2 pt-2 d-inline-block">
          <span>History - {{selectedLocation}}</span>
        </h4>
        <div class="float-right">
        <button class="mb-2 mt-5" mat-icon-button (click)="closeHistory()">
          <span class="close mt--4 mat-button">Close</span>
        </button>
        </div>
        <mms-map-history class="call-ticket-history-table"></mms-map-history>
      </div>
    </div>
  </div>
</div>
