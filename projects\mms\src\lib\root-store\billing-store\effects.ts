import { ApprovalStatusHistory } from './../../configuration/tickets/approval-status-history.model';
import { Bill, Filter } from './../../payroll-and-billing/billing/models/bill.model';
import { ContractService } from './../../payroll-and-billing/contracts/contract.service';
import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import * as featureActions from './actions';
import * as featureState from './state';
import * as featureSelectors from './selectors'
import { exhaustMap, concatMap, catchError, withLatestFrom, switchMap, map, tap, mergeMap } from "rxjs/operators";
import { BillingService } from "../../payroll-and-billing/billing/billing.service";
import { GridStoreActions } from "../shared-store/material-grid-store";
import { of as observableOf, of } from "rxjs";
import { select, Store } from '@ngrx/store';
import { Page } from '../../core/page.service';
import { ApprovalStatusChange } from '../../payroll-and-billing/timesheets/models/approve-tasks-request.model';
import { ApprovalStatus } from '../../configuration/tickets/tickets-enum/ticket-status.enum';
import { UserIdentity } from '@econolite/identity-client';
import { AuthStatus } from '../../payroll-and-billing/billing/models/auth-status.model';
import { Router } from '@angular/router';
@Injectable()
export class BillingStoreEffects {
    constructor(private actions$: Actions,
        private billingService: BillingService,
        private contractService: ContractService,
        private store$: Store<any>,
        private page: Page,
        public userIdentity: UserIdentity,
        private router: Router) {
    }

    filterBills$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.FilterBills>(featureActions.ActionTypes.FILTER_BILLS),
        switchMap((action) => {
            if (action.payload.filter && action.payload.filter.agency === "0") {
                action.payload.filter.agency = "";
            }
            if (action.payload.filter && action.payload.filter.project === "0") {
                action.payload.filter.project = "";
            }
            return this.billingService.filterBills(action.payload.filter).pipe(
                concatMap((items: any) => {
                    const sortedItems = items.items;
                    sortedItems.sort((x: any, y: any) => {
                        return (x.authStatus < y.authStatus) ? 0 : x.authStatus ? -1 : 1;
                    });
                    return [
                        new featureActions.LoadBillsSuccess({ items: sortedItems }),
                        new GridStoreActions.GridLoadItemsSuccessAction(featureState.BILLS_GRID_ID, { items: sortedItems })
                    ];
                }),
                catchError(error => of(new featureActions.LoadFailure({ error })))
            );
        })
    ));

    loadBillContractEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadBillContractRequest>(featureActions.ActionTypes.LOAD_BILL_CONTRACT_REQUEST),
        withLatestFrom(this.store$.pipe(select(featureSelectors.getSelectedBill))),
        switchMap(([action, selectedBill]) =>
            this.contractService.getContractById(selectedBill.contractId).pipe(
                map(contract => new featureActions.LoadBillContractSuccess({ contract })),
                catchError(error => observableOf(new featureActions.LoadFailure({ error })))
            )
        )
    ));

    loadBillJurisdictionsConfigurationEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadBillJurisdictionConfigurationRequest>(featureActions.ActionTypes.LOAD_BILL_JURISDICTION_CONFIGURATION_REQUEST),
        withLatestFrom(this.store$.pipe(select(featureSelectors.getSelectedBill))),
        switchMap(([action, selectedBill]) =>
            this.contractService.getJurisdictionConfigurationById(selectedBill.jurisdictionConfigurationId).pipe(
                map(jurisdictionConfiguration => new featureActions.LoadBillJurisdictionConfigurationSuccess({ jurisdictionConfiguration })),
                catchError(error => observableOf(new featureActions.LoadFailure({ error })))
            )
        )
    ));

    updateBillEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateBillAction>(featureActions.ActionTypes.UPDATE_BILL),
        withLatestFrom(
            this.store$.pipe(select(featureSelectors.getSelectedBill)),
            this.store$.pipe(select(featureSelectors.selectBillLinesGridItems)),
        ),
        map(([action, selectedBill, billLineItems]) => {
            const bill: Bill = {
                ...selectedBill,
                lineItems: billLineItems
            };
            return new featureActions.UpdateRequestAction({ item: bill });
        })
    ));

    updateRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateRequestAction>(featureActions.ActionTypes.UPDATE_BILL_REQUEST),
        withLatestFrom(this.store$.select(featureSelectors.selectFilter)),
        exhaustMap(([action, filter]) =>
            this.billingService.updateBill(action.payload.item).pipe(
                switchMap((updatedBill) => [
                    new featureActions.UpdateSuccessAction({ item: updatedBill }),
                    new featureActions.FilterBills({ filter: { authorized: filter?.authorized } as Filter })
                ]),
                catchError(error => observableOf(new featureActions.LoadFailure({ error })))
            )
        )
    ));

    updateMultipleBills$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateMultipleBills>(featureActions.ActionTypes.UPDATE_MULTIPLE_BILLS),
        mergeMap((action) =>
            this.billingService.updateMultipleBills(action.payload.items).pipe(
                map(() => new featureActions.NoAction()),
                catchError(error => of(new featureActions.LoadFailure({ error })))
            )
        )
    ));

    updateRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateSuccessAction>(featureActions.ActionTypes.UPDATE_BILL_SUCCESS),
        tap(() => {
            this.page.notification.show('Bill updated');
        })
    ), { dispatch: false });

    authorizeBillEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateBillAction>(featureActions.ActionTypes.AUTHORIZE_BILL),
        withLatestFrom(
            this.store$.pipe(select(featureSelectors.getSelectedBill)),
            this.store$.pipe(select(featureSelectors.selectUnauthorizedAndReturnedBills))
        ),
        concatMap(([_, selectedBill, bills]) => {
            const actions = [];
            const bill: Bill = {
                ...selectedBill,
                authStatus: AuthStatus.Authorized
            };
            const user = {
                id: this.userIdentity.id,
                name: this.userIdentity.fullName
            };
            const historyRecord = {
                date: new Date(),
                user,
                status: ApprovalStatus.Authorized,
                comment: ""
            } as ApprovalStatusHistory;
            const approvalStatusChange = {
                ticketId: bill.ticketId,
                status: ApprovalStatus.Authorized,
                historyRecord
            } as ApprovalStatusChange;
            const nextBill = bills.filter(b => b.id !== bill.id)[0];
            if (nextBill) {
                this.router.navigate(['/payroll-and-billing/billing/single-task-bill/' + nextBill.id]);
            } else {
                this.router.navigate(['/payroll-and-billing/billing/list']);
                actions.push(new featureActions.FilterBills({ filter: { authorized: false } as Filter }));
            }
            actions.push(new featureActions.UpdateRequestAction({ item: bill }));
            actions.push(new featureActions.ChangeTaskApprovalStatus({ approvalStatusChange }));
            return actions;
        })
    ));

    unAuthorizeBillEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UnAuthorizeBill>(featureActions.ActionTypes.UNAUTHORIZE_BILL),
        concatMap((action) => {
            const bill: Bill = {
                ...action.payload.bill,
                authStatus: AuthStatus.Returned
            };
            const user = {
                id: this.userIdentity.id,
                name: this.userIdentity.fullName
            };
            const historyRecord = {
                date: new Date(),
                user,
                status: ApprovalStatus.Approved,
                comment: "Unauthorized"
            } as ApprovalStatusHistory;
            const approvalStatusChange = {
                ticketId: bill.ticketId,
                status: ApprovalStatus.Approved,
                historyRecord
            } as ApprovalStatusChange;
            return [
                new featureActions.UpdateRequestAction({ item: bill }),
                new featureActions.ChangeTaskApprovalStatus({ approvalStatusChange })
            ];
        })
    ));

    changeTaskApprovalStatusEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ChangeTaskApprovalStatus>(featureActions.ActionTypes.CHANGE_TASK_APPROVAL_STATUS),
        exhaustMap((action) =>
            this.billingService.changeTaskApprovalStatus(action.payload.approvalStatusChange).pipe(
                switchMap(() => [new featureActions.NoAction()]),
                catchError(error => observableOf(new featureActions.LoadFailure({ error })))
            )
        )
    ));

    deleteRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteRequestAction>(featureActions.ActionTypes.DELETE_BILL_REQUEST),
        switchMap((action) =>
            this.billingService.deleteBill(action.payload.billId || '').pipe(
                map(() => {
                    this.page.notification.show('Bill removed');
                    return new featureActions.DeleteSuccessAction(action.payload);
                })
            )
        )
    ));

    deleteRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteSuccessAction>(featureActions.ActionTypes.DELETE_BILL_SUCCESS),
        tap(() => {
            this.page.notification.show('Bill removed from queue');
            history.back();
        })
    ), { dispatch: false });

    returnTaskToPMEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ReturnTaskToPM>(featureActions.ActionTypes.RETURN_TO_PM),
        withLatestFrom(this.store$.pipe(select(featureSelectors.getSelectedBill))),
        concatMap(([action, selectedBill]) => {
            const user = {
                id: this.userIdentity.id,
                name: this.userIdentity.fullName
            };
            const historyRecord = {
                date: new Date(),
                user,
                status: ApprovalStatus.Rejected,
                comment: action.payload.comment
            } as ApprovalStatusHistory;
            const approvalStatusChange = {
                ticketId: selectedBill.ticketId,
                status: ApprovalStatus.Rejected,
                historyRecord
            } as ApprovalStatusChange;
            return [
                new featureActions.DeleteRequestAction({ billId: selectedBill.id }),
                new featureActions.ChangeTaskApprovalStatus({ approvalStatusChange })
            ];
        })
    ));
}