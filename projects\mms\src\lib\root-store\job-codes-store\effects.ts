import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, select } from '@ngrx/store';
import { of as observableOf, pipe, of } from 'rxjs';
import { catchError, map, switchMap, tap, withLatestFrom, exhaustMap, concatMap } from 'rxjs/operators';

import * as featureActions from './actions';
import * as featureState from './state'
import * as featureSelectors from './selectors'

import { Router } from '@angular/router';
import { Page } from '../../core/page.service';
import { FormResetAction } from '../../shared/forms/connect-form-ngrx';
import { displayMessages } from '../../core/resources/display-messages';
import { User } from '../../configuration/shared/user-and-groups/user.model';
import { UserService } from '../../configuration/shared/user-and-groups/users.service';
import { UserIdentity } from '@econolite/identity-client';
import { MatDialog } from '@angular/material/dialog';
import { JobCode } from '../../configuration/job-codes/models/job-code.model';
import { JobCodeService } from '../../configuration/job-codes/services/job-code.service';
import { GridStoreActions } from '../shared-store/material-grid-store';
import { JOB_CODE_ASSIGNED_USERS_GRID_ID } from './state';

@Injectable()
export class JobCodeStoreEffects {
  constructor(
    private router: Router,
    private jobCodeService: JobCodeService,
    private actions$: Actions,
    private store$: Store<featureState.State>,
    private page: Page,
    private userService: UserService,
    private userIdentity: UserIdentity,
    public dialog: MatDialog) { }

  loadRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(
      featureActions.ActionTypes.LOAD_REQUEST
    ),
    switchMap(action =>
      this.jobCodeService
        .getJobCodes()
        .pipe(
          map(
            items =>
              new featureActions.LoadSuccessAction({
                items
              })
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  saveRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveRequestAction>(
      featureActions.ActionTypes.SAVE_REQUEST
    ),
    exhaustMap((action) =>
      this.jobCodeService
        .addJobCode(action.payload.item)
        .pipe(
          concatMap((jobCode) => {
            return [
              new featureActions.AssignUsers({ jobCode: jobCode! }),
              new featureActions.SaveSuccessAction({
                item: jobCode!
              }),
              new featureActions.LoadRequestAction()
            ]
          }
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  updateRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateRequestAction>(
      featureActions.ActionTypes.UPDATE_REQUEST
    ),
    exhaustMap((action) =>
      this.jobCodeService
        .updateJobCode(action.payload.item)
        .pipe(
          switchMap(
            () => [new featureActions.AssignUsers({ jobCode: action.payload.item }),
            new featureActions.UpdateSuccessAction({ item: { id: action.payload.item.id!, changes: action.payload.item } }),
            new featureActions.LoadRequestAction()]
          ),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        )
    )
  ));

  updateRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateSuccessAction>(
      featureActions.ActionTypes.UPDATE_SUCCESS
    ),
    tap(() => {
      this.page.notification.show('Job Code updated');
    })
  ), { dispatch: false });

  saveRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveSuccessAction>(
      featureActions.ActionTypes.SAVE_SUCCESS
    ),
    map(() => {
      this.page.notification.show('Job Code added');
      return new FormResetAction();
    })
  ));

  confirmDeleteJobCodeEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmDeleteJobCodeAction>(
      featureActions.ActionTypes.CONFIRM_DELETE_JOB_CODE
    ),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectedJobCode))),
    exhaustMap(([action, selectedJobCode]: [featureActions.ConfirmDeleteJobCodeAction, JobCode]) =>
      this.page
        .confirm.show('Are you sure you want to delete this job code?', 'Are you sure?')
        .pipe(
          map(
            (result) => {
              if (result) {
                return new featureActions.DeleteRequestAction();
              }
              return new featureActions.NoAction();
            }
          )
        )
    )
  ));

  deleteRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteRequestAction>(
      featureActions.ActionTypes.DELETE_REQUEST
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedJobCode)),
      this.store$.pipe(select(featureSelectors.selectAssignedUsersIds))
    ),
    switchMap(([action, selectedJobCode, userIds]: [featureActions.DeleteRequestAction, JobCode, Array<string | undefined>]) => {
      const filteredUserIds = userIds.filter((id): id is string => id !== undefined);
      return this.jobCodeService
        .deleteJobCode(selectedJobCode.id!)
        .pipe(
          switchMap(res => [
            new featureActions.DeleteSuccessAction({
              itemId: selectedJobCode.id!
            }),
            new featureActions.DeleteJobCodeFromUsers({userIds: filteredUserIds, jobCodeId: selectedJobCode.id!})
          ]),
          catchError(error =>
            observableOf(new featureActions.LoadFailureAction({ error }))
          )
        );
    })
  ));

  deleteSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteSuccessAction>(
      featureActions.ActionTypes.DELETE_SUCCESS
    ),
    pipe(
      map(() => {
        this.page.notification.show('Job Code deleted');
      }),
      switchMap(res => [
        new FormResetAction(),
        new featureActions.AddNewAction(),
      ])
    )
  ));

  addNewEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AddNewAction>(
      featureActions.ActionTypes.ADD_NEW
    ),
    tap(() => {
      this.router.navigate(['/settings/job-codes']);
    })
  ), { dispatch: false });

  saveEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveAction>(
      featureActions.ActionTypes.SAVE
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedJobCode)),
    ),
    map(([action, selectedJobCode]: [featureActions.SaveAction, JobCode]) => {
      if (selectedJobCode == null) {
        return new featureActions.SaveRequestAction({ item: action.payload.item })
      } else {
        const jobCode = action.payload.item;
        jobCode.id = selectedJobCode.id;
        jobCode.tenantId = selectedJobCode.tenantId;
        return new featureActions.UpdateRequestAction({ item: jobCode })
      }
    })
  ));

  canBeDeletedEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.CanBeDeletedAction>(
      featureActions.ActionTypes.CAN_BE_DELETED
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectedJobCode)),
    ),
    switchMap(([_, selectedJobCode]: [featureActions.CanBeDeletedAction, JobCode]) =>
      this.jobCodeService.canBeDeleted(selectedJobCode.id!).pipe(
        map((response) => {
          if (!response) {
            return new featureActions.ConfirmDeleteJobCodeAction();
          } else {
            this.page.alert.warning(displayMessages.exceptions.itemIsAssociated('job code'));
            return new featureActions.CanceledDeleteJobCodeAction();
          }
        }),
        catchError(error =>
          of(new featureActions.LoadFailureAction({ error }))
        )
      )
    )
  ));

  assignUsers$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AssignUsers>(
      featureActions.ActionTypes.ASSIGN_USERS
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectUsers)),
    ),
    switchMap(([action, selectedUsers]: [featureActions.AssignUsers, User[]]) => {
      const jobCode = action.payload.jobCode;
      if (selectedUsers && selectedUsers.length) {
        selectedUsers.forEach(user => {
          user.assignedJobCodes = [jobCode];
        })
      }
      return this.userService.assignJobCodes(selectedUsers).pipe(
        map((result) => {
          return new featureActions.LoadRequestAction()
        }),
        catchError(error => {
          return of(new featureActions.LoadFailureAction({ error }))
        })
      )
    }),
    catchError(error => {
      return of(new featureActions.LoadFailureAction({ error }))
    })
  ));

  loadAssignedUsers$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadUsers>(
      featureActions.ActionTypes.LOAD_USERS
    ),
    switchMap((action: featureActions.LoadUsers) => {
      const jobCodeId = action.payload.jobCodeId;
      return this.userService.getUsersByJobCode(jobCodeId).pipe(
        map(users => {
          return new featureActions.LoadUsersSuccess({ users })
        }),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      )
    }),
    catchError(error => of(new featureActions.LoadFailureAction({ error })))
  ));

  loadAssignedUsersSuccess$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadUsersSuccess>(
      featureActions.ActionTypes.LOAD_USERS_SUCCESS
    ),
    map((action: featureActions.LoadUsersSuccess) => {
      return new GridStoreActions.GridLoadItemsSuccessAction(JOB_CODE_ASSIGNED_USERS_GRID_ID, { items: action.payload.users })
    }),
    catchError(error => of(new featureActions.LoadFailureAction({ error })))
  ));

  deleteJobCodeFromUsers$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteJobCodeFromUsers>(
      featureActions.ActionTypes.DELETE_JOB_CODE_FROM_USERS
    ),
    switchMap((action: featureActions.DeleteJobCodeFromUsers) => {
      return this.userService.deleteJobCode(action.payload.userIds, action.payload.jobCodeId).pipe(
        map((result) => {
          return new featureActions.NoAction()
        }),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      )
    }),
    catchError(error => of(new featureActions.LoadFailureAction({ error })))
  ));

}