import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, Action, select } from '@ngrx/store';
import { ActivatedRoute, Router } from '@angular/router';

import * as featureActions from './actions';
import * as featureState from './state';
import * as featureSelectors from './selectors';

import { Observable, of, pipe, EMPTY, timer, of as observableOf } from 'rxjs';
import { switchMap, map, catchError, exhaustMap, withLatestFrom, mergeMap, debounce } from 'rxjs/operators';

import { IGridStoreEffects } from '../shared-store/material-grid-store/effects';
import { Page } from '../../core/page.service';
import { GridLoadItemsAction } from '../shared-store/material-grid-store/actions';
import { GridStoreActions } from '../shared-store/material-grid-store';
import { FormResetAction } from '../../shared/forms/connect-form-ngrx';
import { Ticket } from '../../configuration/tickets/tickets.model';
import { TicketService } from '../../configuration/tickets/tickets.service';
import { MMSConfigService } from '../../settings/mms_config/mms-config.service';
import { CallerService } from '../../configuration/callers/callers.service';
import { UserService } from '../../configuration/shared/user-and-groups/users.service';
import { Caller } from '../../configuration/callers/callers.model';
import { RouterStoreSelectors } from '../router-store';
import { RouterReducerState } from '@ngrx/router-store';
import { RouterStateUrl } from '../router-store/router-state.serializer';
import { UserIdentity } from '@econolite/identity-client';
import { CallerStoreActions, CallerStoreSelectors, CallerStoreState } from '../../root-store/callers-store/index';
import { LocationInstanceStoreState, LocationInstanceStoreActions, LocationInstanceStoreSelectors } from '../location-instance-store';
import { Filter } from '../../configuration/tickets/filter/filter.model';
import { TicketTypeStoreSelectors } from '../ticket-types-store';
import { TicketType } from '../../configuration/ticket-types/models/ticket-type.model';
import { ProjectStoreSelectors } from '../projects-store';
import { Project } from '../../configuration/projects/models/project.model';
import { AgencyStoreSelectors } from '../agency-store';
import { Agency } from '../../configuration/agencies/agency.model';
import { localStorageCompositeKeys } from '../../core/resources/local-storage-composite-keys';
import { PROJECTS_GRID_ID } from '../projects-store/state';
import * as mapStoreActions from '../map-store/actions';
import { ProjectCategory } from '../../configuration/projects/enums/project-category.enum';
import { ApprovalStatus, TicketStatus } from '../../configuration/tickets/tickets-enum/ticket-status.enum';
import { TicketQuickFilter } from '../../configuration/tickets/filter/quick-filter.model';
import { TicketTypeCategory } from '../../configuration/ticket-types/enums/ticket-category.enum';
import '../../shared/utility/date.extension';
import { LocationStoreActions, LocationStoreSelectors } from '../location-store';
import { TechAction } from '../../configuration/tickets/tech-action.model';
import { ActionType } from '../../configuration/tickets/tickets-enum/action-type.enum';
import { ContractStoreSelectors } from '../contract-store';
import { Contract } from '../../payroll-and-billing/contracts/models/contract.model';
import { ResponseTimeType } from '../../configuration/shared/models/average-response-time-chart-data';
import { TICKETS_LIST_DASHBOARD_GRID_ID } from './state';
import { ProjectStatus } from '../../configuration/projects/enums/project-status.enum';
import { PermissionsEnum } from '../../core/auth/permissions.enum';
import { TimesheetStoreSelectors } from '../timesheet-store';
import { Timesheet } from '../../payroll-and-billing/timesheets/models/timesheet.model';
import { PriorityEnum } from '../../configuration/tickets/tickets-enum/priority.enum';

// Type-safe interfaces
interface RouterParams {
  projectId?: string;
  locationId?: string;
  [key: string]: string | undefined;
}

interface SafeTicket extends Ticket {
  id: string;
  actions: TechAction[];
  timezone: string;
}

interface TypeSafeFilter extends Omit<Filter, 'projectId' | 'agencyId'> {
  agencies: string[];
  projectId: string | null;
  agencyId: string | null;
}

@Injectable()
export class TicketStoreEffects implements IGridStoreEffects {
  constructor(
    private actions$: Actions,
    private ticketService: TicketService,
    private store$: Store<featureState.State>,
    private page: Page,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private mmsConfigService: MMSConfigService,
    private callerService: CallerService,
    private userService: UserService,
    private userIdentity: UserIdentity,
    private locationInstanceStore$: Store<LocationInstanceStoreState.MainState>,
    private callerStore$: Store<CallerStoreState.MainState>,
    private locationStore$: Store<LocationInstanceStoreState.State>
  ) { }

  private getRouterParams(router: RouterReducerState<RouterStateUrl> | null): RouterParams {
    return (router?.state?.params || {}) as RouterParams;
  }

  private getProjectId(router: RouterReducerState<RouterStateUrl> | null): string {
    const params = this.getRouterParams(router);
    return params.projectId || '';
  }

  private getLocationId(router: RouterReducerState<RouterStateUrl> | null): string {
    const params = this.getRouterParams(router);
    return params.locationId || '';
  }

  private ensureArray<T>(value: T[] | undefined | null): T[] {
    return value || [];
  }

  private createTypeSafeFilter(
    filters: Filter,
    router: RouterReducerState<RouterStateUrl>,
    agency: Agency | null,
    searchText: string,
    quickFilter: TicketQuickFilter,
    userAssignedAgencies: string[],
    allAgencies: Agency[],
    isSysAdmin: boolean,
    canSeeAllAgencies: boolean
  ): TypeSafeFilter {
    const ids = (isSysAdmin || canSeeAllAgencies) && allAgencies?.length ? 
      allAgencies.map(a => a.id ?? '') : [];

    return {
      ...filters,
      projectId: null,
      agencyId: null,
      searchText,
      types: (router && router.state && router.state.url && (router.state.url.includes('call-tickets') || router.state.url.includes('/dashboard')))
        ? [TicketTypeCategory.Call] 
        : this.getTypeFilters(quickFilter),
      paging: true,
      agencies: isSysAdmin || canSeeAllAgencies ? ids : this.ensureArray(userAssignedAgencies).filter((id): id is string => id != null),
      isFinalizedClicked: quickFilter.finalized || false
    };
  }

  private getTypeFilters(quickFilter: TicketQuickFilter): TicketTypeCategory[] {
    return quickFilter.finalized ? [TicketTypeCategory.Call] : [];
  }

  // Effects
  loadRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(featureActions.ActionTypes.LOAD_REQUEST),
    switchMap(() => {
      return this.mmsConfigService?.getMMSConfigSettings().pipe(
        switchMap(mmsSettings => [
          new featureActions.LoadSuccessAction({ mmsSettings }),
          new GridLoadItemsAction(featureState.TICKET_GRID_ID) 
        ]),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      );
    })
  ));

  gridLoadItemsSuccess$ = createEffect(() => this.actions$.pipe(
    ofType(GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_SUCCESS(featureState.TICKET_GRID_ID)),
    map(() => new GridStoreActions.GridNoAction())
  ));

  gridLoadItemsFailed$ = createEffect(() => this.actions$.pipe(
    ofType(GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_FAILED(featureState.TICKET_GRID_ID)),
    map(() => new GridStoreActions.GridNoAction())
  ));

  loadSidebarRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadSidebarRequestAction>(featureActions.ActionTypes.LOAD_SIDEBAR_REQUEST),
    switchMap(() => {
      return this.userService.getUsers().pipe(
        map(data => new featureActions.LoadSidebarSuccessAction({ users: data })),
        catchError(error => of(new featureActions.LoadSidebarFailureAction({ error })))
      );
    })
  ));

  showCreateItemDialogEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ShowCreateTicketAction>(featureActions.ActionTypes.SHOW_CREATE_TICKET),
    withLatestFrom(this.store$.select(RouterStoreSelectors.getRouterStoreState)),
    switchMap(([_, router]) => {
      new FormResetAction();
      this.locationInstanceStore$?.dispatch(new LocationInstanceStoreActions.SearchTextChangedAction(['']));
      this.callerStore$?.dispatch(new CallerStoreActions.SearchChangeAction(''));
      const params = this.getRouterParams(router);
      const projectId = params.projectId ? `${params.projectId}/` : '';
      this.router?.navigate([`/tickets/${projectId}add-ticket`]);
      return of(new featureActions.NoAction());
    })
  ));

  showCreateCallTicketEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ShowCreateCallTicketAction>(
      featureActions.ActionTypes.SHOW_CREATE_CALL_TICKET
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    map(([_, router]: [featureActions.ShowCreateCallTicketAction, RouterReducerState<RouterStateUrl>]) => {     
      new FormResetAction();
      this.locationInstanceStore$.dispatch(new LocationInstanceStoreActions.SearchTextChangedAction(['']));
      this.callerStore$.dispatch(new CallerStoreActions.SearchChangeAction(''));
      this.router.navigate([`/call-tickets/add-ticket`]);
      return new featureActions.NoAction();
    })
  ));

  showEditTicketDialogEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ShowEditTicketAction>(featureActions.ActionTypes.SHOW_EDIT_TICKET),
    withLatestFrom(
      this.store$.select(featureSelectors.selectTicketsList),
      this.store$.select(featureSelectors.selectTicketFormData),
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    switchMap(([action, ticketList, ticketFormData, router]) => {
      if (!action.payload?.ticketId) {
        return EMPTY;
      }

      if (!ticketFormData) {
        const selectedTicket = ticketList?.find(t => t.id === action.payload.ticketId);
        if (selectedTicket?.locationData?.name) {
          this.locationInstanceStore$?.dispatch(
            new LocationInstanceStoreActions.SearchTextChangedAction([selectedTicket.locationData.name])
          );
          if (selectedTicket.caller) {
            this.callerStore$?.dispatch(new CallerStoreActions.SearchChangeAction(''));
          }
        }
      }

      if (router?.state?.url?.includes('call-tickets')) {
        return of(new featureActions.GetTicket({ ticketId: action.payload.ticketId }));
      }

      const projectId = this.getProjectId(router);
      this.router?.navigate([
        `/tickets/${projectId ? `${projectId}/` : ''}edit-ticket/${action.payload.ticketId}`
      ]);
      return EMPTY;
    })
  ));

  getTicketDetailsEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.GetTicketDetailsAction>(featureActions.ActionTypes.GET_TICKET_DETAILS),
    withLatestFrom(this.store$.select(featureSelectors.selectTicketsList)),
    mergeMap(([action, ticketList]) => {
      if (!action.payload?.ticketId) {
        return of(new featureActions.LoadFailureAction({ error: 'No ticket ID provided' }));
      }
      return this.ticketService.getTicket(action.payload.ticketId).pipe(
        switchMap(ticket => {
          if (!ticket) {
            return of(new featureActions.LoadFailureAction({ error: 'Ticket not found' }));
          }
          return [
            new GridStoreActions.GridNewItemAddedAction(featureState.TICKET_GRID_ID, { item: ticket }),
            new featureActions.GetTicketDetailsSuccessAction({ ticket })
          ];
        }),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      );
    })
  ));

  getTicketDetailsSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.GetTicketDetailsSuccessAction>(
      featureActions.ActionTypes.GET_TICKET_DETAILS_SUCCESS
    ),
    mergeMap((action: featureActions.GetTicketDetailsSuccessAction) => {
       return  [
         new featureActions.ShowEditTicketAction({ ticketId: action.payload.ticket.id }),
       ]
    })
  ));

  closeSidebarEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.CloseSidebarAction>(
      featureActions.ActionTypes.CLOSE_SIDEBAR
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState),
      this.store$.pipe(select(featureSelectors.selectTicketFormData))
    ),
    map(([_, routerStore, selectedTicket]: [featureActions.CloseSidebarAction, RouterReducerState<RouterStateUrl>, Ticket]) => {
      let projectId = routerStore && routerStore.state && routerStore.state.params && routerStore.state.params['projectId'] ? `${routerStore.state.params['projectId']}/` : '';
      if(!projectId && this.activatedRoute.snapshot.firstChild){
        projectId = this.activatedRoute.snapshot.firstChild.children[0].params['projectId'] ? `/${this.activatedRoute.snapshot.firstChild.children[0].params['projectId']}/` : '';
      }
      if (routerStore && routerStore.state && routerStore.state.url && (routerStore.state.url.indexOf('add-caller') > -1)) {
        this.router.navigate([(selectedTicket && selectedTicket.id) ? `/tickets${projectId}/edit-ticket/${selectedTicket.id}` : `/tickets${projectId}/add-ticket`]);    
      }
      else{
        const ticketSegment = routerStore.state.url.includes('call-tickets') ? '/call-tickets' : '/tickets';
        this.router.navigate([`${ticketSegment}${projectId}`]);
      }
      return new featureActions.NoAction();
    })
  ));

  addNewCallerEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AddNewCallerAction>(
      featureActions.ActionTypes.ADD_NEW_CALLER
    ), 
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    map(([_, router]: [featureActions.AddNewCallerAction, RouterReducerState<RouterStateUrl>]) => {
      if (router.state.url.includes('call-tickets')) {
        this.router.navigate([`/call-tickets/add-caller/`], { state : { shouldDeactivate : true }});
      }
      else if (router.state.url.includes('/dashboard/DispatchCallTicket')) {
        this.router.navigate([`/call-tickets/add-caller/`], { fragment: "DispatchCallTicket" ,state : { shouldDeactivate : true }});
      }
      else if (router.state.url.includes('/dashboard')) {
        this.router.navigate([`/call-tickets/add-caller/`], { state : { shouldDeactivate : true }});
      }
      else {
        let projectId = router.state.params['projectId'] ? `${router.state.params['projectId']}/` : '';
        if(!projectId && this.activatedRoute.snapshot.firstChild){
          projectId = this.activatedRoute.snapshot.firstChild.children[0].params['projectId'] ? `/${this.activatedRoute.snapshot.firstChild.children[0].params['projectId']}/` : '';
        }
        this.router.navigate([`/tickets/${projectId}add-caller/`], { state : { shouldDeactivate : true }});
      }
      return { type: '[Ticket] Add New Caller Complete' };
    })
  ), { dispatch: false });

  saveCallerEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveCallerAction>(
      featureActions.ActionTypes.SAVE_CALLER
    ), exhaustMap((action) => {
      return this.callerService
        .createCaller(action.payload.caller)
        .pipe(
          map(
            (newCaller) => {
              return new featureActions.SaveCallerSuccessAction({ caller: newCaller! }) ;
            }
          ),
          catchError(error =>
            of(new featureActions.LoadFailureAction({ error }))
          )
        )
      })
  ));

  saveCallerSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveCallerSuccessAction>(
      featureActions.ActionTypes.SAVE_CALLER_SUCCESS
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectTicketFormData)),
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    map(([action, selectedTicket, router]: [featureActions.SaveCallerSuccessAction, Ticket, RouterReducerState<RouterStateUrl>]) => {
      let projectId = router.state.params['projectId'] ? `${router.state.params['projectId']}/` : '';
      if(!projectId && this.activatedRoute.snapshot.firstChild){
        projectId = this.activatedRoute.snapshot.firstChild.children[0].params['projectId'] ? `/${this.activatedRoute.snapshot.firstChild.children[0].params['projectId']}/` : '';
      }
      this.page.notification.show('Caller added');
      if(router.state.url.includes('call-tickets/add-caller#DispatchCallTicket')) {
        this.router.navigate([`/dashboard/DispatchCallTicket`], { state : { shouldDeactivate : true }});
      }
      else if (router.state.url.includes('call-tickets')) {
        this.router.navigate([(selectedTicket && selectedTicket.id) ? `/call-tickets/edit-ticket/${selectedTicket.id}` 
        : `/call-tickets/add-ticket`], { state : { shouldDeactivate : true }});
      }
      else {
        this.router.navigate([(selectedTicket && selectedTicket.id) ? `/tickets/${projectId}edit-ticket/${selectedTicket.id}` 
        : `/tickets/${projectId}add-ticket`], { state : { shouldDeactivate : true }});
      }

      this.store$.dispatch(new CallerStoreActions.LoadCallerListRequestAction());
      return new CallerStoreActions.AddedNewCallerToState(action.payload.caller);     
    })
  ));

  showCallerDetailsEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ShowCallerDetailsAction>(
      featureActions.ActionTypes.SHOW_CALLER_DETAILS
    ),
    withLatestFrom(
      this.store$.pipe(select(RouterStoreSelectors.getRouterStoreState))
    ),
    map(([action, router]: [featureActions.ShowCallerDetailsAction, RouterReducerState<RouterStateUrl>]) => {
        let projectId = router.state.params['projectId'] ? `${router.state.params['projectId']}/` : '';
        if(!projectId && this.activatedRoute.snapshot.firstChild){
          projectId = this.activatedRoute.snapshot.firstChild.children[0].params['projectId'] ? `/${this.activatedRoute.snapshot.firstChild.children[0].params['projectId']}/` : '';
        }
        this.router.navigate([`/tickets/${projectId}caller-details/${action.payload.callerId}`], { state: { shouldDeactivate: true } });
        return { type: '[Ticket] Show Caller Details Complete' };
    })
  ), { dispatch: false });

  showTicketDetails$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ShowTicketDetailsAction>(
      featureActions.ActionTypes.SHOW_TICKET_DETAILS
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    map(([action, router]: [featureActions.ShowTicketDetailsAction, RouterReducerState<RouterStateUrl>]) => {
      if (router.state.url.includes('call-tickets')) {
        this.router.navigate([`/call-tickets/ticket-details/${action.payload.ticketId}`]);
      }
      else {
        const projectId = router.state.params['projectId'] ? `${router.state.params['projectId']}/` : '';
        this.router.navigate([`/tickets/${projectId}ticket-details/${action.payload.ticketId}`]);
      }
      return { type: '[Ticket] Show Ticket Details Complete' };
    })
  ), { dispatch: false });

  saveEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveAction>(
      featureActions.ActionTypes.SAVE
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectSelectedTicket)),
      this.locationStore$.pipe(select(LocationInstanceStoreSelectors.selectAllLocationsAndRegions)),
      this.locationStore$.pipe(select(AgencyStoreSelectors.selectDefaultAgency)),
      this.locationStore$.pipe(select(LocationStoreSelectors.getLocationTypesChecklist)),
      this.store$.pipe(select(RouterStoreSelectors.getRouterStoreState)),
    ),
    map(([action, selectedTicket, locations, agency, locationtypesChecklists, router]) => {
        let ticket: Ticket = {
            ...action.payload.ticket,
            project: {
                ...action.payload.ticket.project!,
                agencyId: action.payload.ticket.project?.agency?.id ?? ''
            },
            actions: selectedTicket ? selectedTicket.actions : [],
            timezone: agency?.timezone || '',
            checklist: action.payload.ticket.type?.category === TicketTypeCategory.PM && selectedTicket == null && locationtypesChecklists?.length > 0 ?
            locationtypesChecklists[0].checklist
            : selectedTicket?.checklist || undefined
      };
        if (ticket.project && ticket.project.status != ProjectStatus.Open && (router.state.url.includes('/call-tickets') || router.state.url.includes('/dashboard')))   
        {
          let text = "A dispatch Project is not assigned to the selected Agency and a Call Ticket cannot be created. Contact an administrator to setup a default dispatch Project.";
          return new featureActions.ErrorDispatchCallAction(text);
        }
  
        if (selectedTicket == null) {
          ticket.approvalStatus = ApprovalStatus.Current;
          ticket.creationDate = Date.prototype.fromTimeZone(new Date(), agency?.timezone || '');
          if (ticket.activeUsers) 
              ticket.dispatchDateTime = Date.prototype.fromTimeZone(new Date(), agency?.timezone || '');
          return new featureActions.SaveRequestAction(ticket);
        } else {
          // We need to send existing actions also, if they exist, or we will close them
          ticket = {
            ...selectedTicket,
            ...ticket
          };
          ticket.id = selectedTicket.id;
          ticket.checklist = selectedTicket.checklist;
          let lastAction = ticket.actions && ticket.actions.length?ticket.actions[ticket.actions.length - 1] : null;
          if (lastAction && !lastAction.endDate && (lastAction.actionType == ActionType.Enroute || lastAction.actionType == ActionType.Onsite)  && (ticket.status == TicketStatus.Finalized || ticket.status == TicketStatus.ClosedPending)) {
            (ticket.actions ?? [])[(ticket.actions ?? []).length - 1].endDate = Date.prototype.fromTimeZone(new Date(), ticket.timezone ?? '');
            var actionType = ticket.status == TicketStatus.Finalized ? ActionType.Finalize : ActionType.StopWork;
            let techAction: TechAction = {
              endDate: Date.prototype.fromTimeZone(new Date(), ticket.timezone ?? ''),
              startDate: Date.prototype.fromTimeZone(new Date(), ticket.timezone ?? ''),
              actionType: actionType,
              eta: new Date(),
              userId: (ticket.actions ?? [])[(ticket.actions ?? []).length - 1].userId,
              userName: (ticket.actions ?? [])[(ticket.actions ?? []).length - 1].userName,
            }
            ticket.actions?.push(techAction);
          }
          return new featureActions.UpdateRequestAction(ticket);
      }
    })
  ));

  saveRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveRequestAction>(
      featureActions.ActionTypes.SAVE_REQUEST
    ),
    exhaustMap((action) => 
        this.ticketService
          .createTicket(action.payload)
          .pipe(
            map(
              (ticket) => {
                return new featureActions.SaveSuccessAction(ticket);
              }
            ),
            catchError(error =>
              of(new featureActions.LoadFailureAction({ error }))
            )
          )
    )
  ));

  saveMultipleEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveMultipleAction>(
      featureActions.ActionTypes.SAVE_MULTIPLE
    ),
    map((action)  => {
        return  new featureActions.SaveMultipleRequestAction(action.payload.multipleTicketsRequest);   
    }
    )
  ));

  saveMultipleRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveMultipleRequestAction>(
      featureActions.ActionTypes.SAVE_MULTIPLE_REQUEST
    ),
    withLatestFrom(
      this.locationStore$.pipe(select(AgencyStoreSelectors.selectDefaultAgency)),
      this.store$.pipe(select(LocationStoreSelectors.getLocationTypesChecklist)),
    ),
    exhaustMap(([action, agency, locationTypesChecklist]) => {
      action.payload.ticket = {
        ...action.payload.ticket,
        project: {
          ...action.payload.ticket?.project!,
          agencyId: action.payload.ticket?.project?.agency?.id ?? ''
        },
        actions: [],
        timezone: agency?.timezone || '',
        creationDate: Date.prototype.fromTimeZone(new Date(), agency?.timezone || ''),
        dispatchDateTime: Date.prototype.fromTimeZone(new Date(), agency?.timezone || '')
      };
      action.payload.locationTypesChecklists = action.payload.ticket?.type?.category === TicketTypeCategory.PM ? locationTypesChecklist || [] : [];
      return this.ticketService
        .createMultipleTickets(action.payload)
        .pipe(
          map(
            (tickets) => {
              return new featureActions.SaveMultipleSuccessAction(tickets);
            }
          ),
          catchError(error =>
            of(new featureActions.LoadFailureAction({ error }))
          )
        )
    }
    )
  ));

  updateRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateRequestAction>(
      featureActions.ActionTypes.UPDATE_REQUEST
    ),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectSelectedTicket))),
    exhaustMap(([action, _]: [featureActions.UpdateRequestAction, Ticket]) => 
        this.ticketService
          .updateTicket(action.payload)
          .pipe(
            map(
              (updatedTicket) => { 
               return  new featureActions.UpdateSuccessAction({ id: updatedTicket.id!, changes: updatedTicket })
              }
            ),
            catchError(error =>
              of(new featureActions.LoadFailureAction({ error }))
            )
          )
    )
  ));

  updateRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.UpdateSuccessAction>(
      featureActions.ActionTypes.UPDATE_SUCCESS
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    map(([action, router]) => {
      this.page?.notification?.show('Ticket updated');
      if (router && router.state && router.state.url && router.state.url.includes('/call-tickets')) {
        this.router?.navigate(['/call-tickets'], { state: { shouldDeactivate: true }});
      } else {
        const projectId = this.getProjectId(router);
        this.router?.navigate([`/tickets${projectId ? `/${projectId}` : ''}`], { state: { shouldDeactivate: true }});
      }
      return new GridStoreActions.GridItemUpdatedAction(featureState.TICKET_GRID_ID, { 
        itemId: action.payload.changes.id, 
        item: action.payload.changes 
      });
    })
  ));

  saveRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveSuccessAction>(
      featureActions.ActionTypes.SAVE_SUCCESS
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    switchMap(([action, router]) => {
      this.store$?.dispatch(new GridStoreActions.GridLoadItemsSuccessAction(PROJECTS_GRID_ID, { items: [] }));
      this.page?.notification?.show('Ticket added');
      
      if (router?.state?.url?.includes('call-tickets')) {
        this.router?.navigate(['/call-tickets'], { state: { shouldDeactivate: true }});
      } else if (router?.state?.url?.includes('DispatchCallTicket')) {
        this.router?.navigateByUrl('/dashboard', { state: { shouldDeactivate: true }});
      } else {
        const projectId = router?.state?.params?.['projectId'] ? `/${router.state.params['projectId']}` : '';
        this.router?.navigate([`/tickets${projectId}`], { state: { shouldDeactivate: true }});
      }

      return [
        new GridStoreActions.GridNewItemAddedAction(featureState.TICKET_GRID_ID, { item: action.payload }),
        new GridStoreActions.GridLoadItemsAction(featureState.TICKET_GRID_ID),
        new LocationStoreActions.SetLocationTypeChecklistConfigAction({ locationTypesChecklists: [] }),
        new CallerStoreActions.LoadCallerListRequestAction()
      ];
    })
  ));

  confirmDeleteTicketEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmDeleteAction>(
      featureActions.ActionTypes.CONFIRM_DELETE
    ),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectSelectedTicket))),
    exhaustMap(([action, selectedTicket]: [featureActions.ConfirmDeleteAction, Ticket]) => {
      if (!action.payload?.ticket) {
        return of(new featureActions.NoAction());
      }
      let msg = action.payload.ticket.actions && action.payload.ticket.actions.length > 0 ?
        `This task includes work periods and all time logged to  the task will be deleted resulting
       in the tech not being paid and the customer not being billed for the work. Are you sure you 
       want to delete this task?` : 'Are you sure you want to delete this ticket?'
      return this.page
        .confirm.show(msg, 'Are you sure?')
        .pipe(
          map(
            (result) => {
              if (result && action.payload?.ticket?.id) {
                return new featureActions.DeleteRequestAction({ticketId: action.payload.ticket.id});
              }
              return new featureActions.NoAction();
            }
          )
        )
    })
  ));

  deleteRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteRequestAction>(
      featureActions.ActionTypes.DELETE_REQUEST
    ),
    switchMap((action) => {
      if (!action.payload?.ticketId) {
        return of(new featureActions.LoadFailureAction({ error: 'No ticket ID provided' }));
      }
      return this.ticketService?.deleteTicket(action.payload.ticketId).pipe(
        map(() => {
          this.page?.notification?.show('Ticket deleted');
          return new featureActions.DeleteSuccessAction(action.payload);
        }),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      );
    })
  ));

  deleteSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DeleteSuccessAction>(
      featureActions.ActionTypes.DELETE_SUCCESS
    ),
    pipe(
      switchMap(res => [
        new GridStoreActions.GridLoadItemsSuccessAction(PROJECTS_GRID_ID, {items: []}),
        new FormResetAction(),
        new GridStoreActions.GridLoadItemsAction(featureState.TICKET_GRID_ID),
        new featureActions.AddNewAction()
      ])
    )
  ));

  addNewEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AddNewAction>(
      featureActions.ActionTypes.ADD_NEW
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    map(([_, router]: [featureActions.AddNewAction, RouterReducerState<RouterStateUrl>]) => {
      if (!router.state.url.includes('call-tickets') || router.state.url.includes('/dashboard')) {
        const projectId = router.state.params['projectId'] ? `/${router.state.params['projectId']}` : '';
        this.router.navigate([`/tickets${projectId}`]);
      }
      return new featureActions.NoAction();
    })
  ));

  gridLoadItems$ = createEffect(() => this.gridLoadItems(GridStoreActions.ActionTypes.GRID_LOAD_ITEMS(featureState.TICKET_GRID_ID)));

  showDispatchTicket$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ShowDispatchTicketAction>(
      featureActions.ActionTypes.SHOW_DISPATCH_TICKET
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    map(([action, router]: [featureActions.ShowDispatchTicketAction, RouterReducerState<RouterStateUrl>]) => {
      if (router.state.url.includes('call-tickets') || router.state.url.includes('/dashboard')) {
        this.router.navigate([`/call-tickets/dispatch-ticket/${action.payload.ticketId}`]);
      }
      else {
        const projectId = router.state.params['projectId'] ? `${router.state.params['projectId']}/` : '';
        this.router.navigate([`/tickets/${projectId}dispatch-ticket/${action.payload.ticketId}`]);
      }
      return new featureActions.NoAction();
    })
  ));

  dispatchRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.DispatchTicketRequestAction>(
      featureActions.ActionTypes.DISPATCH_TICKET_REQUEST
    ),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectSelectedTicket))),
    switchMap(([action, ticket]) => {
      const currentUsers = ticket.assignedUsers;
      if (!currentUsers || !currentUsers.length) {
        ticket.dispatchDateTime =  Date.prototype.fromTimeZone(new Date(), ticket.timezone ?? '');
      }
      if (ticket.status === TicketStatus.Available) {
        ticket.status = TicketStatus.Assigned;
      }
      // Convert scheduler UserAndGroup to configuration UserAndGroup
      ticket.assignedUsers = action.payload.assignedUsers?.filter(user => user.id).map(user => ({
        id: user.id!,
        type: user.type || 0,
        name: user.name || '',
        email: user.email || ''
      })) || [];
      ticket.project = {
        ...ticket.project!,
        agencyId: ticket.project?.agencyId ?? ''
      }

      return this.ticketService.updateTicket(ticket).pipe(
        switchMap(updatedTicket => {
          this.page.notification.show('Ticket dispatched');
          return [
            new featureActions.DispatchTicketSuccessAction({ id: ticket.id!, changes: updatedTicket }),
            new GridStoreActions.GridItemUpdatedAction(featureState.TICKET_GRID_ID, { itemId: ticket.id, item: updatedTicket }),
            new featureActions.CloseSidebarAction()
          ]
        }),
        catchError(error => of(new featureActions.DispatchTicketFailureAction({ error })))
      )
     }
    )
  ));

  showFilter$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ShowFilterAction>(
      featureActions.ActionTypes.SHOW_FILTER
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    map(([_, router]: [featureActions.ShowFilterAction, RouterReducerState<RouterStateUrl>]) => {
      if (router.state.url.includes('call-tickets') || router.state.url.includes('/dashboard')) {
        this.router.navigate([`/call-tickets/filter`])
      } else {
        const projectId = router.state.params['projectId'] ? `${router.state.params['projectId']}/` : '';
        this.router.navigate([`/tickets/${projectId}filter`]);
      }
      return new LocationInstanceStoreActions.SearchTextChangedAction(['']);
    })
  ));

  applyFilter = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ApplyFilterAction>(
      featureActions.ActionTypes.APPLY_FILTER
    ),
    withLatestFrom(
      this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    map(([action, agency, router]: [featureActions.ApplyFilterAction, Agency | undefined | null, RouterReducerState<RouterStateUrl>]) => {
      if (!router.state.url.includes('call-tickets')) {
        action.payload.agencyId = agency?.id || localStorage.getItem(localStorageCompositeKeys.defaultTenantAgency(this.userIdentity.tenantId)) || '';
      }
      return new featureActions.SearchRequestAction(action.payload);
    })
  ));

  filterTicketByLocationEffect = createEffect(() => this.actions$.pipe(
    ofType<featureActions.TicketFilterLocationAction>(
      featureActions.ActionTypes.TICEKT_FILTER_LOCATION
    ),
    withLatestFrom(this.store$.pipe(select(featureSelectors.selectSelectedFilter))),
    switchMap(([action, selectedFilter]: [featureActions.TicketFilterLocationAction, Filter | null]) => {
      if(selectedFilter && selectedFilter.locations){
        return of(new LocationInstanceStoreActions.SearchTextChangedAction(selectedFilter.locations.map(f => f.id).filter((id): id is string => id != null)));
      }
      return EMPTY;
    })
  ));

  searchRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SearchRequestAction>(
      featureActions.ActionTypes.SEARCH_REQUEST
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    exhaustMap(([action, router]: [featureActions.SearchRequestAction, RouterReducerState<RouterStateUrl>]) => {
      let checkSelectedProject = router.state.url && router.state.url.toString();
      const urlSplit = checkSelectedProject.split("/");
      const projectId = urlSplit && urlSplit[2] && urlSplit[2].toString();
      let chekRouteProject = projectId !== 'filter' ? true : false;
      const filter = {
        ...action.payload,
        projectId: chekRouteProject ? projectId : router.state.params['projectId'],
        paging: true
      } as Filter;
      return this.ticketService.getTicketList(filter)
        .pipe(map((res) => {
          return new featureActions.SearchSuccessAction({ tickets: res.body.items, nextPagToken: res.nextPageToken  });
        }
        ),
          catchError(error =>
            of(new featureActions.SearchFailureAction({ error }))
          )
        )
    })
  ));

  searchSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SearchSuccessAction>(
      featureActions.ActionTypes.SEARCH_SUCCESS
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    switchMap(([action, router]: [featureActions.SearchSuccessAction, RouterReducerState<RouterStateUrl>]) => {
      let checkSelectedProject = router.state.url && router.state.url.toString();
      const urlSplit = checkSelectedProject.split("/");
      const project = urlSplit && urlSplit[2] && urlSplit[2].toString();
      let chekRouteProject = project != 'filter' ? true : false;
      let checkProjectId = chekRouteProject ? project : router.state.params['projectId'];
      const projectId = checkProjectId ? `/${checkProjectId}` : '';
      const actions = [];
      if (this.router.url.match(/tickets/g) && !this.router.url.includes('call-tickets')) {
        this.router.navigate([`/tickets${projectId}`]);
      } else if (this.router.url.match(/map/g) || this.router.url.match(/dashboard/g)) {
        actions.push( new mapStoreActions.LoadLocationTypesAction() );
      } else if(this.router.url.includes('call-tickets')) {
        this.router.navigate([`/call-tickets`])
      }

      actions.push ( new GridStoreActions.GridLoadItemsSuccessAction(featureState.TICKET_GRID_ID, { items: action.payload.tickets, nextPageToken: action.payload.nextPagToken, clearGrid: true }) )
      return actions;
    })
  ));

  loadTicketDetailsEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadTicketDetailsAction>(
      featureActions.ActionTypes.LOAD_TICKET_DETAILS
    ), withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectTicketsList))
    ),
    switchMap(([action, tickets]: [featureActions.LoadTicketDetailsAction, Ticket[]]) => {
      if(!tickets.find(x=> x.id == action.payload.ticketId)) {
        return this.ticketService.getTicket(action.payload.ticketId).pipe(
          map(data => {
            if(data){
              return new GridStoreActions.GridNewItemAddedAction(featureState.TICKET_GRID_ID, {item: data});
            }
            return new featureActions.LoadTicketDetailsSuccessAction();
          }),
          catchError(error => of(new featureActions.LoadFailureAction({ error })))
        );
      }
      return of(new featureActions.LoadTicketDetailsSuccessAction());
    })
  ));

  saveNotesEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveNotesAction>(
      featureActions.ActionTypes.SAVE_NOTES
    ),
    withLatestFrom(
      this.store$.pipe(select(CallerStoreSelectors.selectedCallerDetails))
    ),
    map(([action, selectedCaller]: [featureActions.SaveNotesAction, Caller | null]) => {
      if (selectedCaller != null) {
        selectedCaller.notes = action.payload;
        return new CallerStoreActions.UpdateRequestNotesAction(selectedCaller);
      }

      return new CallerStoreActions.NoAction();
    })
  ));

  addTicketDataSuccesEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.AddTicketFromData>(
      featureActions.ActionTypes.GET_CALLER_DETAILS
    ),
    map((action) => {
      return new CallerStoreActions.GetCallerDetailsAction({ caller: action.payload.caller});
    })
  ));

  returnToLocationsEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ReturnToLocationsAction>(
      featureActions.ActionTypes.RETURN_TO_LOCATIONS
    ),
    withLatestFrom(
      this.store$.pipe(select(featureSelectors.selectReturnPath))
    ),
    map(([action, selectedPath]: [featureActions.ReturnToLocationsAction, string | null]) => {

      if (selectedPath) {

        this.router.navigate([selectedPath]);
      }
      return new featureActions.NoAction();
    }
    )
  ));

  ticketCountOnLocationRequestActionEffects$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.TicketCountOnLocationRequestAction>(
      featureActions.ActionTypes.TICKET_COUNT_REQUEST
    ),
    withLatestFrom(
        this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
    ),
    switchMap(([action, agency]) => {
      if (!agency) {
        return of(new featureActions.LoadFailureAction({ error: 'No agency found' }));
      }
      return this.ticketService
      .getTicketsByLocationAndRegionsCount(action.payload.locationIds, action.payload.regionIds, agency)
      .pipe(
          map(
              (numOfTickets) => {
                  if (numOfTickets)
                    return new featureActions.TicketCountOnLocationSuccessAction({ticketCount: numOfTickets});
                  else 
                    return new featureActions.TicketCountOnLocationSuccessAction({ticketCount: 0})
              }
          )
      )
    })
  ));

  ticketsOnLocationRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.TicketsOnLocationRequestAction>(
      featureActions.ActionTypes.TICKETS_ON_LOCATION_REQUEST
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState),
      this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
    ),
    switchMap(([action, router, agency]: [featureActions.TicketsOnLocationRequestAction, RouterReducerState<RouterStateUrl>, Agency | undefined | null]) => {
      const locationId = router.state.params['locationId'];
      if (!locationId) {
        return of(new featureActions.NoAction());
      }
      const agencyId = agency?.id || localStorage.getItem(localStorageCompositeKeys.defaultTenantAgency(this.userIdentity.tenantId)) || '';
      return this.ticketService.getTicketsByLocation(locationId, { id: agencyId } as Agency).pipe(
        map(tickets => new GridStoreActions.GridLoadItemsSuccessAction(featureState.TICKET_GRID_ID, { items: tickets })),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      );
    })
  ));

  ticketsClearChangesActionEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.TicketClearChangesAction>(
      featureActions.ActionTypes.TICEKT_CLEAR_CHANGES
    ),
    withLatestFrom(
      this.store$.select(featureSelectors.selectSelectedTicket)
    ),
    map(([action, selectedTicket]: [featureActions.TicketClearChangesAction, Ticket]) => {
      if(selectedTicket.caller){
        return new CallerStoreActions.SearchChangeAction('');
      }else{
        return new CallerStoreActions.NoAction();
      }
    })
  ));

  setSelectedTicketProject$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SetSelectedTicketProjectAction>(
      featureActions.ActionTypes.SET_SELECTED_TICKET_PROJECT
    ),
    withLatestFrom(
      this.store$.pipe(select(TicketTypeStoreSelectors.selectAllTicketTypesItems)),
      this.store$.pipe(select(ProjectStoreSelectors.getProjectsAllStatus)),
      this.store$.pipe(select(ContractStoreSelectors.selectContracts)),
    ),
    map(([action, ticketTypes, projects, contracts]: [featureActions.SetSelectedTicketProjectAction, TicketType[], Project[], Contract[]]) => {
      let filteredTypes: TicketType[] = [];
      let selectedProject: Project | undefined = undefined;
      
      if (action.payload.project) {
        selectedProject = projects.find(x => x.id === action.payload.project?.id);
        if (selectedProject && selectedProject.type) {
          const category = selectedProject.type.category;
          filteredTypes = ticketTypes.filter(x => x.category && x.category.toString() === category?.toString());
          if (category === ProjectCategory.Call) {
            const woTypes = ticketTypes.filter(x => x.category && x.category.toString() === ProjectCategory.WO.toString());
            filteredTypes = [...filteredTypes, ...woTypes];
          }
          const contract = contracts?.find(con => con.id === selectedProject?.contract?.id);
          return new featureActions.SetSelectedTicketProjectActionSuccess({ 
            filteredTypes, 
            project: selectedProject, 
            contract: contract || undefined 
          });
        }
      }

      // If no project is selected, return empty arrays/undefined
      return new featureActions.SetSelectedTicketProjectActionSuccess({ 
        filteredTypes: [], 
        project: projects[0] || undefined,
        contract: undefined 
      });
    })
  ));

  saveMultipleSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveMultipleSuccessAction>(
      featureActions.ActionTypes.SAVE_MULTIPLE_SUCCESS
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    pipe(
      switchMap(([action, router]: [featureActions.SaveMultipleSuccessAction, RouterReducerState<RouterStateUrl>]) => {
          this.page.notification.show('Tickets added');
          let projectId = router.state.params['projectId'] ? `/${router.state.params['projectId']}` : '';
          if(!projectId && this.activatedRoute.snapshot.firstChild){
            projectId = this.activatedRoute.snapshot.firstChild.children[0].params['projectId'] ? 
              `/${this.activatedRoute.snapshot.firstChild.children[0].params['projectId']}` : '';
          }
          this.router.navigate([`/tickets${projectId}`], { state: { shouldDeactivate: true } });
          const tickets = action.payload;
          const actions = [];
          if(tickets && tickets.length) {
            tickets.forEach(ticket => {
              actions.push(new GridStoreActions.GridNewItemAddedAction(featureState.TICKET_GRID_ID, { item: ticket }));
            });
          }
          actions.push(new GridStoreActions.GridLoadItemsAction(featureState.TICKET_GRID_ID));
          actions.push(new LocationStoreActions.SetLocationTypeChecklistConfigAction({locationTypesChecklists: []}));
          return actions;
      })
    )
  ));

  getLatestTicketNoEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.GetLatestTaskNumberRequest>(
      featureActions.ActionTypes.GET_LATEST_TASK_NUMBER_REQUEST
    ),
    switchMap(action => {
       return this.ticketService.getLatestTaskNumber().pipe(
        map(ticketNo => {
          return new featureActions.GetLatestTaskNumberSuccess({  ticketNo });
        }),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      )
    }
    )
  ));

  showEditCallTicketEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ShowEditCallTicketAction>(
      featureActions.ActionTypes.SHOW_EDIT_CALL_TICKET
    ),
    withLatestFrom(
      this.store$.select(featureSelectors.selectTicketsList),
      this.store$.select(featureSelectors.selectTicketFormData),
      this.store$.select(RouterStoreSelectors.getRouterStoreState)
    ),
    map(([action, ticketList, ticketFormData, router]: [featureActions.ShowEditCallTicketAction, Ticket[], Ticket, RouterReducerState<RouterStateUrl>]) => {
      if (ticketFormData == null) {
        let selectedTicket = action.payload.ticket;
        if (selectedTicket?.locationData != null) {
          this.locationInstanceStore$.dispatch(new LocationInstanceStoreActions.SearchTextChangedAction([selectedTicket.locationData.name!]));
          if (selectedTicket.caller) {
            this.callerStore$.dispatch(new CallerStoreActions.SearchChangeAction(''));
          }
        }
      }
      this.router.navigate([`/call-tickets/edit-ticket/${action.payload.ticketId}`]);
      return new featureActions.NoAction();
    })
  ));

  searchTextChangedAction$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SearchTextChagnedAction>(
      featureActions.ActionTypes.SEARCH_TEXT_CHANGED
    ),
    debounce(() => timer(300)),
    switchMap(() => {
      return observableOf(new GridStoreActions.GridLoadItemsAction(featureState.TICKET_GRID_ID));
    })
  ));

  quickFilterChangeEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.QuickFilterChagnedAction>(
      featureActions.ActionTypes.QUICK_FILTER_CHANGED
    ),
    debounce(() => timer(300)),
    switchMap(() => {
      return observableOf(new GridStoreActions.GridLoadItemsAction(featureState.TICKET_GRID_ID));
    })
  ));

  loadSummaryStatisticRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadSummaryStatisticRequest>(
      featureActions.ActionTypes.LOAD_SUMMARY_STATISTIC_REQUEST
    ),
    switchMap((_) => {
      return this.ticketService.getSummaryStatistic()
      .pipe(
        switchMap(summaryStatistic => [
          new featureActions.LoadSummaryStatisticSuccess({ summaryStatistic }),
        ]),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      )
    }
    )
  ));

  loadAverageDailyResponseChartData$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadAverageResponseTimeRequest>(
      featureActions.ActionTypes.LOAD_AVERAGE_RESPONSE_TIME_REQUEST
    ),
    switchMap((action) => {
      return this.ticketService.getAverageResponseChartData(action.payload.responseTimeType)
        .pipe(
          switchMap(averageResponseTimeChartData => {
            if (action.payload.responseTimeType == ResponseTimeType.AverageDailyResponse) {
              return of(new featureActions.LoadAverageDailyResponseSuccess({ averageResponseTimeChartData }))

            } else if(action.payload.responseTimeType == ResponseTimeType.AverageWeeklyResponse) {
              return of(new featureActions.LoadAverageWeeklyResponseSuccess({ averageResponseTimeChartData }))

            } else if(action.payload.responseTimeType == ResponseTimeType.AverageMonthlyResponse) {
              return of(new featureActions.LoadAverageMonthlyResponseSuccess({ averageResponseTimeChartData }))

            } else {
              return of(new featureActions.NoAction())
            }

          }),
          catchError(error => of(new featureActions.LoadFailureAction({ error })))
        )
    }
    )
  ));

  loadTicketsByPrioritiesRequest$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadTicketsByPrioritiesRequest>(
      featureActions.ActionTypes.LOAD_TICKETS_BY_PRIORITIES_REQUEST
    ),
    withLatestFrom(
      this.locationInstanceStore$.select(featureSelectors.selectSelectedFilter),
      this.store$.select(featureSelectors.selectedLocationTickets),
      this.store$.select(RouterStoreSelectors.getRouterStoreState),
      this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
      this.store$.select(featureSelectors.selectTicketDashboardSearchText),
      this.store$.select(featureSelectors.getQuickFilter)
    ),
    switchMap(([action, filters, locationTickets, router, agency, searchText, quickFilter]) => {
      let filter: Filter = {
        ...filters,
        searchText,
        types: [TicketTypeCategory.Call],
        paging: true,
        priorities: action.payload.priorities.map(p => PriorityEnum[p.name as keyof typeof PriorityEnum])
      };

      if (quickFilter.finalized) {
        filter.isFinalizedClicked = true;
      }
      
      return this.ticketService.getTicketList(filter).pipe(
        switchMap(result => [
          new featureActions.LoadTicketsByPrioritiesSuccess({ tickets: result.body.items }),
          new GridStoreActions.GridLoadItemsSuccessAction(TICKETS_LIST_DASHBOARD_GRID_ID, {items: result.body.items})
        ]),
        catchError(error => of(new featureActions.LoadFailureAction({ error })))
      );
    })
  ));

  getAllTickets$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadAllTicketsRequestAction>(
      featureActions.ActionTypes.LOAD_ALLTICKETS_REQUEST
    ),
    withLatestFrom(
      this.store$.select(AgencyStoreSelectors.selectAssignedAgenciesToUser)
    ),
    switchMap(([action, userAssignedAgencies]) => {   
      const canSeeAllAgencies = this.userIdentity.hasPermission([PermissionsEnum.SeeAllAgencies]);
      const isSysAdmin = this.userIdentity.isSysAdmin;    
      let filter = new Filter();
      let type: any = null;
      if (action?.payload?.type?.category) {
         type = action.payload.type.category;
      }
      let ticketFilter: Filter = {
       ...filter,
       agencies: isSysAdmin || canSeeAllAgencies ? [] : (userAssignedAgencies || []).filter((id): id is string => id != null), 
       paging: false,
       approvalStatuses: [0,2,3,4,6],
       types: [type]
     };

      return this.ticketService.getTicketList(ticketFilter).pipe(
          map(result => {
              if (result?.body?.items) { 
                  return new featureActions.LoadAllTicketsSuccessAction({tickets: result.body.items});
                 }
              return new featureActions.NoAction();                            
          }),
          
          catchError(error =>  of(new featureActions.LoadFailureAction({ error })) )
          
      )
    })
  ));

  saveMergeRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveMergeRequestAction>(
      featureActions.ActionTypes.SAVE_MERGE_REQUEST
    ),
    exhaustMap((action) => {
      return this.ticketService
        .mergeTicket(action.payload)
        .pipe(
          map(() => new featureActions.SaveMergeSuccessAction()),
          catchError(error => of(new featureActions.LoadFailureAction({ error })))
        );
    })
  ));

  saveMergeRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.SaveMergeSuccessAction>(
      featureActions.ActionTypes.SAVE_MERGE_REQUEST_SUCCESS
    ),
    withLatestFrom(
      this.store$.select(RouterStoreSelectors.getRouterStoreState),          
      this.store$.pipe(select(TimesheetStoreSelectors.selectSingleTimesheet))
    ),
    switchMap(([_, router, timesheet]: [featureActions.SaveMergeSuccessAction, RouterReducerState<RouterStateUrl>, Timesheet]) => {
      this.page.notification.show('Ticket merged');
      this.router.navigate([`/maintenance/timesheet/${timesheet.id}`], { state: { shouldDeactivate: true }});
      return [new featureActions.NoAction()];
    })
  ));

  confirmDeleteWorkorderEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ConfirmSaveAction>(
      featureActions.ActionTypes.CONFIRM_SAVE
    ),
    exhaustMap((action) =>
      this.page
        .confirm.show('This action will create ' + action.payload.sumNumberLocations + ' new tasks (one per Location), are you sure you want to continue?', 'Are you sure?')
        .pipe(
          map(
            (result) => {
              if (result) {
                return new featureActions.SaveMultipleRequestAction(action.payload);
              }
              return new featureActions.NoAction();
            }
          )
        )
    )
  ));

  errorTicketEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.ErrorDispatchCallAction>(
      featureActions.ActionTypes.ERROR_DISPATCH
    ),
    map((result) => {
      if (result?.payload) {
        this.page?.alert?.error(result.payload);
      }
      return new featureActions.NoAction();
    })
  ));

  gridLoadItems(actionType: string): Observable<Action> {
    return this.actions$.pipe(
      ofType(actionType),
      map(() => new GridStoreActions.GridNoAction())
    );
  }

  gridLoadItemsSuccess(actionType: string): Observable<Action> {
    return this.actions$.pipe(
      ofType(actionType),
      map(() => new GridStoreActions.GridNoAction())
    );
  }

  gridLoadItemsFailed(actionType: string): Observable<Action> {
    return this.actions$.pipe(
      ofType(actionType),
      map(() => new GridStoreActions.GridNoAction())
    );
  }
}




