﻿import { Validator, ValidationErrors, AbstractControl, FormControl } from '@angular/forms'

export class MaxDateValidator  implements Validator {

    constructor(private maxDate: Date) {
    }

    validate(control: AbstractControl): ValidationErrors | null {
        if (control instanceof FormControl) {
            if (control.value == null || !(control.value instanceof Date) || control.value < this.maxDate) {
                return null;
            }

            return {
                maxDate: {
                    currentDate: control.value,
                    maxDate: this.maxDate
                }
            }
        }
        return null;
    }
}
