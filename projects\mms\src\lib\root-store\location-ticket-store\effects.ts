import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, Action } from '@ngrx/store';

import * as featureActions from './actions';
import * as featureState from './state';

import { Observable, of } from 'rxjs';
import { switchMap, map, catchError, withLatestFrom } from 'rxjs/operators';

import { IGridStoreEffects } from '../shared-store/material-grid-store/effects';
import { GridLoadItemsSuccessAction } from '../shared-store/material-grid-store/actions';
import { GridStoreActions } from '../shared-store/material-grid-store';
import { Ticket } from '../../configuration/tickets/tickets.model';
import { TicketService } from '../../configuration/tickets/tickets.service';
import { LocationInstanceStoreState, LocationInstanceStoreSelectors } from '../location-instance-store';
import { AgencyStoreSelectors } from '../agency-store';
import { Agency } from '../../configuration/agencies/agency.model';
import { TicketStatus } from '../../configuration/tickets/tickets-enum/ticket-status.enum';
import { RouterStoreSelectors } from '../router-store';
import { MapStoreSelectors } from '../map-store';
import { TicketTypeCategory } from '../../configuration/ticket-types/enums/ticket-category.enum';
import { LocationTicketStoreSelectors } from '../location-ticket-store';

const emptyAgency: Agency = {
  id: '',
  name: '',
  tenantId: '',
  defaultDispatchProject: { id: '', name: '' },
  defaultTechProject: { id: '', name: '' },
  isMaintance: false,
  timezone: '',
  inventoryList: { id: '', name: '' },
  inventoryCostList: { id: '', name: '' },
  notifications: {
    onAssign: false,
    onEnroute: false,
    onOnsite: false,
    onStopped: false,
    onFinalized: false,
    additionalEmail: ''
  }
};

@Injectable()
export class LocationTicketStoreEffects implements IGridStoreEffects {
  constructor(
    private actions$: Actions,
    private ticketService: TicketService,
    private store$: Store<featureState.State>,
    private locationInstanceStore$: Store<LocationInstanceStoreState.MainState>
  ) { }

  loadRequestEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadRequestAction>(
      featureActions.ActionTypes.LOAD_REQUEST
    ),
    withLatestFrom(
      this.locationInstanceStore$.select(LocationInstanceStoreSelectors.selectedLocationInstance),
      this.locationInstanceStore$.select(LocationInstanceStoreSelectors.selectVehicleUser),
      this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
      this.store$.select(RouterStoreSelectors.getRouterStoreState),
      this.store$.select(MapStoreSelectors.selectedLocationId),
      this.store$.select(LocationInstanceStoreSelectors.selectedDispatchCallLocation),
      this.store$.select(LocationTicketStoreSelectors.selectSelectedAgency),
    ),
    switchMap(([action, locationInstance, vehicleUser, agency, router, mapSelectedLocation, selectedDispatchCallLocation, selectedAgencyLocationTicket]) => {
      let locationInstanceId = action.payload ? action.payload.locationInstanceId : router?.state?.params?.['locationId'];
      if (router?.state?.url?.includes('/map') || router?.state?.url?.includes('/dashboard') && selectedDispatchCallLocation == null) {
        selectedAgencyLocationTicket = agency || emptyAgency;
        locationInstanceId = mapSelectedLocation
      }
      else if (router?.state?.url?.includes('/dashboard') && (selectedDispatchCallLocation != null || selectedDispatchCallLocation != undefined)) {
        locationInstanceId = selectedDispatchCallLocation;
      }
      else if(router?.state?.url?.includes('/call-tickets')) {
        locationInstanceId = selectedDispatchCallLocation;
      }
      
      return this.ticketService.getTicketsByLocation(
        locationInstanceId !==null ? locationInstanceId : router?.state?.params?.['locationId'], 
        selectedAgencyLocationTicket || emptyAgency
      ).pipe(
        map(res => {
          if(router?.state?.url?.includes('/call-tickets'))
            res = res.filter(x => x.type?.category != TicketTypeCategory.PM)
          if(locationInstance && locationInstance.locationType?.name === 'Vehicle') {
            res = res.filter(x => x.assignedUsers != null).filter(x => (x.assignedUsers?.filter(u => u.id === vehicleUser)));
            return new featureActions.LoadSuccessAction({tickets: res});
          }
          else {
            return new featureActions.LoadSuccessAction({tickets: res});
          }
        })
      );
    })
  ));

  loadSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadSuccessAction>(
      featureActions.ActionTypes.LOAD_SUCCESS
    ),
    map((action) => new GridLoadItemsSuccessAction(featureState.LOCATION_TICKET_GRID_ID, { items: action.payload.tickets.filter(ticket => ticket.status != TicketStatus.Finalized)}))
  ));

  loadTicketSuccessEffect$ = createEffect(() => this.actions$.pipe(
    ofType<featureActions.LoadSuccessAction>(
      featureActions.ActionTypes.LOAD_SUCCESS
    ),
    map((action) => new GridLoadItemsSuccessAction(featureState.LOCATION_HISTORY_TICKET_GRID_ID, { items: action.payload.tickets.filter(ticket => ticket.status == TicketStatus.Finalized)}))
  ));

  gridLoadItems$ = createEffect(() => this.gridLoadItems(
    GridStoreActions.ActionTypes.GRID_LOAD_ITEMS(featureState.LOCATION_TICKET_GRID_ID)
  ));

  gridLoadItemsSuccess$ = createEffect(() => this.gridLoadItemsSuccess(
    GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_SUCCESS(featureState.LOCATION_TICKET_GRID_ID)
  ));

  gridLoadItemsFailed$ = createEffect(() => this.gridLoadItemsFailed(
    GridStoreActions.ActionTypes.GRID_LOAD_ITEMS_FAILED(featureState.LOCATION_TICKET_GRID_ID)
  ));

  gridLoadItems(actionType: string): Observable<Action> {
    return this.actions$.pipe(
      ofType<GridStoreActions.GridLoadItemsAction<Ticket>>(actionType),
      withLatestFrom(
        this.locationInstanceStore$.select(LocationInstanceStoreSelectors.selectedLocationInstance),
        this.store$.select(AgencyStoreSelectors.selectDefaultAgency)
      ),
      switchMap(([_, location, agency]) => 
        this.ticketService.getTicketsByLocation(location.id, agency || emptyAgency)
          .pipe(switchMap(result => {
            return [
              new GridStoreActions.GridLoadItemsSuccessAction(featureState.LOCATION_TICKET_GRID_ID, { items: result.filter(ticket => ticket.status != TicketStatus.Finalized) })
            ];
          }))
      ),
      catchError((error) => of(new GridStoreActions.GridLoadItemsFailedAction(featureState.LOCATION_TICKET_GRID_ID, error)))
    );
  }

  gridLoadItemsSuccess(actionType: string): Observable<GridStoreActions.Actions> {
    return this.actions$.pipe(ofType(actionType)
      , map((action) => new GridStoreActions.GridNoAction));
  }

  gridLoadItemsFailed(actionType: string): Observable<GridStoreActions.Actions> {
    return this.actions$.pipe(ofType(actionType)
      , map((action) => new GridStoreActions.GridNoAction));
  }

}




