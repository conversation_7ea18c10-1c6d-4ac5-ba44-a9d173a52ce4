import { TicketTypeCategory } from './../../configuration/ticket-types/enums/ticket-category.enum';
import { BillingService } from './../../payroll-and-billing/billing/billing.service';
import { UserIdentity } from '@econolite/identity-client';
import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { TimesheetsService } from "../../payroll-and-billing/timesheets/timesheets.service";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { Action, select, Store } from "@ngrx/store";
import { MatDialog } from "@angular/material/dialog";
import * as featureActions from './actions';
import * as featureState from './state'
import * as featureSelectors from './selectors'
import { EmployeesServices } from "../../configuration/employees/services/employees.service";
import { TimesheetFilter } from "../../payroll-and-billing/timesheets/models/filter.model";
import { withLatestFrom, switchMap, concatMap, catchError, map, mergeMap, exhaustMap, tap } from "rxjs/operators";
import * as AgencyStoreSelectors from '../agency-store/selectors';
import { GridStoreActions } from '../shared-store/material-grid-store';
import { Observable, of, forkJoin } from "rxjs";
import { Timesheet } from "../../payroll-and-billing/timesheets/models/timesheet.model";
import { RouterStoreSelectors } from '../router-store';
import { AreaTypeEnum } from '../../configuration/location-instances/shared/area-type.enum';
import { ApprovalStatusChange, ApproveTasksRequest } from '../../payroll-and-billing/timesheets/models/approve-tasks-request.model';
import { TicketService } from '../../configuration/tickets/tickets.service';
import { Ticket } from '../../configuration/tickets/tickets.model';
import { TicketTypesService } from '../../configuration/ticket-types/services/ticket-types.service';
import { ProjectService } from '../../configuration/projects/services/projects.service';
import { ReasonCodesService } from '../../configuration/reason-codes/services/reason-codes.service';
import { Page } from '../../core/page.service';
import { ApprovalStatus, TicketStatus } from '../../configuration/tickets/tickets-enum/ticket-status.enum';
import { MasterInventoryService } from '../../configuration/master-inventory/master-inventory.service';
import { TaskInventory } from '../../payroll-and-billing/timesheets/models/task-inventory.model';
import { BillingItem } from '../../configuration/tickets/billing-item.model';
import { LocationInstancesService } from '../../configuration/location-instances/shared/location-instances.service';
import { LocationInstancesListData } from '../../configuration/location-instances/shared/location-data.model';
import { ActionType } from '../../configuration/tickets/tickets-enum/action-type.enum';
import { Bill, Filter } from '../../payroll-and-billing/billing/models/bill.model';
import { BaseEntity } from '../../payroll-and-billing/timesheets/models/work-period-grid-model.model';
import { AuthStatus } from '../../payroll-and-billing/billing/models/auth-status.model';
import { ApprovalStatusHistory } from '../../configuration/tickets/approval-status-history.model';
import { BillingStoreActions } from '../billing-store';
import { PermissionsEnum } from '../../core/auth/permissions.enum';
import { UserService } from '../../configuration/shared/user-and-groups/users.service';

@Injectable()
export class TimesheetsStoreEffects {
    constructor(
        private router: Router,
        private timesheetsService: TimesheetsService,
        private actions$: Actions,
        private store$: Store<featureState.State>,
        private employeeService: EmployeesServices,
        public dialog: MatDialog,
        public userIdentity: UserIdentity,
        public ticketService: TicketService,
        public ticketTypesService: TicketTypesService,
        public projectsService: ProjectService,
        public reasonCodesService: ReasonCodesService,
        private page: Page,
        public masterInventoryService: MasterInventoryService, 
        public locationInstanceService: LocationInstancesService,
        public billingService: BillingService,
        public userService: UserService
        ) { }

    // tslint:disable-next-line:member-ordering
    loadUsersEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadEmployeesRequestAction>(featureActions.ActionTypes.LOAD_EMPLOYEES_REQUEST),
        switchMap(() => {
            return this.employeeService.getEmployees().pipe(
                concatMap(employees => [
                    new featureActions.LoadEmployeesSuccessAction({ employees }), 
                    new featureActions.LoadRequestAction({ filter: undefined })
                ]),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        })
    ));

    gridLoadItems$ = createEffect(() => this.gridLoadItems(GridStoreActions.ActionTypes.GRID_LOAD_ITEMS(featureState.TIMESHEETS_GRID_ID)));

    gridLoadItems(actionType: string): Observable<any> {
        return this.actions$.pipe(
            ofType<GridStoreActions.GridLoadItemsAction<Timesheet>>(actionType),
            withLatestFrom(
                this.store$.select(featureSelectors.selectEmployees),
                this.store$.select(featureSelectors.selectSelectedFilter),
                this.store$.select(RouterStoreSelectors.getRouterStoreState),

            ),
            switchMap(([action, employees, selectedFilter, routerState]) => {
                let filter: TimesheetFilter = {
                    fromDate: this.getYesterday(),
                    toDate: this.getYesterday(),
                    users: [],
                    hideApproved: false,
                    showAll: false,
                    agencies: [],
                    nextPageToken: '',
                    date: '',
                    userId: ''
                };
                let lastFilter = JSON.parse(localStorage.getItem("timesheetFilter") || '{}') as TimesheetFilter;
                if (lastFilter) {
                    filter = lastFilter;
                } else if (selectedFilter && routerState.state.url.includes("/timesheet/")) {
                    filter = selectedFilter;
                }
                if (filter && action.payload && action.payload.nextPageToken){
                    filter.nextPageToken = action.payload.nextPageToken;
                }

                return this.timesheetsService.getTimesheets(filter).pipe(
                    concatMap(
                        (result) => {
                            if (result.items && result.items.length && employees && employees.length) {
                                result.items.map(timesheet => {
                                    const employee = employees.find(employee => employee.id === timesheet.userId);
                                    if (employee) {
                                        timesheet.username = employee.name;
                                    }
                                    return timesheet;
                                });
                                result.items = result.items.filter(timesheet => timesheet.username !== undefined);
                            }
                            return [new GridStoreActions.GridLoadItemsSuccessAction(featureState.TIMESHEETS_GRID_ID, { items: result.items ?? [], nextPageToken: result.nextPageToken, clearGrid: (action.payload === undefined) })];
                        }
                    ),
                    catchError((error: Error) => of(new GridStoreActions.GridLoadItemsFailedAction(featureState.TIMESHEETS_GRID_ID, { error: error.message })))
                )
            }
            )
        );
    }

    // tslint:disable-next-line:member-ordering
    loadTimesheets$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadRequestAction>(featureActions.ActionTypes.LOAD_REQUEST),
        withLatestFrom(
            this.store$.select(featureSelectors.getNextPageToken),
            this.store$.select(featureSelectors.selectEmployees),
            this.store$.select(AgencyStoreSelectors.selectDefaultAgency),
            this.store$.select(featureSelectors.selectSelectedFilter),
            this.store$.select(RouterStoreSelectors.getRouterStoreState),
        ),
        switchMap(([action, nextPageToken, employees, agency, selectedFilter, routerState]) => {
            let filter: TimesheetFilter = {
                fromDate: this.getYesterday(),
                toDate: this.getYesterday(),
                users: [],
                hideApproved: false,
                showAll: false,
                agencies: [],
                nextPageToken: '',
                date: '',
                userId: ''
            };

            const lastFilterStr = localStorage.getItem("timesheetFilter");
            const lastFilter = lastFilterStr ? JSON.parse(lastFilterStr) as TimesheetFilter : undefined;
            
            if (action.payload?.filter) {
                filter = action.payload.filter;
                if (!action.payload.filter.showAll && agency) {
                    filter.agencies = agency.id ? [agency.id] : [];
                } else {
                    filter.showAll = true;
                }
            } else if (lastFilter) {
                filter = lastFilter;
            } else if (selectedFilter && routerState.state.url.includes("/timesheet/")) {
                filter = selectedFilter;
            }

            return this.timesheetsService.getTimesheets(filter).pipe(
                concatMap(
                    (result) => {
                        if (result.items && result.items.length && employees && employees.length) {
                            result.items.map(timesheet => {
                                const employee = employees.find(employee => employee.id === timesheet.userId);
                                if (employee) {
                                    timesheet.username = employee.name;
                                }
                                return timesheet;
                            });
                            result.items = result.items.filter(timesheet => timesheet.username !== undefined);
                        }
                        return [
                            new featureActions.LoadSuccessAction({ items: result.items ?? [] }),
                            new GridStoreActions.GridLoadItemsSuccessAction(
                                featureState.TIMESHEETS_GRID_ID,
                                { items: result.items ?? [], nextPageToken: result.nextPageToken, clearGrid: nextPageToken !== null }
                            ),
                            new featureActions.SetSelectedFilterAction({filter})
                        ] as Action[];
                    }
                ),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        })
    ));

    // tslint:disable-next-line:member-ordering
    showFilter$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ShowFilter>(featureActions.ActionTypes.SHOW_FILTER),
        tap(() => this.router.navigate(['/maintenance/timesheets/filter']))
    ), { dispatch: false });

    // tslint:disable-next-line:member-ordering
    setSelectedFilter$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SetSelectedFilterAction>(featureActions.ActionTypes.SET_SELECTED_FILTER),
        map((action) => {
            localStorage.setItem('timesheetFilter', JSON.stringify(action.payload.filter));
        })
    ), { dispatch: false });

    // tslint:disable-next-line:member-ordering
    closeFilter$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.CloseFilter>(featureActions.ActionTypes.CLOSE_FILTER),
        tap(() => this.router.navigate(['/maintenance/timesheets']))
    ), { dispatch: false });

    loadEmployeeWeeklyTimesheetsRequest$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadWeeklyTimesheetsRequestAction>(
            featureActions.ActionTypes.LOAD_WEEKLY_TIMESHEETS_REQUEST
        ),
        withLatestFrom(
            this.store$.select(featureSelectors.selectPayPeriodEndDate),
        ),
        switchMap(([_, date]) =>
            this.timesheetsService
                .getWeekTimesheeets(date!)
                .pipe(
                    map(
                        items => new featureActions.LoadWeeklyTimesheetsSuccessAction({ items })
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    loadEmployeeWeeklyTimesheetsSuccess$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadWeeklyTimesheetsSuccessAction>(
            featureActions.ActionTypes.LOAD_WEEKLY_TIMESHEETS_SUCCESS
        ),
        map((action) => {
            return new GridStoreActions.GridLoadItemsSuccessAction(featureState.EMPLOYEE_WEEEKLY_TIMESHEETS_GRID_ID, { items: action.payload.items })
        })
    ));

    setHideFullyTimesheetsAction$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SetHideFullyTimesheetsAction>(
            featureActions.ActionTypes.SET_HIDE_FULLY_TIMESHEETS
        ),
        withLatestFrom(
            this.store$.select(featureSelectors.selectEmployeeWeeklyTimesheets),
        ),
        map(([action, timesheets]) => {
            if (action.payload.hideFullyTimesheets) {
                var items = timesheets.filter(item => !item.isFullyTimesheet);
                return new GridStoreActions.GridLoadItemsSuccessAction(featureState.EMPLOYEE_WEEEKLY_TIMESHEETS_GRID_ID, { items })
            }
            return new GridStoreActions.GridLoadItemsSuccessAction(featureState.EMPLOYEE_WEEEKLY_TIMESHEETS_GRID_ID, { items: timesheets })
        })
    ));

    processAllTimesheets$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ProcessAllTimesheetsAction>(
            featureActions.ActionTypes.PROCESS_ALL_TIMESHETS
        ),
        withLatestFrom(
            this.store$.select(featureSelectors.selectEmployeeWeeklyTimesheets),
        ),
        mergeMap(([_, weeklyTimesheets]) => {
            var timesheets = this.processAllWeeklyTimesheets(weeklyTimesheets);
            return this.timesheetsService.updateMultipleTimesheets(timesheets)
                .pipe(
                    concatMap(() => [
                        new featureActions.LoadWeeklyTimesheetsRequestAction(),
                        new featureActions.GeneratePayrollOutputAction()
                    ]),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        })
    ));

    updateRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateRequestAction>(
            featureActions.ActionTypes.UPDATE_TIMESHET_REQUEST
        ),
        exhaustMap((action) =>
            this.timesheetsService
                .updateTimesheet(action.payload.item)
                .pipe(
                    switchMap(
                        () => 
                        [new featureActions.UpdateSuccessAction({ item: action.payload.item }),
                        new featureActions.GetTimesheetSuccess({ timesheet: action.payload.item })]
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    updateRequestSuccessEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateRequestAction>(
            featureActions.ActionTypes.UPDATE_TIMESHET_SUCCESS
        ),
        withLatestFrom(
            this.store$.select(RouterStoreSelectors.getRouterStoreState)
        ),
        map(([action, routerState]) => {
            if (!routerState.state.url.includes('/timesheet/')) {
                return new featureActions.LoadWeeklyTimesheetsRequestAction();
            } else if (action.payload.item && routerState.state.url.includes('/timesheet/')) {
                return new featureActions.LoadWorkPeriodsRequest({timesheet: action.payload.item});
            }
            return new featureActions.NoAction();
        })
    ));

    stopActioveWPTimesheetRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.StopActiveWPTimesheetRequest>(
            featureActions.ActionTypes.STOP_ACTIVE_WP_TIMESHET_REQUEST
        ),
        exhaustMap((action) =>
            this.timesheetsService
                .stopActiveWPTimesheet(action.payload.item)
                .pipe(
                    switchMap(
                        (timesheet) => [new featureActions.StopActiveWPTimesheetSuccess({ item: timesheet })]
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    viewTimesheet$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ViewTimesheetAction>(featureActions.ActionTypes.VIEW_TIMESHEET),
        withLatestFrom(
            this.store$.select(featureSelectors.selectEmployees),
            this.store$.select(featureSelectors.selectAllTimesheetItems)
        ),
        mergeMap(([action, employees, timesheets]) => {
            let timesheet = null;
            try {
                if (timesheets && timesheets.length) {
                    timesheet = timesheets.find(timesheet => timesheet.id === action.payload.id);
                    if(timesheet){
                        return [
                            new featureActions.GetTimesheetSuccess({ timesheet }),
                            new featureActions.ShowEditTimesheetAction({ timesheetId: action.payload.id, timesheet: timesheet })
                        ];
                    }
                }

                return this.timesheetsService
                .getTimesheetById(action.payload.id)
                .pipe(
                    switchMap(
                        (timesheet) => {
                            const employee = employees.find(employee => employee.id === timesheet.userId);
                            if (employee) {
                                timesheet.username = employee.name;
                            }
                            return [
                                new featureActions.GetTimesheetSuccess({ timesheet }),
                                new featureActions.ShowEditTimesheetAction({ timesheetId: action.payload.id, timesheet: timesheet })
                            ];
                        }
                    ),
                    catchError(error => of(new featureActions.LoadFailureAction({ error: 'Failed to load timesheet' })))
                )
            }
            catch(error) {
                return of(new featureActions.LoadFailureAction({ error: 'Failed to load timesheet' }))
            }
        })
    ));

    loadEmployeesAndTimesheet$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadEmployeesAndTimesheet>(featureActions.ActionTypes.LOAD_EMPLOYEES_AND_TIMESHEET),
        withLatestFrom(
            this.store$.select(featureSelectors.selectEmployees)
        ),
        exhaustMap(([action, employees]) => {
            if(employees == null || employees.length == 0){
                return this.employeeService.getEmployees().pipe(
                    switchMap((employees) => [
                        new featureActions.LoadEmployeesSuccessAction({ employees }),
                        new featureActions.LoadSingleTimesheet({ timesheetId: action.payload.timesheetId })
                    ]),
                    catchError(error => of(new featureActions.LoadFailureAction({ error })))
                );
            }
            return of(new featureActions.LoadSingleTimesheet({timesheetId: action.payload.timesheetId}));
        })
    ));

    loadSingleTimesheet$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadSingleTimesheet>(featureActions.ActionTypes.LOAD_SINGLE_TIMESHEET),
        withLatestFrom(
            this.store$.select(featureSelectors.selectEmployees)
        ),
        exhaustMap(([action, employees]) => {
            return this.timesheetsService.getTimesheetById(action.payload.timesheetId).pipe(
                switchMap((timesheet) => {
                    const employee = employees.find(employee => employee.id === timesheet.userId);
                    if (employee) {
                        timesheet.username = employee.name;
                    }
                    return of(new featureActions.GetTimesheetSuccess({ timesheet }));
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            );
        })
    ));

    showEditTimesheetEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ShowEditTimesheetAction>(
            featureActions.ActionTypes.SHOW_EDIT_TIMESHEET
        ),
        tap((action) => {
            this.router.navigate([`/maintenance/timesheet/${action.payload.timesheetId}`])
        })
    ));

    // tslint:disable-next-line:member-ordering
    approveTasks$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ApproveTaskRequest>(
            featureActions.ActionTypes.APPROVE_TASKS_REQUEST
        ),
        withLatestFrom(
            this.store$.select(featureSelectors.selectSingleTimesheet),
        ),
        exhaustMap(([action, selectedTimesheet]) => {
            const userName = this.userIdentity.fullName;
            const userId = this.userIdentity.id;
            const model = { ticketIds: action.payload.ids, userName, userId } as ApproveTasksRequest;
            return this.timesheetsService
                .approveTasks(model)
                .pipe(
                    concatMap(result => [
                        new featureActions.LoadTimesheetRequestAction({ item: selectedTimesheet }),
                        new featureActions.ApproveTaskSuccess(result),
                        new featureActions.LoadWorkPeriodsRequest()
                    ]),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        })
    ));

    // tslint:disable-next-line:member-ordering
    approveTasksSuccess$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ApproveTaskSuccess>(
            featureActions.ActionTypes.APPROVE_TASKS_SUCCESS
        ),
        withLatestFrom(
            this.store$.select(AgencyStoreSelectors.selectAllAgencyItems),
            this.store$.select(featureSelectors.selectSingleTimesheet),
        ),
        exhaustMap(([action, agencies, timesheet]) => {      
            let bills = new Array<Bill>();
            if (action && action.payload) {
                action.payload.forEach(ticket => {
                    if(ticket.status == TicketStatus.Finalized) {
                        let bill = new Bill();
                        var agency = ticket.project && ticket.project.agencyId ? agencies.find(x => x.id == ticket.project?.agencyId) : null;
                        let billAgency = agency ? new BaseEntity(agency.id!, agency.name ?? '') : new BaseEntity('', '');
                        bill.agency = billAgency;
                        bill.modifiedDate = undefined;
                        bill.taskDate = ticket.creationDate;  
                        bill.authorized = ticket.type && ticket.type.category == TicketTypeCategory.PM ? true : false;
                        bill.project = new BaseEntity(ticket.project?.id,ticket.project?.name);   
                        bill.ticketNo = ticket.ticketNo;
                        bill.ticketId = ticket.id;
                        bill.billingNotes = '';
                        bill.id = '';
                        bill.tenantId = '';
                        bill.taskType = ticket.type?.category;        
                        bill.authStatus =  ticket.type && ticket.type.category == TicketTypeCategory.PM ? AuthStatus.Authorized : AuthStatus.Undefined                    
                        bills.push(bill);
                    }               
                }); 

                this.router.navigate([`/maintenance/timesheet/${timesheet.id}`], { state: { shouldDeactivate: true }});
            }             
            return this.billingService
                .createMultipleBills(bills)
                .pipe(
                    concatMap(() => [new featureActions.CreateBillsSuccess()]
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                );                
        }
        )
    ));

    // tslint:disable-next-line:member-ordering
    loadWorkPeriods$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadWorkPeriodsRequest>(
            featureActions.ActionTypes.LOAD_WORK_PERIODS_REQUEST
        ),
        withLatestFrom(
            this.store$.select(featureSelectors.selectSingleTimesheet)
        ),
        exhaustMap(([action, timesheet]) => {
            return this.timesheetsService
                .getWorkPeriodGridModel(action.payload ? action.payload.timesheet : timesheet, action.payload && action.payload.showDeleted ? action.payload.showDeleted : undefined)
                .pipe(
                    concatMap(
                        (items) => {
                            const sumTotalHours = items.filter(item => item.isDeleted != true)
                                .reduce((sum, current) => sum + (current.totalHours ?? 0), 0);
                            var footerItem = {
                                id: '',
                                projectName: '',
                                projectType: '',
                                locationName: '',
                                hasJurisdiction: false,
                                timezone: '',
                                actionType: ActionType.Undefined,
                                lunch: 0,
                                totalHours: sumTotalHours,
                                straightTime: 0,
                                doubleTime: 0,
                                overTime: 0,
                                jobCodes: [],
                                billCodes: [],
                                jobCode: new BaseEntity('', ''),
                                billCode: new BaseEntity('', ''),
                                ticketId: '',
                                taskNo: 0,
                                isTaskFinalized: false,
                                taskApproval: 0,
                                techComments: [],
                                comment: '',
                                invQty: 0,
                                isShared: false,
                                isPM: false,
                                agencyId: '',
                                isDeleted: false,
                            }
                            items.push(footerItem);
                            return [new featureActions.LoadWorkPeriodsSuccess(),
                            new GridStoreActions.GridLoadItemsSuccessAction(featureState.WORK_PERIODS_GRID_ID, { items })]
                    }
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
            }
        )
    ));

    setSelectedWorkPeriod$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SetSelectedWorkPeriod>(
            featureActions.ActionTypes.SET_SELECTED_WORK_PERIOD
        ),
        withLatestFrom(
            this.store$.select(featureSelectors.selectSingleTimesheet),
        ),
        map(([action, timesheet]) => {
            let workPeriod = timesheet && timesheet.workRecords.find((record: { id: string }) => record.id === action.payload.workPeriodId);
            workPeriod.location.areaType = AreaTypeEnum.Location;
            workPeriod.timesheetId = timesheet.id;
            return new featureActions.SetSelectedWorkPeriodSuccess({ workPeriod });
        })
    ));

    updateWorkPeriodRequest$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ManageWorkPeriodRequest>(
            featureActions.ActionTypes.MANAGE_WORK_PERIOD_REQUEST
        ),
        withLatestFrom(
            this.store$.select(featureSelectors.selectSingleTimesheet),
            this.store$.select(featureSelectors.selectWorkEditorTicket),
            this.store$.select(featureSelectors.selectEmployees),
        ),
        switchMap(([action, timesheet, ticket, employees]) =>
            this.timesheetsService
                .manageWorkPeriod(action.payload.request)
                .pipe(
                    exhaustMap(
                        (item) => {
                            if (item && employees && employees.length) {
                                const employee = employees.find(employee => employee.id === item.userId);
                                if (employee) {
                                    item.username = employee.name;
                                }
                            }

                            if (!action.payload.noRedirect) {
                                if (!item) {
                                    this.router.navigate(['/maintenance/timesheets']);
                                } else {
                                    this.router.navigate(['/maintenance/timesheet/'+ timesheet.id]);
                                }
                            }   

                            return [
                                new featureActions.UpdateSuccessAction({ item }),
                                new featureActions.ShowEditTimesheetAction({ timesheetId: item.id, timesheet: item })
                            ];
                        }
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    loadTaskHistoryRequest$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadTaskHistoryRequest>(
            featureActions.ActionTypes.LOAD_TASK_HISTORY_REQUEST
        ),
        withLatestFrom(
            this.store$.select(featureSelectors.getWorkPeriod),
        ),
        exhaustMap(([_, workPeriod]) =>
            this.timesheetsService
                .getTaskHistory(workPeriod && workPeriod.ticketNo.toString())
                .pipe(
                    map(
                        (taskHistory) => new featureActions.LoadTaskHistorySuccess({ taskHistory })
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    // tslint:disable-next-line:member-ordering
    loadTaskDetailsRequest$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadTaskDetailsRequest>(
            featureActions.ActionTypes.LOAD_TASK_DETAILS_REQUEST
        ),
        withLatestFrom(
            this.store$.select(featureSelectors.getWorkPeriod),
        ),
        switchMap(([_, workPeriod]) =>
            this.ticketService
                .getTicket(workPeriod && workPeriod.ticketId)
                .pipe(
                    concatMap(
                        (task: Ticket) => {
                            var actions = [];
                            actions.push(
                                new featureActions.LoadTaskDetailsSuccess({ task })
                            );

                            if (task.approvalStatus == ApprovalStatus.Approved) {
                                actions.push(new featureActions.LoadTaskDetailsBillRequest({ taskNo: task.ticketNo }))
                            }

                            return actions;
                        }
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    // tslint:disable-next-line:member-ordering
    loadTaskDetailsDropdownData$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadTaskDetailsDropdownDataRequest>(
            featureActions.ActionTypes.LOAD_TASK_DETAILS_DROPDOWN_DATA_REQUEST
        ),
        switchMap(() => {
            const taskTypesObservable = this.ticketTypesService.getTicketTypes();
            const projectsObservable = this.projectsService.getAllProjects();
            const reasonCodesObservable = this.reasonCodesService.getReasonCodes();

            return forkJoin(taskTypesObservable, projectsObservable, reasonCodesObservable).pipe(
                concatMap(data => {
                    return [
                        new featureActions.LoadTaskDetailsDropdownDataSuccess({ taskTypes: data[0], projects: data[1], reasonCodes: data[2] }),
                        new featureActions.LoadTaskDetailsRequest()
                    ];
                }),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        })
    ));

    // tslint:disable-next-line:member-ordering
    stopTask$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.StopTaskRequest>(
            featureActions.ActionTypes.STOP_TASK_REQUEST
        ),
        exhaustMap((action) => {
        
            if (action.payload.task.actions) {
                // find user active action
                let userActiveAction = action.payload.task.actions.find(x => x.actionType && [ActionType.Enroute, ActionType.Onsite].includes(x.actionType)
                    && (!x.endDate) && x.userId == action.payload.userId
                );

                //close active action if exist
                if (userActiveAction) {
                    userActiveAction.endDate = Date.prototype.fromTimeZone(new Date(), action.payload.task.timezone ?? '');

                    // add stop/finalize action for active action
                    var stopAction = {
                        userId: userActiveAction.userId,
                        userName: userActiveAction.userName,
                        actionType: ActionType.Undefined,
                        startDate: Date.prototype.fromTimeZone(new Date(), action.payload.task.timezone ?? ''),
                        endDate: Date.prototype.fromTimeZone(new Date(), action.payload.task.timezone ?? ''),
                        eta: Date.prototype.fromTimeZone(new Date(), action.payload.task.timezone ?? ''),
                        timezone: action.payload.task.timezone
                    };

                    action.payload.task.actions.push(stopAction);
                }

            }

            action.payload.task.status = action.payload.actionType === ActionType.StopWork ? TicketStatus.ClosedPending : TicketStatus.Finalized;

            if (action.payload.actionType === ActionType.Finalize) {
                action.payload.task.approvalStatus = ApprovalStatus.Submitted;
            }
            
            return of(new featureActions.UpdateTask({ task: action.payload.task, taskStopped: true }));
        })
    ));

    // tslint:disable-next-line:member-ordering
    updateTicket$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.UpdateTask>(
            featureActions.ActionTypes.UPDATE_TASK
        ),
        withLatestFrom(
            this.store$.select(featureSelectors.selectSingleTimesheet)
        ),
        switchMap(([action, timesheet]) =>
            this.ticketService.updateTicket(action.payload.task)
                .pipe(
                    concatMap(
                        (task: Ticket) => {
                            var actions = [];
                            actions.push(new featureActions.LoadTaskDetailsSuccess({ task }));

                            this.page.notification.show("Task updated");
                            return actions;
                        }
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    
    // tslint:disable-next-line:member-ordering
    loadTaskInventory$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadTaskInventoryRequest>(
            featureActions.ActionTypes.LOAD_TASK_INVENTORY_REQUEST
        ),
        withLatestFrom(
            this.store$.select(featureSelectors.getWorkPeriod),
        ),
        exhaustMap(([action, workPeriod]) =>
            this.masterInventoryService.getTaskInventoryByWP(
                action.isInventoryList && workPeriod ? workPeriod.id : '',
                action.location || '',
                action.isInventoryList || false
            )
                .pipe(
                    concatMap(
                        (items: TaskInventory[]) => {                            
                            var grid = action.isInventoryList ? featureState.TASK_INVENTORY_GRID_ID : featureState.LOCATION_INVENTORY_GRID_ID;
                            return [new featureActions.LoadTaskInventorySuccess(action.location),
                            new GridStoreActions.GridLoadItemsSuccessAction(grid, { items })
                            ]
                        }
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    // tslint:disable-next-line:member-ordering
    loadTaskBillingItems$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadTaskBillingItemsRequest>(
            featureActions.ActionTypes.LOAD_TASK_BILLING_ITEMS_REQUEST),
        withLatestFrom(
            this.store$.select(featureSelectors.getWorkPeriod),
        ),
        exhaustMap(([action, workPeriod]) => 
            this.ticketService.getTicketBillingItems(workPeriod && workPeriod.ticketId) //"af441378-01ee-cf12-6d9f-41dc9b427f16"
                .pipe(
                    concatMap((items: BillingItem[]) => [new featureActions.LoadTaskBillingItemsSuccess(), new GridStoreActions.GridLoadItemsSuccessAction(featureState.TASK_BILLING_ITEMS_GRID_ID, { items })]
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    // tslint:disable-next-line:member-ordering
    loadLocations$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadLocationsRequest>(
            featureActions.ActionTypes.LOAD_LOCATIONS_REQUEST),
        exhaustMap((action) => 
            this.locationInstanceService.getLocationInstancesGridItems(-1, [''], undefined)
                .pipe(
                    map((result: LocationInstancesListData) => new featureActions.LoadLocationsSuccess({ locations: result.items ?? [] })),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    // tslint:disable-next-line:member-ordering
    saveBillingItem$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SaveBillingItem>(
            featureActions.ActionTypes.SAVE_BILLING_ITEM),
        withLatestFrom(
            this.store$.select(featureSelectors.getWorkPeriod),
        ),
        exhaustMap(([action, workPeriod]) =>
            this.ticketService.saveTicketBillingItem(workPeriod && workPeriod.ticketId, action.payload.item) //"af441378-01ee-cf12-6d9f-41dc9b427f16"
                .pipe(
                    concatMap((result: BillingItem) => {
                        var currentAction = action.payload.item.id ? 'edited' : 'added';
                        this.page.notification.show(`Billing item ${currentAction   }`);
                        return [
                            action.payload.item.id ?
                                new GridStoreActions.GridItemUpdatedAction(featureState.TASK_BILLING_ITEMS_GRID_ID, { itemId: action.payload.item.id, item: result }) :
                                new GridStoreActions.GridNewItemAddedAction(featureState.TASK_BILLING_ITEMS_GRID_ID, { item: result }),
                            new featureActions.LoadTaskBillingItemsSuccess(),
                        ]
                    }
                    ),  
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    // tslint:disable-next-line:member-ordering
    deleteBillingItem$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteBillingItemRequest>(
            featureActions.ActionTypes.DELETE_BILLING_ITEM_REQUEST
        ),
        withLatestFrom(
            this.store$.select(featureSelectors.getWorkPeriod),
        ),
        switchMap(([action, wp]) =>
            this.ticketService
                .deleteTicketBillingItem(wp && wp.ticketId, action.payload.itemId) //"af441378-01ee-cf12-6d9f-41dc9b427f16"
                .pipe(
                    concatMap(() => {
                            this.page.notification.show(`Billing item deleted`);
                            return [
                                    new GridStoreActions.GridItemRemovedAction(featureState.TASK_BILLING_ITEMS_GRID_ID, { itemId: action.payload.itemId }),
                                    new featureActions.DeleteBillintItemSuccess(),
                            ]
                        }        
                    )
                )
        )
    ));

    // tslint:disable-next-line:member-ordering
    locationInventoryDeactivateSearchAction$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeactivateSearchAction>(
            featureActions.ActionTypes.DEACTIVATE_SEARCH
        ),
        exhaustMap((action) =>
            this.masterInventoryService.getTaskInventoryByWP('', action.payload.location, false)
                .pipe(
                    concatMap(
                        (items: TaskInventory[]) => {
                            return [
                                new GridStoreActions.GridLoadItemsSuccessAction(featureState.LOCATION_INVENTORY_GRID_ID, { items }),
                                new featureActions.LoadTaskInventorySuccess(action.payload.location)
                            ]
                        }
                    ),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        )
    ));

    // tslint:disable-next-line:member-ordering
    movedInventorySearchTextChangedEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.SearchChangeAction>(
            featureActions.ActionTypes.SEARCH_CHANGE
        ),
        withLatestFrom(
            this.store$.select(featureSelectors.selectLocationInventoryGridItems),
            this.store$.select(featureSelectors.selectedSearchText)
        ),
        map(([_, items, searchText]) => {
            if (searchText) {
                var filteredItems = items.filter((item: TaskInventory) => {
                    if(item && item.description || item.partNumber) {
                        var itemContent = [
                            item.description ?? '',
                            item.partNumber ?? ''
                        ].join(' ').toLowerCase();
                    // var itemContent = (item.description && item.description.toString()).concat(item.partNumber && item.partNumber.toString()).toLocaleLowerCase();
                    if(itemContent) {
                        return itemContent.includes(searchText.toLocaleLowerCase());
                    }
                    return null;
                    }
                }
               )
                return new GridStoreActions.GridLoadItemsSuccessAction(featureState.LOCATION_INVENTORY_GRID_ID, { items: filteredItems })
            }
            return new featureActions.NoAction();
        })
    ));

    confirmDeleteWorkPeriodEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ConfirmDeleteWorkPeriod>(
            featureActions.ActionTypes.CONFIRM_DELETE_WORK_PERIOD 
        ),
        exhaustMap((action) =>
            this.page
                .confirm.show('Are you sure you want to delete this work period?', 'Are you sure?')
                .pipe(
                    map(
                        (result) => {
                            if (result) {
                                this.store$.dispatch(new featureActions.ManageWorkPeriodRequest({ request: action.payload.request }));
                            }
                            return new featureActions.NoAction();
                        }
                    )
                )
        )
    ));

    confirmDeleteBillingItemEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ConfirmDeleteBillingItem>(
            featureActions.ActionTypes.CONFIRM_DELETE_BILLING_ITEM 
        ),
        exhaustMap((action) =>
            this.page
                .confirm.show('Are you sure you want to delete this billing item?', 'Are you sure?')
                .pipe(
                    map(
                        (result) => {
                            if (result) {
                                this.store$.dispatch(new featureActions.DeleteBillingItemRequest({ itemId: action.payload.itemId }));
                            }
                            return new featureActions.LoadTaskInventorySuccess();
                        }
                    )
                )
        )
    ));

    loadTasksRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadTasksRequestAction>(
            featureActions.ActionTypes.LOAD_TASKS_REQUEST
        ),
        withLatestFrom(           
            this.store$.select(AgencyStoreSelectors.selectAssignedAgenciesToUser)
        ),
        exhaustMap(([action, userAssignedAgencies]) => {
            const canSeeAllAgencies = this.userIdentity.hasPermission([PermissionsEnum.SeeAllAgencies]);
            const isSysAdmin = this.userIdentity.isSysAdmin;
            
            const filter = {
                ...action.payload,
                paging: false,
                isFinalizedClicked: true,
                approvalStatuses: [ApprovalStatus.Approved, ApprovalStatus.Rejected, ApprovalStatus.Submitted, ApprovalStatus.Undefined, ApprovalStatus.Current],
                agencies: isSysAdmin || canSeeAllAgencies ? [] : userAssignedAgencies 
            }
            return this.ticketService.getTicketList(filter)
                .pipe(
                    map((res) => new featureActions.LoadTasksSuccessAction({ tickets: res.body.items, nextPagToken: res.nextPageToken })),
                    catchError(error => of(new featureActions.LoadFailureAction({ error })))
                )
        })
    ));

    returnTaskToPMEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.ReturnTaskToPM>(
            featureActions.ActionTypes.RETURN_TASK_TO_PM
        ),
        withLatestFrom(
            this.store$.pipe(select(featureSelectors.getTaskDetails)),
            this.store$.pipe(select(featureSelectors.selectTaskDetailsBill)),
            this.store$.pipe(select(featureSelectors.selectSingleTimesheet)),
        ),
        concatMap(([action, task, bill, timesheet]) => {
            let user = {
                id: this.userIdentity.id,
                name: this.userIdentity.fullName
            }

            let historyRecord = {
                date: new Date(),
                user,
                status: ApprovalStatus.Rejected,
                comment: action.payload.comment
            } as ApprovalStatusHistory;

            let approvalStatusChange = {
                ticketId: task?.id,
                status: ApprovalStatus.Rejected,
                historyRecord
            } as ApprovalStatusChange;

            this.router.navigate([`/maintenance/timesheet/${timesheet.id}`], { state: { shouldDeactivate: true }});

            return [
                new BillingStoreActions.ChangeTaskApprovalStatus({ approvalStatusChange }),
                new featureActions.DeleteBillRequestAction({ billId: bill && bill.id || '' })
            ];
        })
    ));

    loadBillRequest$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadTaskDetailsBillRequest>(
            featureActions.ActionTypes.LOAD_TASK_DETAILS_BILL_REQUEST
        ),
        switchMap((action) => {
            var filter = {
                ticketNo: action.payload.taskNo,
            } as Filter;

            return this.billingService.filterBills(filter)
                .pipe(
                    concatMap((items: any) => {
                        var items = items.items;
                        return [
                            new featureActions.LoadTaskDetailsBillSuccess({ bill: items[0] }),
                        ]
                    }),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        })
    ));

    deleteRequestEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.DeleteBillRequestAction>(
            featureActions.ActionTypes.DELETE_BILL_REQUEST
        ),
        switchMap((action) =>
            this.billingService
                .deleteBill(action.payload.billId || '')
                .pipe(
                    map(() => {
                        this.page.notification.show('Bill removed');
                        return new featureActions.NoAction();
                    })
                )
        )
    ));

    generatePayrollOutput$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.GeneratePayrollOutputAction>(
            featureActions.ActionTypes.GENERATE_PAYROLL_OUTPUT
        ),
        withLatestFrom(
            this.store$.select(featureSelectors.selectPayPeriodEndDate),
        ),
        switchMap(([_, date]) => {
            return this.timesheetsService.getPayrollOutputData(date!)
                .pipe(
                    map((result) => {
                        this.generatePayrollOutputFile(result, date!.toISOString().split('T')[0]);
                        return new featureActions.GeneratePayrollOutputSuccessAction();
                    }),
                    catchError(error =>
                        of(new featureActions.LoadFailureAction({ error }))
                    )
                )
        })
    ));

    getLatestTimesheet$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadTimesheetRequestAction>(
            featureActions.ActionTypes.LOAD_TIMESHEET_REQUEST
        ),
        switchMap((action) => {         
            return this.timesheetsService.getTimesheetById(action.payload.item.id!).pipe(
                map(
                    (result) => {
                        if (result !== undefined) { 
                            result.workRecords = action.payload.item.workRecords;
                            return new featureActions.UpdateRequestAction({item: result});
                        }
                        return new featureActions.NoAction();                            
                    }                            
                ),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        })
    ));

    loadUsersEmployeesEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadEmployees>(featureActions.ActionTypes.LOAD_USERS_EMPLOYEES),
        switchMap(() => {
            return this.userService.getUsers().pipe(
                concatMap(users => [new featureActions.LoadEmployeesSuccess({ users })]),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        })
    ));

    loadOnlyEmployeeEffect$ = createEffect(() => this.actions$.pipe(
        ofType<featureActions.LoadOnlyEmployeesRequestAction>(featureActions.ActionTypes.LOAD_ONLY_EMPLOYEES_REQUEST),
        switchMap(() => {
            return this.employeeService.getEmployees().pipe(
                concatMap(employees => [new featureActions.LoadOnlyEmployeesSuccessAction({employees})]),
                catchError(error => of(new featureActions.LoadFailureAction({ error })))
            )
        })
    ));

    // Private methods
    private processAllWeeklyTimesheets(weeklyTimesheets: any[]): any[] {
        return weeklyTimesheets.filter(timesheet => !timesheet.isFullyTimesheet);
    }

    private generatePayrollOutputFile(data: any, date: string): void {
        const blob = new Blob([data], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `payroll_output_${date}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
    }

    private getYesterday(): string {
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);
        return yesterday.toISOString().split('T')[0];
    }
}
