import { Component, OnInit, Input, On<PERSON><PERSON>roy, ViewChild, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, Observable } from 'rxjs';
import { FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { takeUntil } from 'rxjs/operators';

import { Store } from '@ngrx/store';
import { OptionGroup } from '../mms-select-menu/option-group.model';
import { UserAndGroup } from '../../../../configuration/shared/user-and-groups/user-and-group.model';
import { UserTypeEnum } from '../../../../configuration/shared/user-and-groups/user-type.enum';
import { UserStoreState, UserStoreSelectors, UserStoreActions } from '../../../../root-store/user-store';
import { MMSSelectMenuComponent } from '../mms-select-menu/mms-select-menu.component';
import { MatFormFieldModule } from '@angular/material/form-field';

@Component({
    selector: 'mms-select-user',
    templateUrl: './mms-select-user.component.html',
    styleUrls: ['./mms-select-user.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MMSSelectMenuComponent
    ]
})
export class MMSSelectUserComponent implements OnInit, OnDestroy {
    menuValue: string = '';
    users$: Observable<UserAndGroup[]>;
    users: UserAndGroup[] = [];
    optionGroups: OptionGroup[] = [];
    @ViewChild(MMSSelectMenuComponent) mmsSelectMenu!: MMSSelectMenuComponent;

    @Input() isMultiple = false;
    @Input() ticketDropdown = false;
    @Input() valueAsOptions = false;
    @Input() placeholder = '';
    @Input() required = false;
    @Input() clearSearchInput = false;
    @Input() formControlName = '';
    @Input() showUsersOnly = false;

    private _destroy = new Subject<void>();

    constructor(
        private store$: Store<UserStoreState.State>,
        @Optional() public parentFormGroup: FormGroupDirective,
        public errorStateMatcher: ErrorStateMatcher
    ) {
        this.users$ = this.store$.select(UserStoreSelectors.selectAllUserAndGroupsItems);
        this.users$.pipe(
            takeUntil(this._destroy)
        ).subscribe(users => {
            this.users = users;
            this.setupOptionGroups();
        });
    }

    ngOnInit() {
        this.store$.dispatch(new UserStoreActions.LoadUGRequestAction());
    }

    ngOnDestroy() {
        this._destroy.next();
        this._destroy.complete();
    }

    setupOptionGroups() {
        if (this.showUsersOnly) {
            this.optionGroups = [
                {
                    name: 'Users',
                    icon: 'person'
                }
            ];
        } else {
            this.optionGroups = [
                {
                    name: 'Users',
                    icon: 'person'
                },
                {
                    name: 'Groups',
                    icon: 'group'
                }
            ];
        }
    }

    checkOptionGroup(option: UserAndGroup, groupName: string): boolean {
        if (this.showUsersOnly) {
            return option.type === UserTypeEnum.User && groupName === 'Users';
        }
        return option.type === UserTypeEnum.User && groupName === 'Users' ||
            option.type === UserTypeEnum.Group && groupName === 'Groups';
    }

    selectUserValueMapper(option: UserAndGroup): any {
        return option.id;
    }

    selectAdditionalInfoMapper(option: UserAndGroup): string {
        return option.email || '';
    }

    userCompareFn(c1: any, c2: any): boolean {
        return c1 === c2;
    }

    onSelectionChange(event: any) {
        // Handle selection change
    }

    onOpenedChange(event: boolean) {
        // Handle opened change
    }

    clearValue() {
        // Handle clear value
    }

    valueChanged(event: any) {
        // Handle value changed
    }

    savedSelect() {
        // Handle save select
    }

    clearValueEmit() {
        // Handle clear value emit
    }
}
