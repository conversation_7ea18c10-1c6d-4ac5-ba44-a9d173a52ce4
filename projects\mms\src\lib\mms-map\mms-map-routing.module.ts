import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuard } from '../core/auth/auth.guard';
import { PermissionsEnum } from '../core/auth/permissions.enum';
import { MmsMapComponent } from './mms-map.component';

export const routes: Routes = [
    {
        path: '',
        component: MmsMapComponent,
        canActivate: [AuthGuard],
        data: { permissions: PermissionsEnum.LocationsModuleAccess }
    }
];

@NgModule({
    imports: [RouterModule],
    exports: [RouterModule]
})

export class MmsMapRoutingModule {
}
