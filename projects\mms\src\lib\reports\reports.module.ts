import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

import { MaterialModule } from '../material.module';
import { ReportsRoutingModule } from './reports-routing.module'
import { ReportsComponent } from './reports.component'
import { MovedAssetsComponent } from './moved-assets/moved-assets.component';
import { PmComingDueComponent } from './pm-coming-due/pm-coming-due.component';
import { AssetsCountFilterComponent } from './assets-counts/assets-counts-filter.component';
import { ReportsService } from './reports.service';
import { FormsModule } from '../shared/forms/forms.module';
import { AssetsCountComponent } from './assets-counts/assets-counts.component';
;
import { NgxPrintModule } from 'ngx-print';
import { MovedAssetsFilterComponent } from './moved-assets/moved-assets-filter.component';
import { PmComingDueFilterComponent } from './pm-coming-due/pm-coming-due-filter.component';
import { MultipleIssuesLocationsFilterComponent } from './multiple-issues-locations/multiple-issues-locations-filter.component';
import { MultipleIssuesLocationsComponent } from './multiple-issues-locations/multiple-issues-locations.component';
import { DispatchHistoryFilterComponent } from './dispatch-history/dispatch-history-filter.component';
import { DispatchHistoryComponent } from './dispatch-history/dispatch-history.component';
import { ProjectActivityComponent } from './project-activity/project-activity.component';
import { ProjectActivityFilterComponent } from './project-activity/project-activity-filter.component';
import { PmEfficiencyComponent } from './pm-efficiency/pm-efficiency.component';
import { PmEfficiencyFilterComponent } from './pm-efficiency/pm-efficiency-filter.component';
import { FailureRateFilterComponent } from './failure-rates/components/failure-rate-filter/failure-rate-filter.component';
import { FailureRateReportComponent } from './failure-rates/components/failure-rate-report/failure-rate-report.component';
import { LimestoneModule } from '@econolite/limestone';
import { PmScheduledComponent } from './pm-scheduled/pm-scheduled.component';
import { PmScheduledFilterComponent } from './pm-scheduled/pm-scheduled-filter.component';
import { SparePartsFilterComponent } from './spare-parts/spare-parts-filter.component';
import { PerformancePriorityReportFilterComponent } from './performance-priority/components/performance-priority-report-filter/performance-priority-report-filter.component';
import { PerformancePriorityReportComponent } from './performance-priority/components/performance-priority-report/performance-priority-report.component';
import { UpcomingPmComponent } from './upcoming-pm/upcoming-pm.component';
import { WoCallComponent } from './wo-call-report/wo-call.component';
import { WoCallFilterComponent } from './wo-call-report/wo-call-filter.component';
import { CustomComponent } from './custom/custom.component';
import { ProjectBudgetReportFilterComponent } from './project-budgeting-report/components/project-budget-report-filter/project-budget-report-filter.component';
import { ProjectBudgetReportComponent } from './project-budgeting-report/components/project-budget-report/project-budget-report.component';
import { PastDueRepairsComponent } from './past-due-repairs/past-due-repairs.component';
import { PastDueRepairsFilterComponent } from './past-due-repairs/past-due-repairs-filter.component';
import { AssetValueReportComponent } from './asset-value/components/asset-value-report/asset-value-report.component';
import { AssetValueReportFilterComponent } from './asset-value/components/asset-value-report-filter/asset-value-report-filter.component';
import { QuotesFilterComponent } from './quote-tracking/components/quote-tracikng-report-filter/quote-tracking-report-filter.component';
import { QuotesReportComponent } from './quote-tracking/components/quote-tracking-report/quote-tracking-report.component';
import { LocationsComponent } from './locations/locations.component';
import { LocationsFilterComponent } from './locations/locations-filter.component';
import { OutstandingPmComponent } from './outstanding-pm/outstanding-pm.component';
import { SparePartsComponent } from './spare-parts/spare-parts.component';

@NgModule({
    imports: [
        CommonModule,
        ReactiveFormsModule,
        ReportsRoutingModule,
        MaterialModule,
        FormsModule,
        
        NgxPrintModule,
        LimestoneModule,
        ReportsComponent,
        AssetsCountComponent,
        AssetsCountFilterComponent,
        MovedAssetsComponent,
        MovedAssetsFilterComponent,
        PmComingDueComponent,
        PmComingDueFilterComponent,
        SparePartsComponent,
        MultipleIssuesLocationsFilterComponent,
        MultipleIssuesLocationsComponent,
        DispatchHistoryFilterComponent,
        DispatchHistoryComponent,
        ProjectActivityComponent,
        ProjectActivityFilterComponent,
        PmEfficiencyComponent,
        PmEfficiencyFilterComponent,
        FailureRateFilterComponent,
        FailureRateReportComponent,
        PmScheduledComponent,
        PmScheduledFilterComponent,
        SparePartsFilterComponent,
        PerformancePriorityReportFilterComponent,
        PerformancePriorityReportComponent,
        UpcomingPmComponent,
        WoCallComponent,
        WoCallFilterComponent,
        CustomComponent,
        ProjectBudgetReportFilterComponent,
        ProjectBudgetReportComponent,
        PastDueRepairsComponent,
        PastDueRepairsFilterComponent,
        AssetValueReportComponent,
        AssetValueReportFilterComponent,
        QuotesFilterComponent,
        QuotesReportComponent,
        LocationsComponent,
        LocationsFilterComponent,
        OutstandingPmComponent
    ],
    providers: [ReportsService],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ReportsModule {

}
