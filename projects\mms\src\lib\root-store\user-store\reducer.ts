import { Actions, ActionTypes } from './actions';
import { State, initialState, } from './state';

export function featureReducer(state = initialState, action: Actions): State {
    switch (action.type) {
        case ActionTypes.LOAD_UG_REQUEST: {
            return {
                ...state,
                isLoading: true
            }
        }
        case ActionTypes.LOAD_TICKET_USERS_SUCCESS: 
        // case ActionTypes.LOAD_UG_SUCCESS: {
        //     return featureAdapter.setAll(action.payload.usersAndGroups, {
        //         ...state,
        //         isLoading: false
        //     });
        // }

        case ActionTypes.LOAD_FAILURE : {
            return {
                ...state,
                isLoading: false
            };
        }

        case ActionTypes.LOAD_USER: {
            return {
                ...state,
                isLoading: true
            }
        }

        case ActionTypes.LOAD_USER_SUCCESS: {
            return  {
                ...state,
                user: action.payload.user,
                isLoading: false
            };
        } 
        case ActionTypes.LOAD_DEFAULT_TASK_USER_SUCCESS: {
            return  {
                ...state,
                dafaultTaskUser: action.payload.usersAndGroups,
            };
        } 
        case ActionTypes.LOAD_USER_BY_MMS_SUCCESS: {
            return  {
                ...state,
                userWithRoles: action.payload.user,
                isLoading: false
            };
        } 
        case ActionTypes.LOAD_USERS_FROM_IDENTITY_SUCCESS: {
            return  {
                ...state,
                identityUsers: action.payload.users,
                isLoading: false
            };
        }          
        default: {
            return state;
        }
    }
}


