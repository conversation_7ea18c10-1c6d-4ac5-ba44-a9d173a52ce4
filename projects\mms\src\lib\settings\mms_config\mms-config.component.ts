import { CommonModule } from '@angular/common';
import { Component, OnInit, HostListener } from '@angular/core';
import { FormGroup, FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { CanDeactivate } from '../../core/can-deactivate.model';
import { map, take } from 'rxjs/operators';
import { Validators } from '../../shared/forms/validators/validators';
import { Title } from '@angular/platform-browser';
import { MmsSettingsStoreSelectors, MmsSettingsStoreState, MmsSettingsStoreActions } from '../../root-store';
import { Store } from '@ngrx/store';
import { FormRevertChangesAction, ConnectFormDirective } from '../../shared/forms/connect-form-ngrx';
import { ValidationMessageComponent } from '../../shared/forms/validation-message/validation-message.component';
import { BlockUIDirective } from '../../shared/block-ui/block-ui.directive';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  templateUrl: './mms-config.component.html',
  styleUrls: ['./mms-config.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatInputModule,
    MatTooltipModule,
    ValidationMessageComponent,
    BlockUIDirective,
    NgxMaskDirective,
    ConnectFormDirective
  ],
  providers: [
    provideNgxMask()
  ]
})
export class MMSConfigComponent implements OnInit, CanDeactivate {

  formGroup: FormGroup = this.form.group({
    assetsOutOfService: [null, [Validators.required(), Validators.number(), Validators.min(1), Validators.max(100000)]],
    retainLastAdded: [null, Validators.required()],
    assetsPastExpectedFailure: [null, [Validators.required(), Validators.number(), Validators.min(1), Validators.max(100000)]],
    callerPhonePattern: [null, [Validators.allowedCharacters(['(', ')', '0', ' ', '-'])]],
    auxDataVisibility: [],
    tagsVisibility: [],
    taskSwitchTimeLimit: [5, Validators.required()]
  });

  isLoading$ = this.store$.select(MmsSettingsStoreSelectors.selectIsLoading);
  mmsConfig$ = this.store$.select(MmsSettingsStoreSelectors.selectedMmsSettings);

  constructor(private form: FormBuilder,
    private store$: Store<MmsSettingsStoreState.State>,
    private title: Title) { this.title.setTitle('MMS Settings'); }

  ngOnInit() {
    this.store$.dispatch(new MmsSettingsStoreActions.LoadRequestAction());
  }

  save() {
    if (this.formGroup.valid) {
      this.store$.dispatch(new MmsSettingsStoreActions.UpdateStateAction(this.formGroup.value));
    }
  }

  cancel() {
    this.store$.dispatch(new FormRevertChangesAction());
  }

  canDeactivate() {
    return this.store$.select(
      MmsSettingsStoreSelectors.selectedMmsSettings
    ).pipe(map((settings) => {
      if (settings != null) {
        return Object.equal(this.formGroup.value, settings, ['assetsNearingFailure', 'id', 'multipleCallTicketsTreshold', 'outStandingCallTickets',
          'outStandingWorkOrders', 'preventiveMaintenancePastDue', 'tenantId', 'ticketsLocation', 'ticketsLocationOverTimeDays'])
      } else if (this.formGroup.pristine) {
        return true;
      } else {
        return false;
      }
    }),
      take(1)
    );
  }

  @HostListener('window:beforeunload')
  canUnloadPage(): boolean {
    var returnValue = false;
    this.canDeactivate().subscribe(x => returnValue = x);
    return returnValue;
  }
}
