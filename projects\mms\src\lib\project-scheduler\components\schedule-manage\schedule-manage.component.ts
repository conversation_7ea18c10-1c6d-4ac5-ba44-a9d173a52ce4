import { <PERSON><PERSON><PERSON>, OnInit, ViewChild, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { Subscription, of } from 'rxjs';
import { Schedule } from '../../models/schedule.model';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { MmsDateTimepickerType } from '../../../shared/forms/controls/date-time-picker/date-time-picker.component';
import { CronOptions } from '../../../shared/cron-editor/CronOptions';
import { Store } from '@ngrx/store';
import { Router } from '@angular/router';
import { LocationInstanceStoreState, LocationInstanceStoreActions, LocationInstanceStoreSelectors } from '../../../root-store/location-instance-store';
import { Validators } from '../../../shared/forms/validators/validators';
import { reccurenceValidatorFn } from './reccurence.validator';
import { ProjectsSchedulerStoreState, ProjectsSchedulerStoreSelectors, ProjectsSchedulerStoreActions } from '../../../root-store/projects-scheduler-store';
import { withLatestFrom, map, take } from 'rxjs/operators';
import { FormRevertChangesAction } from '../../../shared/forms/connect-form-ngrx';
import { ScheduleType } from '../../enums/schedule-type.enum';
import { UserAndGroup } from '../../../configuration/shared/user-and-groups/user-and-group.model';
import { PriorityEnum } from '../../../configuration/tickets/tickets-enum/priority.enum';
import { Project } from '../../../configuration/projects/models/project.model';
import { ProjectCategory } from '../../../configuration/projects/enums/project-category.enum';
import { ProjectStatus } from '../../../configuration/projects/enums/project-status.enum';
import { CanDeactivate } from '../../../core/can-deactivate.model';
import { CronGenComponent } from '../../../shared/cron-editor/cron-editor.component';
import { TicketItem } from '../../models/ticket-item';
import { ProjectItem } from '../../models/project-item';
import { ProjectType } from '../../../configuration/project-types/models/project-type.model';
import { AreaTypeEnum } from '../../../configuration/location-instances/shared/area-type.enum';
import { LocationTypeEntity } from '../../../configuration/location-instances/shared/location-type-entity';
import { LocationStoreActions, LocationStoreSelectors } from '../../../root-store';
import { MatDialog } from '@angular/material/dialog';
import { LocationTypesChecklistsManageComponent } from '../../../configuration/shared/location-types-checklists-manage/location-types-checklists-manage.component';
import { TicketTypeCategory } from '../../../configuration/ticket-types/enums/ticket-category.enum';
import { ContractStoreActions, ContractStoreSelectors } from '../../../root-store/contract-store';
import { PriorityStoreActions, PriorityStoreSelectors } from '../../../root-store/priority-store';
import { Contract } from '../../../payroll-and-billing/contracts/models/contract.model';
import { TicketType } from '../../../configuration/ticket-types/models/ticket-type.model';
import { UserStoreActions} from '../../../root-store/user-store';
import { Priority } from '../../../configuration/priority/models/priority.model';
import { parseExpression, ParserOptions } from 'cron-parser';
import { Page } from '../../../core/page.service';
import { LocationTypeChecklist } from '../../../configuration/shared/models/location-type-checklist.model';
import { MaterialModule } from '../../../material.module';
import { VarDirective } from '../../../shared/forms/variable.directive';
;

@Component({
  selector: 'mms-schedule-manage',
  templateUrl: './schedule-manage.component.html',
  styleUrls: ['./schedule-manage.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MaterialModule,
    VarDirective,
  ]
})
export class ScheduleManageComponent implements OnInit, OnDestroy, CanDeactivate {
  isSidebarLoading$ = this.store$.select(ProjectsSchedulerStoreSelectors.IsSidebarLoading);
  selectedSchedule$ = this.store$.select(ProjectsSchedulerStoreSelectors.selectSelectedSchedule);
  formGroup!: FormGroup;
  projectFormGroup!: FormGroup;
  ticketFormGroup!: FormGroup;
  mmsDateTimepickerType = MmsDateTimepickerType;
  subscriptions: Subscription[] = [];
  isEdit = false;
  selectedScheduleType$ = this.store$.select(ProjectsSchedulerStoreSelectors.selectScheduleType);
  scheduleType = ScheduleType;
  projectCategory = ProjectCategory;
  projects$ = this.store$.select(ProjectsSchedulerStoreSelectors.selectProjects);
  ticketTypes$ = this.store$.select(ProjectsSchedulerStoreSelectors.selectFilteredTicketTypes);
  projectTypes$ = this.store$.select(ProjectsSchedulerStoreSelectors.selecProjectTypes);
  defaultTicketPriority$ = this.store$.select(ProjectsSchedulerStoreSelectors.getPriorityValue);
  defaultScheduleActive$ = this.store$.select(ProjectsSchedulerStoreSelectors.getScheduleActive);
  locationTypes$ = this.locationStore$.select(LocationInstanceStoreSelectors.selectLocationTypes);
  locationTypesChecklists$ = this.store$.select(LocationStoreSelectors.getLocationTypesChecklist);
  contractPrices$ = this.store$.select(ProjectsSchedulerStoreSelectors.getSelectedContractPrices);
  priorities$ = this.store$.select(PriorityStoreSelectors.getAllPriorities);
  contracts$ = this.store$.select(ContractStoreSelectors.selectContracts);
  locationTypesChecklistsItems: LocationTypeChecklist[] = [];
  selectedSchdulerCheklist: LocationTypeChecklist[] = [];
  defaultPriorityItem!: Priority;
  selectedTicketType!: TicketType;
  locationTypes: LocationTypeEntity[] = [];
  defaultScheduleActive = false;
  noEndDateChecked = true;
  selectedProjectType!: ProjectType;
  @ViewChild('reccurenceForm', { static: false}) reccurenceForm!: CronGenComponent;
  @ViewChild('selectedScheduleType', { static: false}) selectedScheduleType: any;

  priorityItems = (Object.keys(PriorityEnum) as Array<keyof typeof PriorityEnum>)
    .filter(key => isNaN(Number(key)))
    .map(p => ({
      id: PriorityEnum[p],
      name: p
    }));

  users: UserAndGroup[] = [];
  existingProjectSelected = true;
  createTicketSelected = true;
  projectNameExist$ = of(false);
  projectToReplace!: Project;
  isDurationInMonthsFieldVisible = false;
  singleEvent = false;
  repositoryId = '';

  // Daily default value
  private defaultReccurence = '0 0 1/1 * *';
  private _cronExpression = this.defaultReccurence
  public set cronExpression(value: string) {
    this._cronExpression = value;
    this.formGroup.patchValue({
      reccurence: value
    })
  }
  public get cronExpression() {
    return this._cronExpression;
  }
  public cronOptions: CronOptions = {
    formInputClass: 'form-control cron-editor-input',
    formSelectClass: 'form-control cron-editor-select',
    formRadioClass: 'cron-editor-radio',
    formCheckboxClass: 'cron-editor-checkbox',
    defaultTime: '00:00:00',
    hideTimeFields: true,
    hideMinutesTab: true,
    hideHourlyTab: true,
    hideDailyTab: false,
    hideWeeklyTab: false,
    hideMonthlyTab: false,
    hideYearlyTab: false,
    hideAdvancedTab: true,
    hideSpecificWeekDayTab: false,
    hideSpecificMonthWeekTab: false,
    use24HourTime: true,
    hideSeconds: false,
    cronFlavor: 'standard'
  };

  minDate = new Date();
  minStartDate = new Date(this.minDate.getFullYear() - 1, this.minDate.getMonth(), this.minDate.getDate(), 0, 0, 0, 0);
  maxDate: Date | null = null;
  maxDateForEndDate: Date | null = null;

  constructor(private store$: Store<ProjectsSchedulerStoreState.State>,
    private formBuilder: FormBuilder,
    private router: Router,
    private locationStore$: Store<LocationInstanceStoreState.State>,
    public dialog: MatDialog,
    public page: Page) { }

  ngOnInit() {
    this.defaultScheduleActive$.subscribe(value => this.defaultScheduleActive = value);

    this.store$.dispatch(new ProjectsSchedulerStoreActions.LoadSidebarDataRequestAction());

    this.formGroup = this.formBuilder.group({
      name: [null, [Validators.required(),
        Validators.emoji()],
        [Validators.async(this.isNameValid.bind(this), '$value Schedule already exists.')]],
      startDate: [new Date(), [Validators.required()]],
      endDate: [null],
      active: [true],
      reccurence: [null, [reccurenceValidatorFn()]],
      durationInDays: [null, [Validators.min(1), Validators.max(999)]],
      durationInMonths: [null, [Validators.min(1), Validators.max(999)]],
      numberOfOccurrences: [null, [Validators.min(1), Validators.max(999)]],
      endType: [null],
      project: [null],
      applyToBoth: [false],
      noOfDaysToGenerateInAdvance: [0, [Validators.min(0), Validators.max(999)]]
    });

    this.projectFormGroup = this.formBuilder.group({
      name: [null],
      type: [null],
      description: [null],
      isBillable: [false],
      closeExistingProject: [false],
      files: [null],
      contract: [null],
      purchaseOrder: [null],
      externalReference: [null],
      payroll: [null]
    });

    this.ticketFormGroup = this.formBuilder.group({
      id: [null],
      type: [null, {disabled: true}, [Validators.required()]],
      description: [null, [Validators.required(), Validators.emoji()]],
      locations: [null, [Validators.required()]],
      regions: [null, [Validators.required()]],
      users: [[]],
      contractPrice: [null],
      priorityItem: [null, Validators.required()]
    });

    this.selectedSchedule$.subscribe(schedule => {
      if (schedule && schedule.reccurenceValue) {
        this.isDurationInMonthsFieldVisible = ['Yearly', 'Monthly'].includes(schedule.reccurenceValue);      
        if (schedule.endDate || schedule.numberOfOccurrences){
          this.noEndDateChecked = false;
        }
        this.selectedSchdulerCheklist = schedule && schedule.locationTypesChecklists ? schedule.locationTypesChecklists : [];

        if (schedule.ticket && schedule.ticket.priorityItem){
          this.defaultPriorityItem = schedule.ticket.priorityItem;
          const priorityControl = this.ticketFormGroup.get('priorityItem');
          if (priorityControl) {
            priorityControl.setValue(schedule.ticket.priorityItem);
          }
        }

        if (schedule.ticket && schedule.ticket.type){
          this.selectedTicketType = schedule.ticket.type;
        }

        if (schedule.project && schedule.project.type){
          this.selectedProjectType = schedule.project.type;
        }
      }
    })


    this.subscriptions.push(this.locationTypes$.subscribe(locationTypes => {
      if (locationTypes) {
        this.locationTypes = locationTypes;
      }
    }))

    setTimeout(() => {
      this.checkValidation();
    });

    this.store$.dispatch(new LocationStoreActions.SetLocationTypeChecklistConfigAction({locationTypesChecklists: []}));

    this.store$.dispatch(
      new ContractStoreActions.LoadRequestAction()
    );

    this.store$.dispatch(
      new PriorityStoreActions.LoadPrioritiesRequestAction()
    );

    var ticketType = new TicketType;
    ticketType.category = TicketTypeCategory.Call;
    this.store$.dispatch(new UserStoreActions.LoadUsersForTicketRequestAction({ ticketType: ticketType }));


    if(this.router.url.includes("add-schedule")) {
      this.priorities$.subscribe((priorities) => {
        if(priorities){
          let priority = priorities.find(r => r.name == "Normal");
          if(priority){
            this.defaultPriorityItem = priority;
            this.ticketFormGroup.get('priorityItem')?.setValue(priority)
          }
        }
      })
    }
  }

  isValidCheklist() {
    return this.ticketFormGroup.controls['locations'].touched ? true : false;
  }

  checkValidation() {
    const ticketType = this.ticketFormGroup.get('type');
    const contractPrice = this.ticketFormGroup.get('contractPrice');
    const projectControl = this.formGroup.get('project');
    const startDateControl = this.formGroup.get('startDate');
    const endDateControl = this.formGroup.get('endDate');

    if (!projectControl || !ticketType || !contractPrice || !startDateControl || !endDateControl) {
      return;
    }

    this.subscriptions.push(projectControl.valueChanges.subscribe((project: Project) => {
      if (project && project.startDate && project.endDate && project.id) {
        this.minDate = new Date(project.startDate);
        this.minStartDate = new Date(project.startDate);
        this.maxDate = new Date(project.endDate);
        this.maxDateForEndDate = new Date(project.endDate);
        contractPrice.enable();
        this.store$.dispatch(new ProjectsSchedulerStoreActions.FilterTicketTypesByProjectTypeAction({ category: project.type?.category ?? ProjectCategory.Undefined }));
        this.store$.dispatch(new ProjectsSchedulerStoreActions.ChangeSelectedProject({ project }));
        this.store$.dispatch(new ProjectsSchedulerStoreActions.SetSelectedContractAction({ contractId: project.contract?.id! ?? '' }));
        this.updateValidations(project);
      } 
      else {
        this.maxDateForEndDate = null;
        ticketType.reset();
        contractPrice.reset();
        contractPrice.disable();
      }
    }));

    const contractSubscription = this.projectFormGroup.get('contract')?.valueChanges.subscribe((item: Contract) => {
      if(item && item.id){
        this.store$.dispatch(new ProjectsSchedulerStoreActions.SetSelectedContractAction({contractId: item.id}))    
        if(item.contractPrices && item.contractPrices.length && item.contractPrices.length > 0){
          contractPrice.enable();
        }
      }
    });
    if (contractSubscription) {
      this.subscriptions.push(contractSubscription);
    }
    
    const typeSubscription = this.projectFormGroup.get('type')?.valueChanges.subscribe((type: ProjectType) => {
      if (type) {
        this.store$.dispatch(new ProjectsSchedulerStoreActions.FilterTicketTypesByProjectTypeAction({ category: type.category ?? ProjectCategory.Undefined }));
      }
    });
    if (typeSubscription) {
      this.subscriptions.push(typeSubscription);
    }

    const nameSubscription = this.projectFormGroup.get('name')?.valueChanges.subscribe((name: string) => {
      if (name) {
        this.projectNameExist$ = this.isProjectNameExist(name);
      }
    });
    if (nameSubscription) {
      this.subscriptions.push(nameSubscription);
    }

    const reccurenceSubscription = this.formGroup.get('reccurence')?.valueChanges.subscribe((reccurence: string) => {
      if (reccurence && reccurence !== this.cronExpression) {
        this.cronExpression = reccurence;
      }
    });
    if (reccurenceSubscription) {
      this.subscriptions.push(reccurenceSubscription);
    }

    this.subscriptions.push(
      this.selectedSchedule$.subscribe(selectedSchedule => {
        if (selectedSchedule == null) {
          this.isEdit = false;
          this.existingProjectSectionToggle(true);
        }

        if (selectedSchedule != null) {
          this.isEdit = true;
          this.projects$.subscribe((projects: Project[]) => {
            const project = projects.find(x => x.id === selectedSchedule.project.id) as Project | undefined;
            if (project && project.startDate && project.endDate) {
              this.minDate = new Date(project.startDate);
              this.minStartDate = new Date(project.startDate);
              this.maxDate = new Date(project.endDate);
              this.maxDateForEndDate = new Date(project.endDate);
              this.repositoryId = project.id;
              setTimeout(() => {
                this.checkRegionLocationValidators();
              }, 6000);
            }
          });

          if (selectedSchedule.numberOfOccurrences && selectedSchedule.numberOfOccurrences > 0) {
            this.formGroup.patchValue({ 'endType': 'numberOfOccurences' });
           } else if (selectedSchedule.endDate) {
             this.formGroup.patchValue({ 'endType': 'endDate' });
           }

           if (selectedSchedule.ticket && selectedSchedule.ticket.locations) {
             this.locationStore$.dispatch(new LocationInstanceStoreActions.SearchTextChangedAction(selectedSchedule.ticket.locations.map(i => i.id!)));
           } else {
             this.locationStore$.dispatch(new LocationInstanceStoreActions.SearchTextChangedAction(['']));
           }
        }

        if (selectedSchedule != null && selectedSchedule.reccurence == null) {
          this.singleEvent = true;
          this.store$.dispatch(new ProjectsSchedulerStoreActions.ChangeSelectedTypeAction({ type: ScheduleType.SingleEvent }));
          this.formGroup.get('reccurence')?.patchValue(null);
        } else if (selectedSchedule != null && selectedSchedule.reccurence != null) {
          this.store$.dispatch(new ProjectsSchedulerStoreActions.ChangeSelectedTypeAction({ type: ScheduleType.RecurringEvent }));
        }

        if (selectedSchedule && selectedSchedule.applyToBoth) {
          this.existingProjectSelected = false;
          this.existingProjectSectionToggle(false);
          

          if (!selectedSchedule.ticket) {
            this.createTicketSectionToggle(false);
          }
        }
      }));

    this.subscriptions.push(startDateControl.valueChanges.subscribe((value: string) => {
      const validators = [];
      if (this.singleEvent) {
        validators.push(Validators.required());
      }
      validators.push(Validators.minDate(value ? new Date(value) : new Date()));
      if (projectControl.value) {
        this.minDate = projectControl.value.startDate ? new Date(projectControl.value.startDate) : this.minDate;
      } else {
        this.minDate = new Date(value);
      }

      endDateControl.setValidators(validators);
      endDateControl.updateValueAndValidity();
    }));

    this.subscriptions.push(endDateControl.valueChanges.subscribe((value: string) => {
        if (value) {
            this.maxDate = new Date(value);
            if (this.maxDate) {
              startDateControl.setValidators([Validators.required(), Validators.maxDate(this.maxDate)]);
            } else {
              startDateControl.setValidators([Validators.required()]);
            }
        } else {
            if (projectControl.value) {
              this.maxDate = projectControl.value.endDate ? new Date(projectControl.value.endDate) : this.maxDate;
              if (this.maxDate) {
                startDateControl.setValidators([Validators.required(), Validators.maxDate(this.maxDate)]);
              } else {
                startDateControl.setValidators([Validators.required()]);
              }

            } else {
              this.maxDate = null;
              startDateControl.setValidators([Validators.required()]);
            }
        }
    }));
  }

  close() {
    this.router.navigate(['/maintenance/scheduler/calendar']);
    this.formGroup.reset()
  }

  clearDurationFields(field: FormControl) {
    field.reset();
  }

  clear() {
    this.store$.dispatch(new FormRevertChangesAction());
  }

  reset() {
    this.formGroup.reset();
    this.formGroup.controls['applyToBoth'].setValue('false');
    this.ticketFormGroup.reset();
    this.ticketFormGroup.get('priorityItem')?.setValue(this.defaultPriorityItem);
    this.formGroup.get('active')?.patchValue(true);
    if (this.reccurenceForm) {
      this.reccurenceForm.onClear('');
    }
    this.formGroup.markAsPristine();
    this.formGroup.patchValue({ 'endType': 'noEndDate' });
    this.formGroup.get('numberOfOccurrences')?.clearValidators();
    this.formGroup.get('endDate')?.clearValidators();
    this.projectFormGroup.reset();
    this.existingProjectSectionToggle(true);
  }


  save() { 
    const currentDate = new Date(Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), new Date().getUTCDate()));
    var reccurence = this.formGroup.get('reccurence')?.value;
    if(reccurence === null){
      this.submitForm();
      return;
    }
    const options: ParserOptions<boolean> = {
      tz: 'UTC',
      iterator: false,
      currentDate: currentDate,
      utc: true,
      startDate: currentDate
    };
    var parser = parseExpression(reccurence, options);
    var nextResult = parser.next();
    var nextOccurrence = nextResult && 'value' in nextResult ? nextResult.value.toDate() : null;
    var prevResult = parser.hasPrev() ? parser.prev() : null;
    var prevOccurrence = prevResult && 'value' in prevResult ? prevResult.value.toDate() : null;
    const startDateValue = this.formGroup.get('startDate')?.value;
    if (!startDateValue) {
      return;
    }
    const schedulerStartDateParts = startDateValue.split('/');
    const schedulerStartYear = parseInt(schedulerStartDateParts[2], 10);
    const schedulerStartMonth = parseInt(schedulerStartDateParts[0], 10) - 1;
    const schedulerStartDay = parseInt(schedulerStartDateParts[1], 10);
    var schedulerStartDate = new Date(Date.UTC(schedulerStartYear, schedulerStartMonth, schedulerStartDay));

    var noOfDaysToGenerateInAdvance = this.formGroup.get('noOfDaysToGenerateInAdvance')?.value ? 
      this.formGroup.get('noOfDaysToGenerateInAdvance')?.value as number : 0;

    if (!nextOccurrence) {
      return;
    }

    const nextOccurenceResult = new Date(nextOccurrence.getTime());
    nextOccurenceResult.setDate(nextOccurenceResult.getDate() - noOfDaysToGenerateInAdvance);

    const prevOccurenceResult = prevOccurrence ? new Date(prevOccurrence.getTime()) : null;
    if(prevOccurenceResult){
      prevOccurenceResult.setDate(prevOccurenceResult.getDate() - noOfDaysToGenerateInAdvance);
    }
    
    var nextOccurenceResultDay = nextOccurenceResult.getDate();
    var currentDateDay = currentDate.getDate();

    if(((noOfDaysToGenerateInAdvance == 0 || noOfDaysToGenerateInAdvance == null) && this.reccurenceForm.activeTab == "monthly" && schedulerStartDay > nextOccurenceResultDay && schedulerStartDate >= currentDate)){
      this.openSubmitDialog();
      return;
    }
    if(noOfDaysToGenerateInAdvance && noOfDaysToGenerateInAdvance > 0 && ((nextOccurenceResultDay <= currentDateDay && schedulerStartDate < currentDate) || 
    (prevOccurrence == null && (nextOccurenceResult <= currentDate || nextOccurenceResult < schedulerStartDate)) || (prevOccurrence != null && prevOccurenceResult && prevOccurenceResult < currentDate))){
      this.openSubmitDialog();
      return;
    }
    this.submitForm();
    return;
  }

  openSubmitDialog(): void {
    this.page
      .confirm.showWithCustomActions(
        'It is too close to the next scheduled task start date to generate tasks and therefore tasks will not be generated until the following recurrence date.', 
        'Warning',
        false,
        undefined,
        true,
        'Cancel',
        'OK'
      )
      .pipe(
        map(
          (result) => {
            if (result) {
              this.submitForm();
            }
          }
        )
      ).toPromise();
  }

  submitForm(): void {
    var locationTypesChecklists = this.locationTypesChecklistsItems;
    this.setLocationData();
    let ticketSelectedBasedCondition = false;
    if (!this.existingProjectSelected) {
      this.markFormTouched(this.projectFormGroup);
    }
    if (this.createTicketSelected) {
      this.markFormTouched(this.ticketFormGroup);
      if (this.ticketFormGroup.valid && this.formGroup.valid && this.projectFormGroup.valid && this.isLocationTypesChecklistsValid(locationTypesChecklists)) {
        ticketSelectedBasedCondition = true;
      }
    } else {
      if (this.formGroup.valid && this.projectFormGroup.valid) {
        ticketSelectedBasedCondition = true;
      }
    }
    if (ticketSelectedBasedCondition) {
      const rawValue = this.formGroup.getRawValue();
      const ticketRawValue = this.ticketFormGroup.getRawValue();
      const projectRawValue = this.projectFormGroup.getRawValue();

      const ticketItem = {
        type: ticketRawValue.type,
        priorityItem: ticketRawValue.priorityItem,
        description: ticketRawValue.description,
        locations: ticketRawValue.locations,
        regions: ticketRawValue.regions,
        users: ticketRawValue.users,
        contractPrice: ticketRawValue.contractPrice
      } as TicketItem;

      const projectItem = {
        id: this.projectToReplace ? this.projectToReplace.id: this.repositoryId,
        name: projectRawValue.name,
        type: projectRawValue.type,
        priority: projectRawValue.priority,
        description: projectRawValue.description,
        status: ProjectStatus.Open,
        files: projectRawValue.files,
        isBillable: projectRawValue.isBillable,
        contract: projectRawValue.contract,
        purchaseOrder: projectRawValue.purchaseOrder,
        externalReference: projectRawValue.externalReference,
        payroll: projectRawValue.payroll
      } as ProjectItem;

      const schedule = {
        id: '',
        tenantId: '', 
        name: rawValue.name,
        active: rawValue.active,
        reccurence: rawValue.reccurence,
        applyToBoth: rawValue.applyToBoth,
        startDate: rawValue.startDate,
        endDate: rawValue.endDate,
        project: this.existingProjectSelected ? rawValue.project : projectItem,
        endType: rawValue.endType,
        numberOfOccurrences: rawValue.numberOfOccurrences,
        durationInDays: rawValue.durationInDays,
        durationInMonths: rawValue.durationInMonths,
        ticket: this.createTicketSelected ? ticketItem : undefined,
        replaceExistingProject: projectRawValue.closeExistingProject,
        noOfDaysToGenerateInAdvance: rawValue.noOfDaysToGenerateInAdvance,
        agency: undefined,
        type: rawValue.type,
        locationTypesChecklists: locationTypesChecklists.length === 0 ? this.selectedSchdulerCheklist : locationTypesChecklists
      } as unknown as Schedule;

      this.store$.dispatch(
        new ProjectsSchedulerStoreActions.SaveFormAction({schedule, locationTypes: this.locationTypes})
      );
      this.formGroup.markAsPristine();
    }
    else {
      this.showRegions();
    }
  }

  private isNameValid(name: string) {
    return this.store$.select(ProjectsSchedulerStoreSelectors.selectAllSchedules).pipe(
      withLatestFrom(this.store$.select(ProjectsSchedulerStoreSelectors.selectSelectedSchedule)
    ),
      map(([allSchedules, selectedSchedule]: [Schedule[], Schedule | undefined]) => {
        return allSchedules.filter(x => (selectedSchedule == null || x.id !== selectedSchedule.id) && x.name.toLocaleLowerCase() === name.toLocaleLowerCase()).length === 0;
      })
    );
  }

  private isProjectNameExist(name: string) {
    return this.store$.select(
      ProjectsSchedulerStoreSelectors.selectProjects
    ).pipe(
      map((projects: Project[]) => {
        const foundProject = projects.find(x => (x.name.toLocaleLowerCase() === name.toLocaleLowerCase()));
        if (foundProject) {
          this.projectToReplace = foundProject;
        }
        return !!foundProject;
      })
    );
  }

  recurrenceRangeChange(value: string) {
    const validators = [];
    validators.push(Validators.required());
    const numberOfOccurrencesControl = this.formGroup.get('numberOfOccurrences');
    const endDateControl = this.formGroup.get('endDate');
    const startDateControl = this.formGroup.get('startDate');

    if (value === 'numberOfOccurences') {
      numberOfOccurrencesControl?.setValidators([Validators.required(), Validators.min(1), Validators.max(999)]);
      numberOfOccurrencesControl?.updateValueAndValidity();
      endDateControl?.clearValidators();
      endDateControl?.reset();
    } else if (value === 'endDate') {
      numberOfOccurrencesControl?.clearValidators();
      numberOfOccurrencesControl?.reset();
      const startDate = startDateControl?.value;
      if (startDate) {
        endDateControl?.setValidators([Validators.required(), Validators.minDate(new Date(startDate))]);
        endDateControl?.updateValueAndValidity();
      }
    } else {
      numberOfOccurrencesControl?.clearValidators();
      numberOfOccurrencesControl?.reset();
      endDateControl?.clearValidators();
      endDateControl?.reset();
    }
  }

  checkRegionLocationValidators() {
    const locationsFormControl = this.ticketFormGroup.get('locations');
    const regionsFormControl = this.ticketFormGroup.get('regions');

    if (locationsFormControl?.value == null || locationsFormControl?.value.length === 0) {
      locationsFormControl?.clearValidators();
      locationsFormControl?.updateValueAndValidity();
    } else if (regionsFormControl?.value == null || regionsFormControl?.value.length === 0) {
      regionsFormControl?.clearValidators();
      regionsFormControl?.updateValueAndValidity();
    } else {
      locationsFormControl?.clearValidators();
      locationsFormControl?.updateValueAndValidity();
      regionsFormControl?.clearValidators();
      regionsFormControl?.updateValueAndValidity();
    }
  }

  setRegionValidator() {
    const locationsFormControl = this.ticketFormGroup.get('locations');
    const regionsFormControl = this.ticketFormGroup.get('regions');
    regionsFormControl?.clearValidators();
    if (locationsFormControl?.value == null || locationsFormControl?.value.length === 0) {
      regionsFormControl?.setValidators(Validators.required())
    }
    regionsFormControl?.updateValueAndValidity({ onlySelf: false });
  }


  setLocationValidator() {
    const locationsFormControl = this.ticketFormGroup.get('locations');
    const regionsFormControl = this.ticketFormGroup.get('regions');
    locationsFormControl?.clearValidators();
    if (regionsFormControl?.value == null || regionsFormControl?.value.length === 0) {
      locationsFormControl?.setValidators(Validators.required())
    }
    locationsFormControl?.updateValueAndValidity({ onlySelf: false });
  }

  changeScheduleType(type: ScheduleType) {
    this.store$.dispatch(new ProjectsSchedulerStoreActions.ChangeSelectedTypeAction({ type }));
    const reccurenceControl = this.formGroup.get('reccurence');
    if (!reccurenceControl) {
      return;
    }
    if (type === ScheduleType.SingleEvent) {
      reccurenceControl.clearValidators();
      this.formGroup.patchValue({
        reccurence: null
      });
    } else {
      reccurenceControl.setValidators(reccurenceValidatorFn()); 
      this.formGroup.patchValue({
        reccurence: this.defaultReccurence
      });
    }
    this.formGroup.updateValueAndValidity();
  }

  existingProjectSectionToggle(value: boolean) {
    const existingProjectControl = this.formGroup.get('project');
    if (!existingProjectControl) {
      return;
    }
    if (value) {
      existingProjectControl.setValidators(Validators.required());
      this.createTicketSectionToggle(true);
    } else {
      existingProjectControl.clearValidators();
      existingProjectControl.updateValueAndValidity({onlySelf: true});
      this.repositoryId = Object.guid();
    }
    this.existingProjectSelected = value;
    this.projectFormUpdateValidators();
  }

  projectFormUpdateValidators() {
    if (!this.existingProjectSelected) {
      Object.keys(this.projectFormGroup.controls).forEach(key => {
        const validators = [];
        let excludeFromRequired = ["id","closeExistingProject","isBillable","files","purchaseOrder", "externalReference", "payroll" ];
        if (!excludeFromRequired.includes(key)) {
          validators.push(Validators.required());
          if (key === 'name' || key === 'description') {
            validators.push(Validators.emoji())
          }
          this.projectFormGroup.controls[key].setValidators(validators);
          this.projectFormGroup.controls[key].updateValueAndValidity();
        }
       
      });
    } else {
      Object.keys(this.projectFormGroup.controls).forEach(key => {
        this.projectFormGroup.controls[key].clearValidators();
        this.projectFormGroup.controls[key].updateValueAndValidity();
      });
    }
  }

  createTicketSectionToggle(value: boolean) {
    if (value) {
      this.createTicketSelected = value;
    } else {
      this.createTicketSelected = !this.createTicketSelected;
    }
   
    if (this.createTicketSelected) {
      Object.keys(this.ticketFormGroup.controls).forEach(key => {
        if (key !== 'id' && key !== 'users' && key !== "contractPrice") {
          this.ticketFormGroup.get(key)?.setValidators(Validators.required());
          if (key === 'description') {
            this.ticketFormGroup.get(key)?.setValidators(Validators.emoji());
          }
          this.ticketFormGroup.get(key)?.updateValueAndValidity();
        }
      });
    } else {
      Object.keys(this.ticketFormGroup.controls).forEach(key => {
        this.ticketFormGroup.get(key)?.clearValidators();
        this.ticketFormGroup.get(key)?.updateValueAndValidity();
      });
    }
    this.checkRegionLocationValidators();
  }

  valueMapper(option: any) {
    return option;
  }

  compareFn(c1: any, c2: any): boolean {
    return c1 && c2 ? (c1.id === c2.id) : c1 === c2
  }

  usersValueChanged(value: any[]) {
    if (value && value.length === 0) {
      this.ticketFormGroup.patchValue({
        users: null
      });
    }
    setTimeout(() => this.users = value ? value : []);
  }

  updateValidations(project: Project) {
     this.formGroup.updateValueAndValidity();
  }

  canDeactivate() {
    const navigation = this.router.getCurrentNavigation();
    const shouldDeactivate = navigation?.extras?.state?.['shouldDeactivate'];
    if (shouldDeactivate === false || this.formGroup.pristine) {
      return true;
    }
    return this.checkIsFormModified();
  }

  /**
   * Triggered on any unload browser event (refresh, back, exit, tenant change, sign out) if there are any unsaved changes on form
   */
  @HostListener('window:beforeunload')
  canUnloadPage(): boolean {
    let returnValue = false;
    this.checkIsFormModified().subscribe(x => returnValue = x);
    return returnValue;
  }

  /**
   * Checks if there was any changes on the form
   */
  checkIsFormModified() {
    return this.store$.select(ProjectsSchedulerStoreSelectors.selectSelectedSchedule).pipe(map((schedule) => {
      if (schedule != null) {
        return Object.equal(this.formGroup.value, schedule, ['id', 'reccurence', 'tenantId']);
      } else {
        const emptySchedule = new Schedule('', '', new Date(), new Date(), '', true, 0, 0, 0, {} as ProjectItem, {} as TicketItem, false, false, []);
        return Object.equal(this.formGroup.value, emptySchedule, ['id', 'reccurence', 'tenantId']);
      }
    }),
      take(1)
    );
  }

  ngOnDestroy() {
    this.subscriptions.forEach((subscription: Subscription) => {
      subscription.unsubscribe();
    })
  }

  setTicketType() {
    this.ticketTypes$.subscribe(ticketTypes => {
      if(ticketTypes){
        var ticketType = ticketTypes[0];
        if(ticketType){
          this.selectedTicketType = ticketType;
          const typeControl = this.ticketFormGroup.get('type');
          if (typeControl) {
            typeControl.setValue(ticketType);
          }
        }
      }
    });
  }

  onProjectChange() {
    const startDateControl = this.formGroup.get('startDate');
    const endDateControl = this.formGroup.get('endDate');
    if (startDateControl) {
      startDateControl.reset();
    }
    if (endDateControl) {
      endDateControl.reset();
    }
    this.setTicketType();
  }

  setLocationData() {
    const locationData = this.ticketFormGroup.controls['locations'].value;
    if (locationData && locationData.length) {
      const locations = locationData.filter((x: any) => x.areaType === AreaTypeEnum.Location);
      const regions = locationData.filter((x: any) => x.areaType === AreaTypeEnum.Region);

      this.ticketFormGroup.controls['locations'].setValue(locations);
      this.ticketFormGroup.controls['regions'].setValue(regions);
    }
    this.setLocationValidator();
    this.setRegionValidator();
  }

  showRegions() {
    const locations = this.ticketFormGroup.controls['locations'].value;
    const regions = this.ticketFormGroup.controls['regions'].value;
    this.ticketFormGroup.controls['locations'].setValue(locations ? locations.concat(regions) : regions);
  }

  markFormTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      formGroup.controls[key].markAsTouched();
    });
  }

  recurrenceTabChange(tab: string) {
    this.isDurationInMonthsFieldVisible = ['monthly', 'yearly'].includes(tab);
  }

  openPMManageModal() {
    var locations = this.getLocations();
    var regions = this.getRegions();
    var isEdit = false;
    var list = null;

    this.selectedSchedule$.subscribe(selectedSchedule => {
      if (selectedSchedule != null) {
        isEdit = true;
        list = selectedSchedule && selectedSchedule.locationTypesChecklists && selectedSchedule.locationTypesChecklists.length 
        && selectedSchedule.locationTypesChecklists.filter(x => x.checklist && x.checklist.id);
      }
    });

    const dialogRef = this.dialog.open(LocationTypesChecklistsManageComponent, {
      data: { regions, locations, isEdit, list }
    });

    dialogRef.afterClosed().subscribe(data => {
      if (data) {
        this.locationTypesChecklistsItems = data;
        this.store$.dispatch(new LocationStoreActions.SetLocationTypeChecklistConfigAction({locationTypesChecklists: data}));
      }
    });
  }

  showPMManage() {
    const typeControl = this.ticketFormGroup.controls['type'];
    return this.ticketFormGroup.controls['locations'].value &&
      typeControl && typeControl.value && typeControl.value.category === TicketTypeCategory.PM;
  }

  isLocationTypesChecklistsValid(locationTypesChecklistsItems: LocationTypeChecklist[]) {
    const typeControl = this.ticketFormGroup.controls['type'];
    return typeControl &&
      typeControl.value &&
      ((typeControl.value.category === TicketTypeCategory.PM &&
        (locationTypesChecklistsItems.length > 0) || this.isEdit) || typeControl.value.category != TicketTypeCategory.PM);
  }

  onLocationDataChange(newValue: any) {
    this.locationTypesChecklistsItems = [];
    this.store$.dispatch(new LocationStoreActions.SetLocationTypeChecklistConfigAction({locationTypesChecklists: []}));
  }

  getRegions() {
    const locationData = this.ticketFormGroup.get('locations')?.value;
    return locationData && locationData.length ? locationData.filter((x: any) => x.areaType === AreaTypeEnum.Region) : null;
  }

  getLocations() {
    const locationData = this.ticketFormGroup.get('locations')?.value ? 
      this.ticketFormGroup.get('locations')?.value.length ? 
        this.ticketFormGroup.get('locations')?.value : 
        [this.ticketFormGroup.get('locations')?.value] 
      : null;

    return locationData && locationData.length ? locationData.filter((x: any) => x.areaType === AreaTypeEnum.Location) : null;
  }

  isPMTaskExistingProject() {
    const checkTypeIsPm = this.ticketFormGroup.get('type')?.value;
    return checkTypeIsPm && checkTypeIsPm.category && checkTypeIsPm.category === TicketTypeCategory.PM;
  }

  isPMTask() {
    const checkContract = this.projectFormGroup.controls['contract'];
    const checkTypeIsPm = this.projectFormGroup.controls['type'];
    return checkContract && checkTypeIsPm && checkContract.value && checkTypeIsPm.value && checkTypeIsPm.value.category
      && checkTypeIsPm.value.category === TicketTypeCategory.PM;
  }

  ticketItemValueMapper(option: any) {
    return option;
  }

  ticketItemCompareFn(c1: any, c2: any): boolean {
    return c1 && c2 ? (c1.id === c2.id) : c1 === c2
  }

 }
